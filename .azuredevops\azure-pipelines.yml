trigger:
    - main
    - desenvolvimento

pool: Padrão

variables:
  - group: Shared
  - name : solution
    value: '**/*.sln'
  - name : buildPlatform
    value: 'Any CPU'
  - name : buildConfiguration
    value: 'Release'
  - name : JAVA_HOME
    value: 'D:\Ferramentas\scoop\apps\zulu17-jdk\current'
  - name : coverageReportPath
    value: '$(Build.SourcesDirectory)\coverage.xml'               # Para SonarQube
  - name : coberturaReportPath
    value: '$(Build.SourcesDirectory)\Cobertura\Cobertura.xml'   # Para Azure DevOps
  - name : PackageLocation
    value: '$(Build.ArtifactStagingDirectory)\SISPREC'

steps:

# Configura JAVA_HOME e PATH
- script: |
    echo Configurando JAVA_HOME e PATH
    set JAVA_HOME=D:\Ferramentas\scoop\apps\zulu17-jdk\current
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo %JAVA_HOME%
    echo %PATH%
  displayName: 'Configurar JAVA_HOME e PATH'

# Instalar NuGet
- task: NuGetToolInstaller@1
  displayName: 'Instalar NuGet'

# Obter ID do Pull Request
- powershell: |
    # Defina as variáveis
    $collectionUri = "$(System.TeamFoundationCollectionUri)"
    $project = "$(System.TeamProject)"
    $repoName = "$(Build.Repository.Name)"
    $commitId = "$(Build.SourceVersion)"
    
    # Codifique o token de acesso pessoal (PAT). Como estamos executando na pipeline, podemos usar o System.AccessToken
    $personalAccessToken = "$(System.AccessToken)"
    $basicAuthHeader = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$personalAccessToken"))
    
    # Construa a URL da API REST
    $uri = "$collectionUri$project/_apis/git/repositories/$repoName/pullRequests?searchCriteria.status=completed&searchCriteria.mergeCommitId=$commitId&api-version=6.0"
    
    # Faça a chamada à API REST
    try {
        $response = Invoke-RestMethod -Uri $uri -Headers @{Authorization = "Basic $basicAuthHeader"}
    
        # Imprima a resposta completa da API
        Write-Host "Resposta da API:"
        Write-Host ($response | ConvertTo-Json -Depth 100)
    
        # Obtenha o ID do Pull Request
        if ($response.count -gt 0) {
            $prId = $response.value[0].pullRequestId
            Write-Host "ID do Pull Request: $prId"
            # Defina como uma variável de pipeline
            Write-Host "##vso[task.setvariable variable=PullRequestId]PR_$prId"
        } else {
            Write-Host "Nenhum Pull Request encontrado para o commit $commitId"
        }
    } catch {
        Write-Host "Erro ao chamar a API REST:"
        Write-Host $_.Exception.Message
    }
  displayName: 'Obter ID do Pull Request'
  env:
    SYSTEM_ACCESSTOKEN: $(System.AccessToken)

# Preparar SonarQube para TRF3.SISPREC-PR-Dev
- task: SonarQubePrepare@5
  inputs:
    SonarQube: 'sonarqube-connection'
    scannerMode: 'MSBuild'
    projectKey: 'TRF3.SISPREC-PR-Dev'
    projectName: 'TRF3.SISPREC-PR-Dev'
    extraProperties: |
      sonar.cs.vscoveragexml.reportsPaths=$(coverageReportPath)
      sonar.cs.vstest.reportsPaths="**/TestResults/**/*.trx"
      sonar.cpd.exclusions="**/Migrations/**/*","**/TRF3.SISPREC.Domain/SincronizacaoLegado/**/*","**/TRF3.SISPREC.EntityFrameworkCore/EntityFrameworkCore/**/*","**/*.js","**/src/TRF3.SISPREC.Web/Pages/**/*ViewModel.cs","**/Dtos/**/*.cs", "**/PrevencaoBaseQueryResult.cs","**/src/TRF3.SISPREC.Web/Pages/**/*FilterInput.cs"
      sonar.coverage.exclusions="**/Migrations/**/*","**/TRF3.SISPREC.EntityFrameworkCore/EntityFrameworkCore/**/*","**/TRF3.SISPREC.Domain/SincronizacaoLegado/**/*","**/*.js"
      sonar.exclusions=**/TRF3.SISPREC.Infraestrutura/PDFServices/Templates/**,**/TRF3.SISPREC.Web/wwwroot/**/summernote/**,**/TRF3.SISPREC.EntityFrameworkCore/Migrations/**
      sonar.verbose=true
      sonar.buildString="$(PullRequestId), $(Build.BuildNumber)"
      sonar.projectVersion=$(PullRequestId)
  displayName: 'Preparar SonarQube para análise'

# Restaura os nugets
- task: NuGetCommand@2
  displayName: 'NuGet restore da solution TRF3.SISPREC.sln'
  inputs:
    command: 'restore'
    restoreSolution: '$(solution)'
    feedsToUse: 'config'

# Task para instalar libs frontend
- task: PowerShell@2
  inputs:
    targetType: 'inline'
    script: |
       # Verificar se ABP CLI está instalado
       if (-not (Get-Command "abp" -ErrorAction SilentlyContinue)) {
           Write-Host "ABP CLI não está instalado. Instale com 'dotnet tool install -g Volo.Abp.Cli'"
           exit 1
       }
       # Navegar até o diretório do código-fonte e instalar libs
       cd $(Build.SourcesDirectory)
       abp install-libs
    pwsh: true
  displayName: 'abp install-libs'

# Compilar TRF3.SISPREC.sln usando DotNetCoreCLI
- task: DotNetCoreCLI@2
  displayName: 'Compilar TRF3.SISPREC.sln usando dotnet build'
  inputs:
    command: 'build'
    projects: '$(solution)'
    arguments: '--nr:false --configuration $(buildConfiguration) /p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfiguration=true /p:PackageLocation=$(PackageLocation) /p:PackageOnPublish=true'

# Executar testes e coletar cobertura e resultados
- task: PowerShell@2
  displayName: 'Executar testes e coletar cobertura e resultados'
  inputs:
    targetType: 'inline'
    script: |
      $result = & C:\Windows\ServiceProfiles\NetworkService\.dotnet\tools\dotnet-coverage.exe collect "dotnet test --no-build --configuration $(buildConfiguration) --logger trx" -f xml -o "$(coverageReportPath)"
      if ($LASTEXITCODE -ne 0) {
          Write-Host "Os testes falharam."
          # Salvar o código de saída para usar mais tarde
          Write-Host "##vso[task.setvariable variable=testsFailed]true"
      } else {
          Write-Host "Os testes passaram."
          Write-Host "##vso[task.setvariable variable=testsFailed]false"
      }
    pwsh: true
  continueOnError: true  # Permite que o pipeline continue mesmo se os testes falharem

# Gerar relatório Cobertura usando ReportGenerator
- script: |
    reportgenerator -reports:"$(coverageReportPath)" -targetdir:"$(Build.SourcesDirectory)\Cobertura" -reporttypes:"Cobertura"
  displayName: 'Gerar relatório Cobertura usando ReportGenerator'
  env:
    PATH: $(PATH);%USERPROFILE%\.dotnet\tools

# Publicar resultados dos testes
- task: PublishTestResults@2
  displayName: 'Publicar resultados dos testes'
  inputs:
    testResultsFormat: 'VSTest'
    testResultsFiles: '**/TestResults/**/*.trx'
    searchFolder: '$(Build.SourcesDirectory)'

# Publicar resultados de cobertura de código no Azure DevOps
- task: PublishCodeCoverageResults@1
  displayName: 'Publicar resultados de cobertura de código'
  inputs:
    codeCoverageTool: 'Cobertura'
    summaryFileLocation: '$(coberturaReportPath)'
    reportDirectory: '$(Build.SourcesDirectory)\Cobertura'

# Verificar resultado dos testes e falhar se necessário
- task: PowerShell@2
  displayName: 'Verificar resultado dos testes e falhar se necessário'
  inputs:
    targetType: 'inline'
    script: |
      if ($env:testsFailed -eq "true") {
          Write-Host "Falhando o pipeline devido a falha nos testes."
          exit 1  # Falha explicitamente o pipeline
      }
  condition: always()  # Esta etapa sempre será executada

# Incluir Node.js no PATH (se necessário)
- script: |
    echo Incluindo Node.js no PATH
    set PATH=C:\Program Files\nodejs;%PATH%
    echo node -v
    echo PATH: %PATH%
  displayName: 'Incluir Node.js no PATH'

# Analisar com SonarQube
- task: SonarQubeAnalyze@5
  displayName: 'Analisar com SonarQube'

# Publicar resultados do SonarQube
- task: SonarQubePublish@5
  displayName: 'Publicar resultados do SonarQube'
  inputs:
    pollingTimeoutSec: '300'

#Removido pois com o puglin do SonarQube, agora a validação está sendo feita pelo Status Check da policy da branch de desenvolvimento
# Verificar o status do Quality Gate do SonarQube
# - task: PowerShell@2
#   displayName: 'Verificar o status do Quality Gate do SonarQube'
#   inputs:
#     targetType: 'inline'
#     script: |
#       $url = "https://sonarqube.trf3.jus.br/api/qualitygates/project_status?projectKey=TRF3.SISPREC-PR-Dev&branch=desenvolvimento"
#       $base64Token = "$(ApiKeySonar)"
#       $headers = @{
#         Authorization = "Basic $base64Token"
#       }
#       $response = Invoke-RestMethod -Uri $url -Method Get -Headers $headers
#       $status = $response.projectStatus.status
#       if ($status -eq 'OK') {
#         Write-Host "Status do Quality Gate: Aprovado"
#       } else {
#         Write-Host "Status do Quality Gate: Reprovado"
#       }
#       foreach ($condition in $response.projectStatus.conditions) {
#         Write-Host "--Métrica: $($condition.metricKey)------------------- "
#         Write-Host "     Status:       $($condition.status)"
#         Write-Host "     Valor Mínimo: $($condition.errorThreshold)"
#         Write-Host "     Valor Atual:  $($condition.actualValue)"
#         Write-Host "----------------------------------------------------- "
#       }
#       if ($status -ne 'OK') {
#         Write-Host "O Quality Gate falhou"
#         exit 1
#       }

# Publicar os artefatos no Azure DevOps
- task: PublishBuildArtifacts@1
  condition : ${{or( eq(variables['Build.SourceBranchName'], 'main'), eq(variables['Build.SourceBranchName'], 'desenvolvimento') )}}
  displayName: 'Publicar os artefatos no Azure DevOps'
  inputs:
    PathtoPublish: '$(PackageLocation)'
    ArtifactName: '$(Build.BuildNumber)'
    publishLocation: 'Container'