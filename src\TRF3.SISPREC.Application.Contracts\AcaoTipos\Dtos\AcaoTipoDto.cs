using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.AcaoTipos.Dtos
{
    [Serializable]
    [ExcludeFromCodeCoverage]
    public class AcaoTipoDto : EntityDto
    {
        [Display(Name = "Id")]
        public int AcaoTipoId { get; set; }

        [Display(Name = "Código")]
        public ECodigoAcaoTipo Codigo { get; set; }

        [Display(Name = "Descrição")]
        public string? Descricao { get; set; }
    }
}
