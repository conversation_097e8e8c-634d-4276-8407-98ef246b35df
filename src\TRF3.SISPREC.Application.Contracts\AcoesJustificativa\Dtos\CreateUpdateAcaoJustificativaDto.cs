using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.AcoesJustificativa.Dtos
{
    [ExcludeFromCodeCoverage]
    public class CreateUpdateAcaoJustificativaDto
    {
        private string _descricao;

        [Display(Name = "Descricao")]
        public string Descricao
        {
            get => _descricao;
            set => _descricao = value?.Trim();
        }

        [Display(Name = "Ação Tipo")]
        public int AcaoTipoId { get; set; }

        [Display(Name = "Ação")]
        public AcaoJustificativaDto Acao { get; set; }

        [Display(Name = "Ativo")]
        public bool Ativo { get; set; }
    }
}
