using TRF3.SISPREC.AcoesJustificativa.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.AcoesJustificativa
{
    public interface IAcaoJustificativaAppService : ICrudAppService<AcaoJustificativaDto, int, AcaoJustificativaGetListInput, CreateUpdateAcaoJustificativaDto, CreateUpdateAcaoJustificativaDto>
    {
        Task<IEnumerable<AcaoJustificativaSelectDto>> ListarAcaoJustificativaAsync(string motivo);
    }
}
