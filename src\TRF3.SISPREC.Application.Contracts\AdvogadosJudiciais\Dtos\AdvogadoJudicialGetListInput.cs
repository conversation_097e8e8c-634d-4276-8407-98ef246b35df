using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.AdvogadosJudiciais.Dtos;

[Serializable]
public class AdvogadoJudicialGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "ID Advogado")]
    public int? AdvogadoJudicialId { get; set; }

    [Display(Name = "Nome")]
    public string? Nome { get; set; }

    [Display(Name = "Nome Social")]
    public string? NomeSocial { get; set; }

    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    [Display(Name = "Código OAB")]
    public string? CodigoOab { get; set; }

    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

}