using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.AdvogadosJudiciais.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdateAdvogadoJudicialDto : EntityDto
{
    [Display(Name = "Nome")]
    [StringLength(AdvogadoJudicialConsts.NOME_MAX_LENGTH, ErrorMessage = "O nome deve ter no máximo {1} caracteres")]
    public string Nome { get; set; }

    [Display(Name = "Nome Social")]
    [StringLength(AdvogadoJudicialConsts.NOME_SOCIAL_MAX_LENGTH, ErrorMessage = "O nome social deve ter no máximo {1} caracteres")]
    public string? NomeSocial { get; set; }

    [Display(Name = "CPF")]
    [StringLength(AdvogadoJudicialConsts.CPF_MAX_LENGTH, ErrorMessage = "O CPF deve ter no máximo {1} caracteres")]
    public string? Cpf { get; set; }

    [Display(Name = "Código OAB")]
    [StringLength(AdvogadoJudicialConsts.COD_OAB_MAX_LENGTH, ErrorMessage = "O código oab deve ter no máximo {1} caracteres")]
    public string? CodigoOab { get; set; }

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }

}