using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Bancos.Dtos;
using TRF3.SISPREC.Enderecos.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Agencias.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class AgenciaDto : EntityDto
{
    [Display(Name = "Número da Agencia")]
    public int AgenciaId { get; set; } = 0;

    [Display(Name = "Cod. Dígito Verificador")]
    public string CodDigitoVerifi { get; set; } = string.Empty;

    [Display(Name = "Nome da Agencia")]
    public string NomeAgencia { get; set; } = string.Empty;

    [Display(Name = "Banco")]
    public BancoDto Banco { get; set; } = new BancoDto();

    [Display(Name = "Municipio")]
    public MunicipioDto Municipio { get; set; } = new MunicipioDto();

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }
}