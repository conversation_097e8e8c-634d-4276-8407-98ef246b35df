using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Agencias.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class AgenciaGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Número da Agencia")]
    public int? AgenciaId { get; set; }

    //[Display(Name = "Cod. Digito Verificador")]
    //public string? CodDigitoVerifi { get; set; }

    [Display(Name = "Nome da Agencia")]
    public string? NomeAgencia { get; set; }

    [Display(Name = "Banco")]
    public int? BancoId { get; set; }

    [Display(Name = "Municipio")]
    public int? MunicipioId { get; set; }

    [Display(Name = "Ativo")]
    public string? Ativo { get; set; }
}