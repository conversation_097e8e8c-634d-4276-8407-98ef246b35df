using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Agencias.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateAgenciaDto : EntityDto
{
    [Display(Name = "Número da Agência")]
    public int AgenciaId { get; set; } = 0;

    [Display(Name = "Nome da Agencia")]
    [StringLength(AgenciaConsts.NOME_MAX_LENGTH, ErrorMessage = "O nome da agência deve ter até {1} caracteres")]
    public string NomeAgencia { get; set; } = string.Empty;

    [Display(Name = "Cod. Digito Verificador")]
    [StringLength(AgenciaConsts.DIGITO_VERIFICADOR_MAX_LENGTH, ErrorMessage = "O dígito verificador deve ter até {1} caracteres")]
    public string CodDigitoVerifi { get; set; } = string.Empty;

    [Display(Name = "Banco")]
    public int BancoId { get; set; } = 0;

    [Display(Name = "Municipio")]
    public int MunicipioId { get; set; } = 0;
}