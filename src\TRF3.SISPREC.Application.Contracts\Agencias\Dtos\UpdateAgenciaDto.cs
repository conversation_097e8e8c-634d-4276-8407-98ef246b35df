using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Agencias.Dtos
{
    [Serializable]
    [ExcludeFromCodeCoverage]
    public class UpdateAgenciaDto : EntityDto
    {
        [Display(Name = "Nome da Agencia")]
        [StringLength(AgenciaConsts.NOME_MAX_LENGTH, ErrorMessage = "O nome da agência deve ter até {1} caracteres")]
        public string NomeAgencia { get; set; } = string.Empty;

        [Display(Name = "Ativo")]
        public bool Ativo { get; set; }
    }
}
