using TRF3.SISPREC.AnaliseCpfCnpj.Dtos;
using TRF3.SISPREC.Analises.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.AnaliseCpfCnpj;

public interface IAnaliseCpfCnpjAppService : IApplicationService
{
    Task<PagedResultDto<RequisicaoAnaliseCpfCnpjDto>> GetRequisicoes(AnaliseGetListInput input);
    Task<PagedResultDto<RequisicaoParteAnaliseCpfCnpj>> GetPartes(string numeroProtocoloRequisicao);
    Task<DadosRequisicaoCpfCnpjDto> GetDados(string numeroProtocoloRequisicao, string numeroCpfCnpj);
    bool CompararNomes(string? nomeEntrada, string? nomeSaida);
}
