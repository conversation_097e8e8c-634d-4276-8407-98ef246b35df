using System.ComponentModel;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.AnaliseNomesPartes.Dtos;

[Serializable]
public class AnaliseNomesParteGetListInput : PagedAndSortedResultRequestDto
{
    [DisplayName("Procedimento")]
    public ETipoProcedimentoRequisicao TipoProcedimento { get; set; }

    [DisplayName("Ano")]
    public int AnoPropos { get; set; }

    [DisplayName("Mês")]
    public int MesPropos { get; set; }

    [DisplayName("Lida")]
    public bool? Lida { get; set; }

    [DisplayName("Total")]
    public int Total { get; set; }
}
