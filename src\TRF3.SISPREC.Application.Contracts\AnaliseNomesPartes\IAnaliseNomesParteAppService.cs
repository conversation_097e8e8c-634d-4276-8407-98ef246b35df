using TRF3.SISPREC.AnaliseNomesPartes.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.AnaliseNomesPartes;
public interface IAnaliseNomesParteAppService : IApplicationService
{
    Task<PagedResultDto<AnaliseNomesParte>> GetNomesPartesDivergentes(AnaliseNomesParteGetListInput input);
    Task<byte[]> ExportarExcel(AnaliseNomesParteGetListInput input);
    Task AlternarVisualizacaoDivergencia(string cpfCnpj, int anoPropos, int mesPropos);
}
