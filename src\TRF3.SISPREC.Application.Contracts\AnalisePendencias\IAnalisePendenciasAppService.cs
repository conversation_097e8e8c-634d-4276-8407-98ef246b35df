using TRF3.SISPREC.Analises.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.AnalisePendencias;

public interface IAnalisePendenciasAppService : IApplicationService
{
    Task<PagedResultDto<RequisicoesPendentes>> BuscarRequisicoesAnalisesPendentes(AnaliseGetListInput analisePendenciasGetList);
    Task<PagedResultDto<RequisicoesPendentes>> BuscarRequisicoesParaComparacaoPorCpf(string? numeroProtocoloRequisicao);
    Task<PagedResultDto<RequisicoesPendentes>> BuscarRequisicoesParaComparacaoPorOriginario(string? numeroProtocoloRequisicao);
    Task<PagedResultDto<OcorrenciasAnalise>> BuscaOcorrenciasAnalise(string? numeroProtocoloRequisicao);
}
