using TRF3.SISPREC.AnaliseReinclusoes.Dto;
using TRF3.SISPREC.Analises.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.AnaliseReinclusoes;

public interface IAnaliseReinclusaoAppService : IApplicationService
{
    Task<PagedResultDto<AnaliseReinclusao>> ObterAnaliseReinclusao(AnaliseGetListInput input);
    Task<EstornoRequisicaoDto> GetEstornoRequisicao(string? numeroProtocoloRequisicao, string? numeroProtocoloRequisicaoOriginal);
}