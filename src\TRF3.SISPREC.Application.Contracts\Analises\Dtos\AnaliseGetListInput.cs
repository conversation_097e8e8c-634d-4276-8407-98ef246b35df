using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Analises.Dtos;

public class AnaliseGetListInput : PagedAndSortedResultRequestDto, IValidatableObject
{
    [Display(Name = "Procedimento")]
    public ETipoProcedimentoRequisicao? TipoProcedimento { get; set; }

    [Display(Name = "Ano")]
    public int? Ano { get; set; }

    [Display(Name = "Mês")]
    public int? Mes { get; set; }

    [Display(Name = "Data Início")]
    public DateTime? DataInicio { get; set; }

    [Display(Name = "Data Término")]
    public DateTime? DataTermino { get; set; }

    [Display(Name = "Requisição")]
    public string? NumeroProtocoloRequisicao { get; set; }

    IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
    {
        if ((!TipoProcedimento.HasValue || !Ano.HasValue || !Mes.HasValue) && NumeroProtocoloRequisicao.IsNullOrEmpty())
        {
            yield return new ValidationResult("Preencha os campos (Procedimento, Ano e Mês) ou (Requisição).");
        }
        else if (DataInicio.HasValue && DataTermino.HasValue && DataInicio > DataTermino)
        {
            yield return new ValidationResult("Data Início deve ser menor que Data Término", [nameof(DataInicio), nameof(DataTermino)]);
        }
    }
}
