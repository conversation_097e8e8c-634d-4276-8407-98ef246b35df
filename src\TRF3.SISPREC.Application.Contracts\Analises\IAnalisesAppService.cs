using TRF3.SISPREC.AnalisePendencias;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.Analises
{
    public interface IAnalisesAppService : IApplicationService
    {
        Task<List<RequisicoesAnalise>> CompararRequisicoes(string numeroProtocoloRequisicao);
        Task<List<string>> BuscaOriginariosAnalise(string numeroProtocoloRequisicao);
        Task<List<string>> BuscaExpedientesAnalise(string numeroProtocoloRequisicao);
        Task<List<string>> BuscaContratuaisAnalise(string numeroProtocoloRequisicao);
        Task<List<string>> BuscaSemelhantesAnalise(string numeroProtocoloRequisicao);
        Task<List<string>> BuscaReferenciasAnalise(string numeroProtocoloRequisicao);
    }
}
