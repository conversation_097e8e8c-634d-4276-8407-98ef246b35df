using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Assuntos.Dtos;

[ExcludeFromCodeCoverage]
[Serializable]
public class AssuntoAuxiliarDto : EntityDto
{
    [DisplayName("Ind. Desapr.")]
    public bool? IndicadorDesapropriacao { get; set; }

    [DisplayName("Ind. Exec. Fiscal")]
    public bool? IndicadorExecucaoFiscal { get; set; }

    [DisplayName("Ind. Obr. PSS")]
    public bool? IndicadorObrigacaoPSS { get; set; }

    [DisplayName("Ind. RRA")]
    public bool? IndicadorRRA { get; set; }

    [DisplayName("Ind. Tributário")]
    public bool? IndicadorTributario { get; set; }
}