using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Assuntos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class AssuntoDespesaDto : EntityDto
{
    [HiddenInput]
    public int DespesaClassificacaoId { get; set; } = 0;

    [HiddenInput]
    public int AssuntoId { get; set; } = 0;

    [Display(Name = "Classificação de Despesa")]
    public string DescricaoDespesaClassificacao { get; set; } = string.Empty;
}