using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Assuntos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class AssuntoDto : BaseEntidadeDominioDto
{
    [Display(Name = "Id")]
    public int Seq_Assunt { get; set; } = 0;

    [Display(Name = "Cód. CJF")]
    public string CodigoCJF { get; set; } = string.Empty;

    [Display(Name = "Desc. CJF")]
    public string DescricaoCJF { get; set; } = string.Empty;

    [Display(Name = "Cód. CNJ")]
    public string CodigoCNJ { get; set; } = string.Empty;

    [Display(Name = "Desc. CNJ")]
    public string DescricaoCNJ { get; set; } = string.Empty;

    [Display(Name = "Dt. Cadastro")]
    public DateTime? DataCadastro { get; set; }

    [Display(Name = "Dt. Atualizacao")]
    public DateTime? DataAtualizacao { get; set; }

    [Display(Name = "Sequencial CJF")]
    public int? Seq_CJF { get; set; }

    public AssuntoAuxiliarDto AssuntoAuxiliarDto { get; set; } = new AssuntoAuxiliarDto();

    [Display(Name = "Classificações de Despesa")]
    public List<AssuntoDespesaDto> AssuntoDespesaDtos { get; set; } = new();
}