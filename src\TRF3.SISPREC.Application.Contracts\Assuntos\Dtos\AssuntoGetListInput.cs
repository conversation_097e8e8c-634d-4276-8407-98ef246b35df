using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Assuntos.Dtos;

[ExcludeFromCodeCoverage]
[Serializable]
public class AssuntoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Id")]
    public int? Seq_Assunt { get; set; }

    [Display(Name = "Cód. CJF")]
    public string? CodigoCJF { get; set; }

    [Display(Name = "Desc. CJF")]
    public string? DescricaoCJF { get; set; }

    [Display(Name = "Cód. CNJ")]
    public string? CodigoCNJ { get; set; }

    [Display(Name = "Desc. CNJ")]
    public string? DescricaoCNJ { get; set; }

    [Display(Name = "Dt. Cadastro")]
    public DateTime? DataCadastro { get; set; }

    [Display(Name = "Dt. Atualizacao")]
    public DateTime? DataAtualizacao { get; set; }

    [Display(Name = "Sequencial CJF")]
    public int? Seq_CJF { get; set; }

    [Display(Name = "Data Utilização Fim")]
    public DateTime? DataUtilizacaoFim { get; set; }

    [Display(Name = "Ativo")]
    public bool? Ativo { get; set; }

    [Display(Name = "Sincronizado CJF")]
    public ESimNao? FoiSincronizadoCjf { get; set; }
}