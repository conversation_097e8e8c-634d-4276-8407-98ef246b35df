using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Assuntos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdateAssuntoDespesaDto : EntityDto
{
    [Display(Name = "Assunto")]
    public int AssuntoId { get; set; } = 0;

    [Display(Name = "Classificação de Despesa")]
    public int DespesaClassificacaoId { get; set; } = 0;

}