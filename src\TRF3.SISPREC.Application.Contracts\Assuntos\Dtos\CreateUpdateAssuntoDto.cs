using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Assuntos.Dtos;

[ExcludeFromCodeCoverage]
[Serializable]
public class CreateUpdateAssuntoDto
{
    [Display(Name = "Cód. CJF")]
    public string CodigoCJF { get; set; } = string.Empty;

    [Display(Name = "Desc. CJF")]
    public string DescricaoCJF { get; set; } = string.Empty;

    [Display(Name = "Cód. CNJ")]
    public string CodigoCNJ { get; set; } = string.Empty;

    [Display(Name = "Desc. CNJ")]
    public string DescricaoCNJ { get; set; } = string.Empty;

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }

    public AssuntoAuxiliarDto AssuntoAuxiliarDto { get; set; } = new AssuntoAuxiliarDto();

    [Display(Name = "Classificações de Despesa")]
    public List<AssuntoDespesaDto> AssuntoDespesaDtos { get; set; } = new();
}