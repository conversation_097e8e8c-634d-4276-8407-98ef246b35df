using TRF3.SISPREC.Assuntos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.Assuntos;

public interface IAssuntoAppService : ICrudAppService<AssuntoDto, int, AssuntoGetListInput, CreateUpdateAssuntoDto, CreateUpdateAssuntoDto>
{
    Task<AssuntoDto> SalvarAssuntoAuxiliarAsync(int assuntoId, AssuntoAuxiliarDto input);
    Task UpdateAssuntoDespesa(int assuntoId, IList<AssuntoDespesaDto> input);
}