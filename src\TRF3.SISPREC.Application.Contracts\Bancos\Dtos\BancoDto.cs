using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Bancos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class BancoDto
{
    [Display(Name = "Número")]
    public int BancoId { get; set; } = 0;

    [Display(Name = "Nome")]
    public string NomeBanco { get; set; } = string.Empty;

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }
}