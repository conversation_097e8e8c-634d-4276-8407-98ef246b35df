using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Bancos.Dtos;

[Serializable]
public class BancoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Número")]
    public int? BancoId { get; set; }

    [Display(Name = "Nome")]
    public string? NomeBanco { get; set; }

    [Display(Name = "Ativo")]
    public string? Ativo { get; set; }
}