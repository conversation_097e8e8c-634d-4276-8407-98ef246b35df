using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Bancos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateBancoDto : EntityDto
{
    [Display(Name = "Número")]
    public int BancoId { get; set; } = 0;

    [Display(Name = "Nome")]
    [StringLength(BancoConsts.NOME_MAX_LENGTH, ErrorMessage = "O nome do banco deve ter até {1} caracteres")]
    public string NomeBanco { get; set; } = string.Empty;
}