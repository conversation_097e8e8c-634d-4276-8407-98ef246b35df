using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.BeneficiarioTipos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class BeneficiarioTipoDto : EntityDto
{
    public int Seq_Benefi_Tipo { get; set; } = 0;

    public EBeneficiarioTipo Codigo { get; set; } = new EBeneficiarioTipo();

    public string Descricao { get; set; } = string.Empty;

    public DateTime? DataUtilizacaoFim { get; set; }

    public int? Seq_CJF { get; set; }

}