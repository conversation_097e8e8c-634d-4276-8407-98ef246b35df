using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.CodigosReceitaFederal.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CodigoReceitaFederalGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Código Receita Federal ID")]
    public int? CodigoReceitaFederalId { get; set; }

    [Display(Name = "Código Tipo Guia")]
    public string? CodigoTipoReceita { get; set; }

    [Display(Name = "Código Receita Federal")]
    public string? CodReceitaFederal { get; set; }

    [Display(Name = "Descrição Resumo")]
    public string? DescricaoResumo { get; set; }

    [Display(Name = "Descrição Título")]
    public string? DescricaoTitulo { get; set; }

}