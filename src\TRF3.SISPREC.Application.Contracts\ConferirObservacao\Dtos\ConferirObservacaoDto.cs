using System.ComponentModel;

namespace TRF3.SISPREC.ConferirObservacoes.Dtos
{
    [Serializable]
    public class ConferirObservacaoDto
    {
        [DisplayName("Procedimento")]
        public string Procedimento { get; set; } = string.Empty;

        [DisplayName("Ano")]
        public int Ano { get; set; }

        [Display<PERSON><PERSON>("Mês")]
        public int Mes { get; set; }

        [DisplayName("Total")]
        public int Total { get; set; }

        [DisplayName("Nº Requisição")]
        public string NumeroProtocolo { get; set; } = string.Empty;

        [DisplayName("Tipo Justificativa")]
        public string? TipoJustificativa { get; set; } = string.Empty;

        [DisplayName("Observação")]
        public string Observacao { get; set; } = string.Empty;
    }
}
