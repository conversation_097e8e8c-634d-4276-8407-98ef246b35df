using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ConferirObservacoes.Dtos
{
    public class ConferirObservacaoGetListInputDto : PagedAndSortedResultRequestDto, IValidatableObject
    {
        [DisplayName("Tipo de Procedimento")]
        public ETipoProcedimentoRequisicao? TipoProcedimento { get; set; }
        [DisplayName("Ano")]
        public int? Ano { get; set; }
        [DisplayName("Mês")]
        public int? Mes { get; set; }

        [Display(Name = "Requisição")]
        public string? NumeroRequisicao { get; set; }

        [DisplayName("Possui Justificativa")]
        public ESimNao? PossuiJustificativa { get; set; } = null;

        [DisplayName("Total")]
        public int? Total { get; set; }

        public List<string> OpcoesCheckbox { get; set; }

        public string? OutrosTermos { get; set; }
        public string? OcultarFiltro { get; set; }
        public string? OcultarRequisicaoFiltro { get; set; }

        IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            if ((!TipoProcedimento.HasValue || !Ano.HasValue || !Mes.HasValue) && NumeroRequisicao.IsNullOrEmpty())
            {
                yield return new ValidationResult("Preencha os campos (Procedimento, Ano e Mês) ou (Requisição).");
            }
        }
    }
}
