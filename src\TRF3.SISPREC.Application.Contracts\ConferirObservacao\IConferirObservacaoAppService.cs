using TRF3.SISPREC.ConferirObservacoes.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ConferirObservacoes
{
    public interface IConferirObservacaoAppService : IApplicationService
    {
        Task<PagedResultDto<ConferirObservacaoDto>> GetListAsync(ConferirObservacaoGetListInputDto input);
        Task<byte[]> ExportarExcel(ConferirObservacaoGetListInputDto input);
        Task<byte[]> GerarPDF(ConferirObservacaoGetListInputDto input);
    }
}