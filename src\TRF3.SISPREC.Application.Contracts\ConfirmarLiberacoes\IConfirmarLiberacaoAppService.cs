using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.ConfirmarLiberacoes.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ConfirmarLiberacoes
{
    public interface IConfirmarLiberacaoAppService : IApplicationService
    {
        Task<PagedResultDto<RequisicoesPendentes>> GetRequisicoes(ConfirmarcaoLiberacoesGetListInput input);
        Task<DadosBasicoParaConfirmarLiberacao?> GetInformacaoBasica(string numeroProtocoloRequisicao);
        Task<PagedResultDto<DadosOcorrenciasParaConfirmarLiberacao>> GetOcorrencias(string numeroProtocoloRequisicao);
        Task<PagedResultDto<JustificativasAnalises>> GetJustificastivaAnalises(string numeroProtocoloRequisicao);

    }
}
