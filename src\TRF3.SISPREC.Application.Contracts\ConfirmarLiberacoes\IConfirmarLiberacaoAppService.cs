using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.Analises.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ConfirmarLiberacoes
{
    public interface IConfirmarLiberacaoAppService : IApplicationService
    {
        Task<PagedResultDto<RequisicoesPendentes>> GetRequisicoes(AnaliseGetListInput input);
        Task<DadosBasicoParaConfirmarLiberacao?> GetInformacaoBasica(string numeroRequisicao);
        Task<PagedResultDto<DadosOcorrenciasParaConfirmarLiberacao>> GetOcorrencias(string numeroRequisicao);
        Task<PagedResultDto<JustificativasAnalises>> GetJustificastivaAnalises(string numeroRequisicao);

    }
}
