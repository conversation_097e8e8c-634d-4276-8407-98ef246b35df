using System.ComponentModel;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.ConsultaAjuizamentos.Dtos
{
    public class ConsultaAjuizamentoFiltroExcelDto
    {
        [DisplayName("Procedimento")]
        public string TipoProcedimento { get; set; }
        [DisplayName("Ano")]
        public int? Ano { get; set; }
        [DisplayName("Mês")]
        public int? Mes { get; set; }
        [DisplayName("Data Protocolo Orig. Limite")]
        public string? DataProtocoloOrigemLimite { get; set; }
        [DisplayName("Total")]
        public int Total { get; set; }


        public ConsultaAjuizamentoFiltroExcelDto()
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.RPV.ToString();
            Ano = DateTime.Now.Year == 12 ? DateTime.Now.AddYears(1).Year : DateTime.Now.Year;
            Mes = 01;
            DataProtocoloOrigemLimite = DateTime.Now.Date.ToString();
        }
    }
}
