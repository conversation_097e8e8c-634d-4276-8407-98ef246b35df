using TRF3.SISPREC.ConsultaComplementares.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ConsultaComplementares
{
    public interface IConsultaComplementaresAppService : IApplicationService
    {
        Task<PagedResultDto<ConsultaComplementar>> GetListAsync(ConsultaComplementaresGetListInputDto input);
        Task<byte[]> GerarPDF(ConsultaComplementaresGetListInputDto input);
    }
}
