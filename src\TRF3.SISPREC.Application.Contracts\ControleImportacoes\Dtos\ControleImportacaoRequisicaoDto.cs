using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Volo.Abp.Application.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.ControleImportacoes.Dtos
{
    [Serializable]
    public class ControleImportacaoRequisicaoDto : PagedAndSortedResultRequestDto
    {
        [DisplayName("ID")]
        public int ControleImportacaoRequisicaoId { get; set; }
        [DisplayName("Nº Requisição")]
        public string NumeroProtocoloRequisicao { get; set; }
        [DisplayName("Proposta ID")]
        public int PropostaId { get; set; }
        [DisplayName("Status Erro")]
        public EStatusImportacao Status { get; set; }
        [DisplayName("Descrição Erro")]
        public string Descricao { get; set; }
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Data Erro")]
        public DateTime DataErro { get; set; }

    }
}
