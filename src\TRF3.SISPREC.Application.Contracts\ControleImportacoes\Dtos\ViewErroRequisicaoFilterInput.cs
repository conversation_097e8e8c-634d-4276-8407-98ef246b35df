using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ControleImportacoes.Dtos
{
    [ExcludeFromCodeCoverage]
    [Serializable]
    public class ViewErroRequisicaoFilterInput : PagedAndSortedResultRequestDto
    {
        [Display(Name = "Nº Requisição")]
        public string? NumProtocoloRequis { get; set; }

        [Display(Name = "Descrição do Erro")]
        public string? DescricaoErro { get; set; }
    }
}
