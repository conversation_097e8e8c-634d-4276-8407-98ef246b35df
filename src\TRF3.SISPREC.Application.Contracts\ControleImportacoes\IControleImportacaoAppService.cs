using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.ControleImportacoes.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ControleImportacoes
{
    public interface IControleImportacaoAppService : IApplicationService
    {
        Task GetInserirControle(string numeroProtocolo);
        Task<PagedResultDto<ControleImportacaoRequisicaoDto>> GetObterListaErros(ViewErroRequisicaoFilterInput input);
        Task<ControleImportacaoRequisicao> UpdateAsync(string? numeroProtocoloRequisicao);
    }
}
