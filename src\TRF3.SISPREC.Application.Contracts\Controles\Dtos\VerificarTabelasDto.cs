using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Controles.Dtos
{
    /// <summary>
    /// Classe excluída da cobertura pois não há lógica de negócio, serve apenas como transferência de dados.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class VerificarTabelasDto
    {
        public string NomeTabela { get; set; } = string.Empty;
        public bool ExisteOrigem { get; set; }
        public bool ExisteDestino { get; set; }
        public int QtdLinhasOrigem { get; set; } = 0;
        public bool SeraImportado => ExisteOrigem && ExisteDestino && QtdLinhasOrigem > 0 && !(VerificacaoColunas?.Any(a => a.ExisteDestino.Equals(false)) ?? false);
        public List<VerificarColunasDto>? VerificacaoColunas { get; set; }
        public List<VerificarColunasDto>? ColunasInexistentesDestino => VerificacaoColunasToList();

        private List<VerificarColunasDto>? VerificacaoColunasToList()
        {
            return VerificacaoColunas?.Where(a => a.ExisteDestino.Equals(false)).ToList();
        }
    }
}
