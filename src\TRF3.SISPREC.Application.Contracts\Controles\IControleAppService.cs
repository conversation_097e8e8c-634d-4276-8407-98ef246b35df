using TRF3.SISPREC.Controles.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;

namespace TRF3.SISPREC.Controles
{
    public interface IControleAppService : IApplicationService
    {
        Task<string> Upload(IRemoteStreamContent streamContent, int faseId);
        Task<List<VerificarTabelasDto>> AnalisaArquivoMdbAsync(string caminhoArquivo, int faseId);
        Task ImportarMdb(ControleProcessamentoDto inputDto);
        void CancelarUpload(string caminhoArquivo);
        Task ReprocessarEtapaAtual(int controleProcessamentoId);
        Task MoverParaProximaEtapa(int controleProcessamentoId);
    }
}
