using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.DespesaClassificacoes.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdateDespesaClassificacaoDto
{
    [Display(Name = "Alimentar?")]
    public bool Alimentar { get; set; }

    [Display(Name = "")]
    public int? Seq_CJF { get; set; }

    [Display(Name = "Data Fin. Utilização")]
    public DateTime? Utilizacao_Fim_Data { get; set; }

    /// <summary>
    /// Id da natureza da despesa.
    /// </summary>
    [Display(Name = "Cod. Despesa Natureza")]
    public int DespesaNaturezaId { get; set; } = 0;

    /// <summary>
    /// Id do tipo da despesa.
    /// </summary>
    [Display(Name = "Cod. Tipo Despesa")]
    public int DespesaTipoId { get; set; } = 0;

}