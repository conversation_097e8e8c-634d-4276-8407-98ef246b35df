using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using TRF3.SISPREC.DespesaNaturezas.Dtos;
using TRF3.SISPREC.DespesaTipos.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.DespesaClassificacoes.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class DespesaClassificacaoDto : EntityDto<int>
{
    [Display(Name = "Seq. Classificação Despesa")]
    public int Seq_Classi_Despesa { get; set; } = 0;

    [Display(Name = "Alimentar?")]
    public bool Alimentar { get; set; }

    [Display(Name = "")]
    public int? Seq_CJF { get; set; }

    [Display(Name = "Data Fin. Utilização")]
    public DateTime? DataUtilizacaoFim { get; set; }

    /// <summary>
    /// Id da natureza da despesa.
    /// </summary>
    [Display(Name = "Cod. Despesa Natureza")]
    public int DespesaNaturezaId { get; set; } = 0;

    /// <summary>
    /// Id do tipo da despesa.
    /// </summary>
    [Display(Name = "Cod. Tipo Despesa")]
    public int DespesaTipoId { get; set; } = 0;

    /// <summary>
    /// Natureza da despesa. <see cref="DespesaNaturezaId"/>
    /// </summary>
    [JsonIgnore]//evitar problema de mapeamento circular - loop
    [Display(Name = "Natureza")]
    public DespesaNaturezaDto DespesaNatureza { get; set; } = new DespesaNaturezaDto();

    /// <summary>
    /// Tipo de despesa. <see cref="DespesaTipoId"/>
    /// </summary>
    [JsonIgnore]//evitar problema de mapeamento circular - loop
    [Display(Name = "Típo de Despesa")]
    public DespesaTipoDto DespesaTipo { get; set; } = new DespesaTipoDto();

    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }
}