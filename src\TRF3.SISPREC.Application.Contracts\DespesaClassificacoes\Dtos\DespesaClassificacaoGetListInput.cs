using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.DespesaClassificacoes.Dtos;

[Serializable]
public class DespesaClassificacaoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Seq. Classificação Despesa")]
    public int? Seq_Classi_Despesa { get; set; }

    [Display(Name = "Alimentar?")]
    public string? Alimentar { get; set; }

    [Display(Name = "Seq. CJF")]
    public int? Seq_CJF { get; set; }

    [Display(Name = "")]
    public DateTime? DataUtilizacaoFim { get; set; }

    /// <summary>
    /// Id da natureza da despesa.
    /// </summary>
    [Display(Name = "Cod. Natureza")]
    public int? DespesaNaturezaId { get; set; }

    /// <summary>
    /// Id do tipo da despesa.
    /// </summary>
    [Display(Name = "Cod. Tipo Despesa")]
    public int? DespesaTipoId { get; set; }

}