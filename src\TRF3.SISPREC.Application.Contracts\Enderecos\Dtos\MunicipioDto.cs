using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Enderecos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class MunicipioDto : EntityDto<int>
{
    [Display(Name = "")]
    public int MunicipioId { get; set; }

    [Display(Name = "Município")]
    public string Nome { get; set; }

    [Display(Name = "UF")]
    public string SiglaUF { get; set; }
}