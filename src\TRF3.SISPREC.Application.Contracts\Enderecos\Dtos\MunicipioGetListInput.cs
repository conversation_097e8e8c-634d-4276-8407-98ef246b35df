using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Enderecos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class MunicipioGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "")]
    public int? CidadeId { get; set; }

    [Display(Name = "Nome da Cidade")]
    public string? NomeCidade { get; set; }

    [Display(Name = "UF")]
    public string? UfId { get; set; }
}