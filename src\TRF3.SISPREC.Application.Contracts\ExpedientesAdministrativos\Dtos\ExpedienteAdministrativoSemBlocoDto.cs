using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.ExpedientesAdministrativos.Dtos
{
    [Serializable]
    [ExcludeFromCodeCoverage]
    public class ExpedienteAdministrativoSemBlocoDto
    {
        [Display(Name = "Nº Expediente")]
        public string? NumeroProcessoSei { get; set; }

        [Display(Name = "Tipo")]
        public string? TipoExpedienteAdministrativo { get; set; }

        [Display(Name = "Data")]
        public DateTime? DataExpedienteAdministrativo { get; set; }

        [Display(Name = "Usuário")]
        public string? NomeUsuario { get; set; }

        [Display(Name = "Motivo")]
        public string? ObservacaoExpedienteAdministrativo { get; set; }

        [Display(Name = "Status")]
        public string? StatusExpedienteAdminstrativo { get; set; }

        [Display(Name = "Nº Bloco")]
        public int? BlocoSisprecId { get; set; }
        public int ExpedienteAdministrativoId { get; set; }
    }
}
