using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ExpedientesAdministrativos.Dtos
{
    [Serializable]
    [ExcludeFromCodeCoverage]
    public class ExpedienteAdministrativoSemBlocoGetListInput : PagedAndSortedResultRequestDto
    {
        public string? NumeroProcessoSei { get; set; }
        public ETipoExpedienteAdministrativo? TipoExpedienteAdministrativo { get; set; }
        public string? NomeUsuario { get; set; }
        public string? NumeroRequisicao { get; set; }
    }
}
