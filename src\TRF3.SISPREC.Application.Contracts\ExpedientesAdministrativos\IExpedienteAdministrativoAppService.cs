using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.ExpedientesAdministrativos
{
    public interface IExpedienteAdministrativoAppService : IApplicationService
    {
        Task<PagedResultDto<ExpedienteAdministrativoSemBlocoDto>> GetExpedientesSemBloco(ExpedienteAdministrativoSemBlocoGetListInput input);
        Task GerarBloco(List<SemBlocosDto> blocosDto);
        Task IncluirExpedienteEmBlocoExistente(CreateSemBlocoDto blocoDto);
        Task<PagedResultDto<ExpedienteAdministrativoDto>> GetExpedientes(ExpedienteAdministrativoGetListInput input);
    }
}
