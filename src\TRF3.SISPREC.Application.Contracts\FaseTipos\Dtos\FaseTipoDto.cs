using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.FaseTipos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class FaseTipoDto : EntityDto
{
    public int Id { get; set; }

    /// <summary>
    /// Código do tipo da fase.
    /// </summary>
    public EFaseTipo Codigo { get; set; }

    public string Descricao { get; set; }

    public string Observacao { get; set; }

    public string Origem { get; set; }

    public int? NumeroParcela { get; set; }

    public DateTime? DataUtilizacaoFim { get; set; }

    /// <summary>
    /// Código do Tipo de Fase no CJF (campo SEQ_CJF)
    /// </summary>
    public int? SequencialCJF { get; set; }
}