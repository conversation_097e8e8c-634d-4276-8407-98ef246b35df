using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;

[Serializable]
public class CreateUpdateIndicadorEconomicoTipoDto
{
    [Required]
    [StringLength(3)]
    [Display(Name = "Código")]
    public string? Codigo { get; set; }

    [Required]
    [StringLength(50)]
    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    [Required]
    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }
}