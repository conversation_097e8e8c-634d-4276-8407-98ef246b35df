using Volo.Abp.Application.Dtos;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;

[Serializable]
public class IndicadorEconomicoTipoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Id")]
    public int? TipoIndicadorEconomicoId { get; set; }

    [Display(Name = "Código")]
    public string? Codigo { get; set; }

    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

}