using TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.IndicadorEconomicoTipos
{
    public interface IIndicadorEconomicoTipoAppService : ICrudAppService<IndicadorEconomicoTipoDto, int, IndicadorEconomicoTipoGetListInput, CreateUpdateIndicadorEconomicoTipoDto, CreateUpdateIndicadorEconomicoTipoDto>
    {
        Task<ListResultDto<IndicadorEconomicoTipoDto>> GetLookupAsync();
    }
}