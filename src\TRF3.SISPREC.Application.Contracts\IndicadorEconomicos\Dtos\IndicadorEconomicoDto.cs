using System.ComponentModel;
using TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.IndicadorEconomicos.Dtos
{
    public class IndicadorEconomicoDto : EntityDto
    {
        [DisplayName("Id")]
        public int IndicadorEconomicoId { get; set; }

        [DisplayName("Indicador Economico Tipo Id")]
        public int IndicadorEconomicoTipoId { get; set; }

        [DisplayName("Data Indicador")]
        public DateTime DataIndicador { get; set; }

        [DisplayName("Valor")]
        public decimal Valor { get; set; }

        [DisplayName("Percentual Inflacionário")]
        public decimal? PercentualInflacionario { get; set; }

        [DisplayName("Sigla Moeda")]
        public string? SiglaMoeda { get; set; }

        [DisplayName("Ativo")]
        public bool IsDeleted { get; set; }

        public IndicadorEconomicoTipoDto? TipoIndicadorEconomico { get; set; }
    }
}