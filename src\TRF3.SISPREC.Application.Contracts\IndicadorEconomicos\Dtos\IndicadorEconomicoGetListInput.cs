using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.IndicadorEconomicos.Dtos;

[Serializable]
public class IndicadorEconomicoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Id")]
    public int? IndicadorEconomicoId { get; set; }

    [Display(Name = "Data")]
    public DateTime? Data { get; set; }

    [Display(Name = "Valores referenciais - Indicador")]
    public decimal? ValorIndicador { get; set; }

    [Display(Name = "Valores referenciais - Inflação")]
    public decimal? ValorInflacao { get; set; }

    [Display(Name = "Tipo Indicador")]
    public int? TipoIndicadorEconomicoId { get; set; }

    [Display(Name = "Moeda")]
    public string? Moeda { get; set; }
}