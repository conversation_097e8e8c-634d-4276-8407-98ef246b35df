using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdateIndiceAtualizacaoMonetariaTipoDto
{
    [Display(Name = "Código")]
    public string Codigo { get; set; }

    [Display(Name = "Descrição")]
    public string Descricao { get; set; }

    [Display(Name = "Descrição de Uso")]
    public string DescricaoUsoCodigo { get; set; }

    [Display(Name = "Sequencial CJF")]
    public int? SequencialCJF { get; set; }

}