using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.Dtos;

[Serializable]
public class IndiceAtualizacaoMonetariaTipoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "ID Atual. Mon.")]
    public int? Seq_Indice_Atuali_Moneta_Tipo { get; set; }

    [Display(Name = "Código")]
    public string? Codigo { get; set; }

    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    [Display(Name = "Descrição de Uso")]
    public string? DescricaoUsoCodigo { get; set; }

    [Display(Name = "Sequencial CJF")]
    public int? SequencialCJF { get; set; }

}