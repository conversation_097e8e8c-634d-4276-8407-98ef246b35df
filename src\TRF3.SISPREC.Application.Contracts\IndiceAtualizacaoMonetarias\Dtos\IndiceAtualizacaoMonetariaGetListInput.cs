using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.IndiceAtualizacaoMonetarias.Dtos;

[Serializable]
public class IndiceAtualizacaoMonetariaGetListInput : PagedAndSortedResultRequestDto
{
    public int? Seq_Indice_Atuali_Moneta { get; set; }

    public int? Ano { get; set; }

    public int? Mes { get; set; }

    public DateTime? DataValidadeInicio { get; set; }

    public DateTime? DataValidadeFim { get; set; }

    /// <summary>
    /// Data de cadastro do dado no banco de dados.
    /// </summary>
    public DateTime? DataRegistro { get; set; }

    public decimal? Valor { get; set; }

    public ETipoIndice? TipoIndiceEnum { get; set; }

    public int? SequencialCJF { get; set; }

    public int? IndiceAtualizacaoMonetariaTipoId { get; set; }

}
