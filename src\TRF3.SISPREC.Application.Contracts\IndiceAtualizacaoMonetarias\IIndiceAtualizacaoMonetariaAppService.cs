using TRF3.SISPREC.IndiceAtualizacaoMonetarias.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.IndiceAtualizacaoMonetarias;


public interface IIndiceAtualizacaoMonetariaAppService :
    IReadOnlyAppService<
        IndiceAtualizacaoMonetariaDto,
        int,
        IndiceAtualizacaoMonetariaGetListInput>
{
    Task CreateAsync(IndiceAtualizacaoMonetariaDto dto);
    Task UpdateAsync(IndiceAtualizacaoMonetariaDto dto);
    Task<IndiceAtualizacaoMonetariaDto> GetAsync(int id);
}
