using Volo.Abp.Application.Dtos;
using System.Text.Json.Serialization;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;

namespace TRF3.SISPREC.JustificativaComparacoes.Dtos;

[Serializable]
public class JustificativaComparacaoDto : EntityDto
{
    public string? NumeroProtocoloRequisicao { get; set; }

    public long RequisicaoJustificativaId { get; set; }

    public string? Observacoes { get; set; }

    public bool IsDeleted { get; set; }

    [JsonIgnore]
    public RequisicaoJustificativaDto? RequisicaoJustificativa { get; set; }
}