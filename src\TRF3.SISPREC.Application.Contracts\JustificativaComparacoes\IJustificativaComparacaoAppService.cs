using TRF3.SISPREC.JustificativaComparacoes.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.JustificativaComparacoes;

public interface IJustificativaComparacaoAppService :
    ICrudAppService<
        JustificativaComparacaoDto,
        JustificativaComparacaoKey,
        JustificativaComparacaoGetListInput,
        CreateUpdateJustificativaComparacaoDto,
        CreateUpdateJustificativaComparacaoDto>
{
    public Task<List<JustificativaComparacao>> ObterJustificativaComparacaoPorRequisicaoJustificativa(long requisicaoJustificativaId);
}