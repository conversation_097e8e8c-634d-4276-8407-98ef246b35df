using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.JustificativaDocumentos.Dtos
{
    [ExcludeFromCodeCoverage]
    public class CreateEspelhoRequisicaoDto : EntityDto, IValidatableObject
    {
        public long RequisicaoJustificativaId { get; set; }
        public List<string> NumeroRequisicoes { get; set; } = [];
        public string Observacao { get; set; }
        public string Procedimento { get; set; }
        public List<RequisicaoComparadaDto> RequisicoesComparada { get; set; } = [];

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (RequisicoesComparada != null && RequisicoesComparada.Any())
            {
                if (RequisicoesComparada.Any(x => NumeroRequisicoes.Contains(x.NumeroRequisicao!)))
                    yield return new ValidationResult("A requisição comparada não pode ser a mesma para a qual está sendo cadastrada a justificativa.");

                bool existeDuplicada = RequisicoesComparada
                    .GroupBy(x => x.NumeroRequisicao)
                    .Any(group => group.Count() > 1);

                if (existeDuplicada)
                    yield return new ValidationResult("Requisição já incluída. Para editar as observações, remova a requisição e inclua novamente.");
            }
        }
    }
}
