using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.JustificativaDocumentos.Dtos;

[Serializable]
public class CreateUpdateJustificativaDocumentoDto
{
    [Display(Name = "Nome do Documento")]
    public string NomeDocumento { get; set; }

    [Display(Name = "Caminho do Arquivo")]
    public string Path { get; set; }

    [Display(Name = "Data de Criação")]
    public DateTime DataCriacao { get; set; }

    [Display(Name = "Deletado")]
    public bool IsDeleted { get; set; }

    public long RequisicaoJustificativaId { get; set; }
}