using Volo.Abp.Application.Dtos;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.RequisicaoJustificativas;

namespace TRF3.SISPREC.JustificativaDocumentos.Dtos;

[Serializable]
public class JustificativaDocumentoDto : EntityDto
{
    [Display(Name = "Documento Requisição Id")]
    public long JustificativaDocumentoId { get; set; }

    [Display(Name = "Nome do Documento")]
    public string NomeDocumento { get; set; }

    [Display(Name = "Caminho do Arquivo")]
    public string Path { get; set; }

    [Display(Name = "Data de Criação")]
    public DateTime DataCriacao { get; set; }

    [Display(Name = "Deletado")]
    public bool IsDeleted { get; set; }

    public long RequisicaoJustificativaId { get; set; }

    #region Virtual Properties

    public RequisicaoJustificativa? RequisicaoJustificativa { get; set; }

    #endregion
}