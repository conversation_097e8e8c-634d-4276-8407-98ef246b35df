using Volo.Abp.Application.Dtos;
using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.JustificativaDocumentos.Dtos;

[Serializable]
public class JustificativaDocumentoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Documento Requisição Id")]
    public long? RequisicaoDocumentoId { get; set; }

    [Display(Name = "Nome do Documento")]
    public string? NomeDocumento { get; set; }

    [Display(Name = "Caminho do Arquivo")]
    public string? Path { get; set; }

    [Display(Name = "Data de Criação")]
    public DateTime? DataCriacao { get; set; }

    [Display(Name = "Numero do Protocolo Requisição")]
    public string? NumeroProtocoloRequisicao { get; set; }

    [Display(Name = "Deletado")]
    public bool? IsDeleted { get; set; }
}