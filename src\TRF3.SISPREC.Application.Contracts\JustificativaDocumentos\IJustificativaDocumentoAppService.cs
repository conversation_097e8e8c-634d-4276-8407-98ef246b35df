using Microsoft.AspNetCore.Http;
using TRF3.SISPREC.JustificativaDocumentos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.JustificativaDocumentos;

public interface IJustificativaDocumentoAppService :
    ICrudAppService<
        JustificativaDocumentoDto,
        long,
        JustificativaDocumentoGetListInput,
        CreateUpdateJustificativaDocumentoDto,
        CreateUpdateJustificativaDocumentoDto>
{
    Task<List<EspelhoRequisicaoJustificativaDocumento>> GerarEspelhoRequisicaoAsync(CreateEspelhoRequisicaoDto input);

    Task ExcluirEspelhoRequisicaoAsync(long requisicaoJustificativaId);

    Task SalvarExtratoRFBAsync(long requisicaoJustificativaId, string numeroRequisicao, string procedimento, IFormFile arquivo);
}