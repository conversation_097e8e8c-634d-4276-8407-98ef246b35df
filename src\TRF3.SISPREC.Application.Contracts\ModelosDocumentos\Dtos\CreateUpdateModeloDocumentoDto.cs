using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.ModelosDocumentos.Dtos;

[Serializable]
public class CreateUpdateModeloDocumentoDto : IValidatableObject
{
    public string? NomeModelo { get; set; }

    public string? TextoDocumento { get; set; }
    public int? SetorId { get; set; }

    public bool IsDeleted { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (string.IsNullOrWhiteSpace(NomeModelo) || (string.IsNullOrWhiteSpace(TextoDocumento) || TextoDocumento.Equals("<p><br></p>", StringComparison.OrdinalIgnoreCase)))
            yield return new ValidationResult("Nome do modelo e HTML devem estar preenchidos!", new[] { nameof(NomeModelo) });

        if (string.IsNullOrWhiteSpace(TextoDocumento) || TextoDocumento.Equals("<p><br></p>", StringComparison.OrdinalIgnoreCase))
            yield return new ValidationResult("O campo Texto do Documento é obrigatório e não pode ser vazio.", new[] { nameof(TextoDocumento) });

        if (SetorId == null || SetorId == 0)
            yield return new ValidationResult("O campo Setor é obrigatório.", new[] { nameof(SetorId) });
    }
}