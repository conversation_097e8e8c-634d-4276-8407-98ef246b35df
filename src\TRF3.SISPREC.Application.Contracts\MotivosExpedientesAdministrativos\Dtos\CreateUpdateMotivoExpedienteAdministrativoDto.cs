using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.MotivosExpedientesAdministrativos.Dtos
{
    [Serializable]
    public class CreateUpdateMotivoExpedienteAdministrativoDto : EntityDto
    {
        [StringLength(MotivoExpedienteAdministrativoConsts.DESCRICAO_MOTIVO_TAMANHO_MAX, ErrorMessage = "Descrição não deve ter mais de {1} caracteres!")]
        [Required]
        public string DescricaoMotivo { get; set; } = string.Empty;
    }
}
