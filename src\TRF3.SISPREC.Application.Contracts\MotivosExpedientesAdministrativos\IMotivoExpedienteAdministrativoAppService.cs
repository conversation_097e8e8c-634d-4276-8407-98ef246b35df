using TRF3.SISPREC.MotivosExpedientesAdministrativos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.MotivosExpedientesAdministrativos
{
    public interface IMotivoExpedienteAdministrativoAppService : ICrudAppService
        <MotivoExpedienteAdministrativoDto, int, MotivoExpedienteAdministrativoGetListInput,
        CreateUpdateMotivoExpedienteAdministrativoDto, CreateUpdateMotivoExpedienteAdministrativoDto>
    {
        Task<MotivoExpedienteAdministrativo> BuscaPorId(int id);
        Task<List<MotivoExpedienteAdministrativoDto>> ListarTodos();
    }
}
