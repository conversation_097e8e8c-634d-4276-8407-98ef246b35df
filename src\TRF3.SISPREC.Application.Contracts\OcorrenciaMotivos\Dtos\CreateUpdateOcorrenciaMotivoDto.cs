using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.OcorrenciaMotivos.Dtos;

[Serializable]
public class CreateUpdateOcorrenciaMotivoDto : EntityDto
{
    [Display(Name = "Código do Motivo")]
    [Range(0, OcorrenciaMotivoConsts.CODIGO_MOTIVO_TAMANHO_MAX, ErrorMessage = "O tamanho máximo é {2} caracteres.")]
    public int CodigoMotivo { get; set; }

    [Display(Name = "Ação Tipo")]
    [Required]
    public int AcaoTipoId { get; set; }

    [Display(Name = "Tela de Analise")]
    public int? AnaliseTelaId { get; set; }

    [Display(Name = "Descrição Motivo")]
    [Required]
    [StringLength(OcorrenciaMotivoConsts.DESCRICAO_MOTIVO_TAMANHO_MAX, ErrorMessage = "O tamanho máximo é {1} caracteres.")]
    public string DescricaoMotivo { get; set; } = string.Empty;

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }

    [Display(Name = "Deletado")]
    public bool IsDeleted { get; set; }

}