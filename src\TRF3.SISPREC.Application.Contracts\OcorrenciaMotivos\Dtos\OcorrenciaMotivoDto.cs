using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AnaliseTelas;
using TRF3.SISPREC.RequisicoesOcorrencias;

namespace TRF3.SISPREC.OcorrenciaMotivos.Dtos;

[Serializable]
public class OcorrenciaMotivoDto
{
    [Display(Name = "OcorrenciaMotivoId")]
    public int OcorrenciaMotivoId { get; set; }
    [Display(Name = "Código do Motivo")]
    public int CodigoMotivo { get; set; }

    [Display(Name = "Ação Tipo")]
    public int AcaoTipoId { get; set; }

    [DisplayName("Análise Tela Id")]
    public int? AnaliseTelaId { get; set; }

    [Display(Name = "Descrição Motivo")]
    public string? DescricaoMotivo { get; set; }

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }

    [Display(Name = "Deletado")]
    public bool IsDeleted { get; set; }

    [JsonIgnore]
    [Display(Name = "")]
    public ICollection<RequisicaoOcorrencia>? DtoRequisicaoOcorrencia { get; set; }

    [JsonIgnore]
    [Display(Name = "")]
    public AcaoTipo AcaoTipo { get; set; }

    [JsonIgnore]
    [Display(Name = "")]
    public AnaliseTela? AnaliseTela { get; set; }
}