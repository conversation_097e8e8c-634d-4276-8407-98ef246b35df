using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.OcorrenciaMotivos.Dtos;

[Serializable]
public class OcorrenciaMotivoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "ID Motivo Ocorrência")]
    public int? Id { get; set; }

    public int? CodigoMotivo { get; set; }

    [Display(Name = "Descrição Motivo")]
    public string DescricaoMotivo { get; set; } = string.Empty;

    [Display(Name = "Ação Tipo")]
    public EDescricaoAcaoTipo? AcaoTipoId { get; set; }

    [DisplayName("Tipo Análise")]
    public EDescricaoAnaliseTela? AnaliseTelaId { get; set; }

    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

    [Display(Name = "Deletado")]
    public bool? IsDeleted { get; set; }

}