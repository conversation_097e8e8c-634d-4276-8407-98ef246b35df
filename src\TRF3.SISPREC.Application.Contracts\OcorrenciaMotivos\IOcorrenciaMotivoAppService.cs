using TRF3.SISPREC.OcorrenciaMotivos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.OcorrenciaMotivos;

public interface IOcorrenciaMotivoAppService :
    ICrudAppService<
        OcorrenciaMotivoDto,
        int,
        OcorrenciaMotivoGetListInput,
        CreateUpdateOcorrenciaMotivoDto,
        CreateUpdateOcorrenciaMotivoDto>
{
    public Task<OcorrenciaMotivo> InserirAsync(CreateUpdateOcorrenciaMotivoDto ocorrenciaMotivoDto);
}