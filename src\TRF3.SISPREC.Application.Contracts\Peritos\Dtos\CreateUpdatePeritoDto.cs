using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Peritos.Dtos;

[Serializable]
public class CreateUpdatePeritoDto : EntityDto
{
    [StringLength(PeritoConsts.CPF_CNPJ_MAX_LENGTH, ErrorMessage = "O campo {0} deve ter no máximo {1} caracteres.")]
    public string? NumeroCnpjCpf { get; set; }

    [StringLength(PeritoConsts.NOME_MAX_LENGTH, ErrorMessage = "O campo {0} deve ter no máximo {1} caracteres.")]
    public string? NomePessoa { get; set; }

    public int VerificacaoTipoId { get; set; }

    public bool Ativo { get; set; }
}