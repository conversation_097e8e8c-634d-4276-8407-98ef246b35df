using Volo.Abp.Application.Dtos;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using TRF3.SISPREC.VerificacaoTipos;

namespace TRF3.SISPREC.Peritos.Dtos;

[Serializable]
public class PeritoDto : EntityDto
{
    [Display(Name = "")]
    public int PeritoId { get; set; }

    [Display(Name = "")]
    public string? NumeroCnpjCpf { get; set; }

    [Display(Name = "")]
    public string? NomePessoa { get; set; }

    [Display(Name = "")]
    public int VerificacaoTipoId { get; set; }

    [Display(Name = "")]
    public bool Ativo { get; set; }

    [JsonIgnore]//evitar problema de mapeamento circular - loop
    [Display(Name = "")]
    public VerificacaoTipo? VerificacaoTipo { get; set; }
}