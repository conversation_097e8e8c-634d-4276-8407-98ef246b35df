using TRF3.SISPREC.Peritos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.Peritos;

public interface IPeritoAppService :
    ICrudAppService<
        PeritoDto,
        int,
        PeritoGetListInput,
        CreateUpdatePeritoDto,
        CreateUpdatePeritoDto>
{
    public Task<Perito?> GetPeritoPorCpfCnpj(string numeroDocumento, int tipoVerificacao);
    public Task<string?> ObterNomePorCNPJ(string cnpj);
}