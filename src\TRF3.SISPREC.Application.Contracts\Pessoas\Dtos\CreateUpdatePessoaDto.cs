using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Pessoas.Dtos.EnderecosPessoas;

namespace TRF3.SISPREC.Pessoas.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdatePessoaDto
{
    [Display(Name = "Nome")]
    public string Nome { get; set; }

    [Display(Name = "Nome Social")]
    public string NomeSocial { get; set; }

    [Display(Name = "Tipo Pessoa")]
    public ETipoPessoa TipoPessoa { get; set; } // Certifique-se de que ETipoPessoa está definido em algum lugar

    [Display(Name = "Nº CNPJ/CPF")]
    public string NumeroCnpjCpf { get; set; }

    public CreateUpdateEnderecoPessoaDto? Endereco { get; set; } = new();
}
