using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enderecos.Dtos;

namespace TRF3.SISPREC.Pessoas.Dtos.EnderecosPessoas;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdateEnderecoPessoaDto
{
    [Display(Name = "Descrição Endereço")]
    public string DescricaoEndereco { get; set; }

    [Display(Name = "Nome Bairro")]
    public string NomeBairro { get; set; }

    [Display(Name = "CEP")]
    public int? Cep { get; set; }

    [Display(Name = "ID Municipio")]
    public int? MunicipioId { get; set; }
    public MunicipioDto MunicipioDto { get; set; } = new MunicipioDto();

}