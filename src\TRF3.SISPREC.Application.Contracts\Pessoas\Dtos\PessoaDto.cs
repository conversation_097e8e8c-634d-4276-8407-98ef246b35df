using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Pessoas.Dtos.EnderecosPessoas;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Pessoas.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class PessoaDto : EntityDto
{
    [Display(Name = "ID Pessoa")]
    public long PessoaId { get; set; }

    [Display(Name = "Nome")]
    public string Nome { get; set; }

    [Display(Name = "Nome Social")]
    public string NomeSocial { get; set; }

    [Display(Name = "Tipo Pessoa")]
    public ETipoPessoa TipoPessoa { get; set; }

    [Display(Name = "Nº CNPJ/CPF")]
    public string NumeroCnpjCpf { get; set; }

    public EnderecoPessoaDto Endereco { get; set; }

}