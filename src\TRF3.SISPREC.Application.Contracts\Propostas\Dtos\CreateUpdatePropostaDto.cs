using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Propostas.Dtos;

[Serializable]
[ExcludeFromCodeCoverage]
public class CreateUpdatePropostaDto
{
    [Display(Name = "Tipo Procedimento")]
    public string TipoProcedimentoId { get; set; }

    [Display(Name = "ID Unidade")]
    public int UnidadeId { get; set; }

    [Display(Name = "Ano Proposta")]
    public int AnoProposta { get; set; }

    [Display(Name = "Mês Proposta")]
    public int MesProposta { get; set; }

    [Display(Name = "Data Atualização")]
    public DateTime DataAtualizacao { get; set; }

    [Display(Name = "Valor Mínimo")]
    public decimal ValorMinimo { get; set; }

    [Display(Name = "Valor Máximo")]
    public decimal ValorMaximo { get; set; }

    [Display(Name = "Valor Mínimo Parcela")]
    public decimal ValorMinimoParcela { get; set; }

    [Display(Name = "Qtd. Máx. Parcela Alimentar")]
    public int QtdMaximaParcelaAlimetar { get; set; }

    [Display(Name = "Qtd. Máx. Parcela Comum")]
    public int QtdMaximaParcelaComum { get; set; }

    [Display(Name = "Qtd. Máx. Parcela Desapropriação Única")]
    public int QtdMaximaParcelaDesapropriacaoUnico { get; set; }

    [Display(Name = "Qtd. Máx. Parcela Desapropriação")]
    public int QtdMaximaParcelaDesapropriacao { get; set; }

    [Display(Name = "Tipo Atualização Monetária")]
    public ETipoIndice TipoAtualizacaoMonetaria { get; set; }

    [Display(Name = "Situação Proposta")]
    public ESituacaoProposta SituacaoProposta { get; set; }

    [Display(Name = "Data Início Cálc. Juros")]
    public DateTime? DataInicioCalculoJuros { get; set; }


}