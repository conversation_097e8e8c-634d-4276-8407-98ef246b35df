using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RelatorioPorJuizos.Dtos
{
    [Serializable]
    public class RelatorioPorJuizoDto : EntityDto
    {
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy HH:mm:ss}", ApplyFormatInEditMode = true)]
        [DisplayName("Data Protocolo")]
        public DateTime DatHoraProtocRequis { get; set; }

        [DisplayName("Nº Ofício")]
        public string NumOficioRequit { get; set; }

        [DisplayName("Status Protocolo")]
        public string StaProtocRequis { get; set; }

        [DisplayName("Identificador Protocolo")]
        public string IdeProtocRequis { get; set; }

        [DisplayName("Procedimento")]
        public string CodTipoProced { get; set; }

        [DisplayName("Nº Requisição")]
        public string NumProtocRequis { get; set; }

        [DisplayName("Tipo Juízo")]
        public string CodEspeciTipoJuizo { get; set; }

        [DisplayName("Código SIAFI Unidade")]
        public string CodSiafiUnidad { get; set; }

        [DisplayName("Unidade Judicial")]
        public string DesUnidadJudici { get; set; }

        [DisplayName("Nome Magistrado")]
        public string NomMagist { get; set; }

        [DisplayName("Valor Requisição")]
        public decimal ValRequis { get; set; }
    }
}
