using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.RelatorioPorJuizos.Dtos
{
    public class RelatorioPorJuizoFiltroExcelDto
    {
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", ApplyFormatInEditMode = true)]
        [DisplayName("Data Inicio")]
        public DateTime? DataInicio { get; set; }
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", ApplyFormatInEditMode = true)]
        [DisplayName("Data Fim")]
        public DateTime? DataFim { get; set; }
        [DisplayName("Unidade Judicial")]
        public string? DesUnidadJudici { get; set; }
        [DisplayName("Total")]
        public int? Total { get; set; }
    }
}
