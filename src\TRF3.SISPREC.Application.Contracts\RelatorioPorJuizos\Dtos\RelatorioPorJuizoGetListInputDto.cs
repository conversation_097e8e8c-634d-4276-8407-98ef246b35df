using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RelatorioPorJuizos.Dtos
{
    [Serializable]
    public class RelatorioPorJuizoGetListInputDto : PagedAndSortedResultRequestDto
    {
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", ApplyFormatInEditMode = true)]
        [DisplayName("Data Inicio")]
        public DateTime? DataInicio { get; set; }
        [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", ApplyFormatInEditMode = true)]
        [DisplayName("Data Fim")]
        public DateTime? DataFim { get; set; }
        [DisplayName("Unidade Judicial")]
        public string? DesUnidadJudici { get; set; }
        [DisplayName("Total")]
        public int? Total { get; set; }

        public RelatorioPorJuizoGetListInputDto()
        {
            DataInicio = DateTime.Now;
            DataFim = DateTime.Now.AddDays(1);
        }
    }
}
