using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RelatorioPorJuizos.Dtos
{
    [Serializable]
    public class TotalizadoresDto : EntityDto
    {
        public TotalizadoresDto()
        {
            RPVJuizados = 0;
            RPVFederal = 0;
            RPVEstadual = 0;
            RPVTRF = 0;
            PRCJuizados = 0;
            PRCFederal = 0;
            PRCEstadual = 0;
            PRCTRF = 0;
        }

        [Display(Name = "Juizados")]
        public int RPVJuizados { get; set; }

        [Display(Name = "Federal")]
        public int RPVFederal { get; set; }

        [Display(Name = "Estadual")]
        public int RPVEstadual { get; set; }

        [Display(Name = "TRF")]
        public int RPVTRF { get; set; }


        [Display(Name = "Juizados")]
        public int PRCJuizados { get; set; }

        [Display(Name = "Federal")]
        public int PRCFederal { get; set; }

        [Display(Name = "Estadual")]
        public int PRCEstadual { get; set; }

        [Display(Name = "TRF")]
        public int PRCTRF { get; set; }
    }
}
