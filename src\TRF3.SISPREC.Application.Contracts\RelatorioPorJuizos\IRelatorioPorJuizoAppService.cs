using TRF3.SISPREC.RelatorioPorJuizos.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.RelatorioPorJuizos
{
    public interface IRelatorioPorJuizoAppService : IApplicationService
    {
        Task<PagedResultDto<RelatorioPorJuizoDto>> GetListAsync(RelatorioPorJuizoGetListInputDto input);
        Task<TotalizadoresDto> GetTotalizadores(string? desUnidadeJudici, DateTime? dataInicial, DateTime? dataFinal);
        Task<byte[]> ExportarExcel(RelatorioPorJuizoGetListInputDto input);
    }
}
