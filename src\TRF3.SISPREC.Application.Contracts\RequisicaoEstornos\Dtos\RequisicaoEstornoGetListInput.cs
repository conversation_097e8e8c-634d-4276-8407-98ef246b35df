using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicaoEstornos.Dtos;

[Serializable]
public class RequisicaoEstornoGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Número do Protocolo")]
    public string? NumeroProtocoloRequisicaoId { get; set; }

    [Display(Name = "Número da Requisição")]
    public string? NumeroRequisicaoOriginal { get; set; }

    [Display(Name = "Número do Banco")]
    public string? NumeroBanco { get; set; }

    [Display(Name = "Número do Protocolo")]
    public string? NumeroContaCorrente { get; set; }

    [Display(Name = "Nome do Beneficiário")]
    public string? NomeBeneficiario { get; set; }

    [Display(Name = "Código do Beneficiário")]
    public string? CodigoBeneficiario { get; set; }

    [Display(Name = "Data de Recolhimento da Conta")]
    public DateTime? DataRecolhimentoConta { get; set; }

    [Display(Name = "Valor do Recolhimento da Conta")]
    public double? ValorRecolhimentoConta { get; set; }

    [Display(Name = "Data e Hora do Protocolo da Requisição")]
    public DateTime? DataHoraProtocoloRequisicao { get; set; }

    [Display(Name = "")]
    public string? RequisicaoProtocoloId { get; set; }

}