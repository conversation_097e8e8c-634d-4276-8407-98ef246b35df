using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.JustificativasComparacao;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicaoJustificativas.Dtos
{
    public class RequisicaoComparadaDto : EntityDto
    {
        [StringLength(JustificativaComparacaoConsts.NUM_PROTOC_REQUIS_COMPAR_TAMANHO_MAX, ErrorMessage = "O nome deve ter no máximo {1} caracteres")]
        public string? NumeroRequisicao { get; set; }
        public string? Observacoes { get; set; }
        public long RequisicaoJustificativaId { get; set; }
    }
}
