using TRF3.SISPREC.AcoesJustificativa.Dtos;
using TRF3.SISPREC.AnaliseTelas.Dtos;
using TRF3.SISPREC.JustificativaComparacoes.Dtos;
using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicaoJustificativas.Dtos;

[Serializable]
public class RequisicaoJustificativaDto : EntityDto
{
    public long RequisicaoJustificativaId { get; set; }

    public string? NumeroProtocoloRequisicao { get; set; }

    public int AnaliseTelaId { get; set; }

    public int AcaoJustificativaId { get; set; }

    public string? ComplementoMotivo { get; set; }

    public DateTime DataAnalise { get; set; }

    public string? NomeUsuario { get; set; }

    public string? Observacoes { get; set; }

    public bool IsDeleted { get; set; }

    public virtual RequisicaoProtocoloDto? RequisicaoProtocolo { get; set; }
    public virtual AnaliseTelaDto? AnaliseTela { get; set; }
    public virtual AcaoJustificativaDto? AcaoJustificativa { get; set; }
    public virtual ICollection<JustificativaComparacaoDto>? JustificativaComparacao { get; set; }
}