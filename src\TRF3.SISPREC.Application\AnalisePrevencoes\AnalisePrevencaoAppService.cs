using TRF3.SISPREC.Analises.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.AnalisePrevencoes;

public class AnalisePrevencaoAppService(IAnalisePrevencaoRepository repository) : BaseAppService, IAnalisePrevencaoAppService
{
    private readonly IAnalisePrevencaoRepository _repository = repository;

    public async Task<PagedResultDto<RequisicaoPrevencoes>> GetRequisicoes(AnaliseGetListInput input)
    {
        var requisicoes = await _repository.ObterRequisicoes(
            input.TipoProcedimento,
            input.Ano,
            input.Mes,
            input.DataInicio,
            input.DataTermino,
            input.NumeroProtocoloRequisicao);

        return new PagedResultDto<RequisicaoPrevencoes>
        {
            Items = requisicoes,
            TotalCount = requisicoes.Count
        };
    }

    public async Task<PagedResultDto<Prevencao>> GetPrevencoes(string numeroProtocoloRequisicao)
    {
        if (numeroProtocoloRequisicao.IsNullOrEmpty())
            return new PagedResultDto<Prevencao> { TotalCount = 0, Items = [] };

        var prevencoes = await _repository.ObterPrevencoes(numeroProtocoloRequisicao);

        return new PagedResultDto<Prevencao>
        {
            Items = prevencoes,
            TotalCount = prevencoes.Count
        };
    }
}
