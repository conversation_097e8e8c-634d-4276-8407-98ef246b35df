using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;
using Volo.Abp.Auditing;

namespace TRF3.SISPREC;

/// <summary>
/// Classe base abstrata que fornece funcionalidades comuns para todos os serviços de aplicação do sistema.
/// Herda de ApplicationService do ABP Framework e adiciona configurações padrão de autorização e auditoria.
/// </summary>
/// <remarks>
/// Esta classe serve como base para todos os serviços de aplicação, fornecendo:
/// - Configuração padrão de autorização através do atributo [Authorize]
/// - Desabilitação de auditoria por padrão através do atributo [DisableAuditing]
/// - Acesso aos recursos e funcionalidades básicas do ApplicationService do ABP
/// </remarks>
/// <example>
/// Para implementar um serviço básico:
/// <code>
/// public class MeuAppService : BaseAppService
/// {
///     public MeuAppService()
///     {
///         // Implementação específica
///     }
/// }
/// </code>
/// </example>
/// Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[Authorize]
public abstract class BaseAppService : ApplicationService
{

}
