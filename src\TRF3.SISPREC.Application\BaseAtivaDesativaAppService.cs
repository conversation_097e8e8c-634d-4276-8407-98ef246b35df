using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Domain;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC;

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey>
    : BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey, PagedAndSortedResultRequestDto>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey, TGetListInput>
    : BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey, TGetListInput, TEntityDto, TEntityDto>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput>
    : BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TCreateInput>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
public abstract class BaseAtivaDesativaAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseAtivaDesativaAppService<TEntity, TEntityDto, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

/// <summary>
/// Classe base para serviços de aplicação que gerenciam entidades com estado ativo/inativo.
/// Herda de BaseCrudAppService e adiciona funcionalidade de ativação/desativação.
/// </summary>
/// <typeparam name="TEntity">Tipo da entidade de domínio que herda de BaseAtivaDesativaEntity</typeparam>
/// <typeparam name="TGetOutputDto">DTO para operações de leitura individual</typeparam>
/// <typeparam name="TGetListOutputDto">DTO para operações de listagem</typeparam>
/// <typeparam name="TKey">Tipo da chave primária da entidade</typeparam>
/// <typeparam name="TGetListInput">DTO para parâmetros de listagem</typeparam>
/// <typeparam name="TCreateInput">DTO para operação de criação</typeparam>
/// <typeparam name="TUpdateInput">DTO para operação de atualização</typeparam>
/// <remarks>
/// Utilize esta classe base quando a entidade possuir um estado ativo/inativo gerenciável.
/// Além das operações CRUD padrão, fornece o método AtivarDesativarAsync para alternar o estado da entidade.
/// A entidade deve herdar de BaseAtivaDesativaEntity para utilizar esta funcionalidade.
/// </remarks>
/// <example>
/// Para implementar um serviço com ativação/desativação:
/// <code>
/// public class MeuAppService : BaseAtivaDesativaAppService&lt;MinhaEntidade, MeuDto, int, MeuFiltroDto, MeuCreateDto, MeuUpdateDto&gt;
/// {
///     public MeuAppService(IRepository&lt;MinhaEntidade&gt; repository, EntidadeManager manager)
///         : base(repository, manager)
///     {
///     }
/// }
/// </code>
/// </example>
public abstract class BaseAtivaDesativaAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {
    }

    [ExcludeFromCodeCoverage]
    public virtual async Task AtivarDesativarAsync(TKey id)
    {
        var entity = await GetEntityByIdAsync(id);

        if (entity.Ativo)
            entity.Ativo = false;
        else
            entity.Ativo = true;

        await Manager.AlterarAsync(entity, autoSave: true);
    }
}
