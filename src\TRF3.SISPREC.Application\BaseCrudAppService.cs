using Microsoft.AspNetCore.Authorization;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Domain;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC;

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseCrudAppService<TEntity, TEntityDto, TKey>
    : BaseCrudAppService<TEntity, TEntityDto, TKey, PagedAndSortedResultRequestDto>
    where TEntity : class, IEntity
{
    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseCrudAppService<TEntity, TEntityDto, TKey, TGetListInput>
    : BaseCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TEntityDto, TEntityDto>
    where TEntity : class, IEntity
{
    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput>
    : BaseCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TCreateInput>
    where TEntity : class, IEntity
{
    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[ExcludeFromCodeCoverage]
public abstract class BaseCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TEntityDto, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{
    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

/// <summary>
/// Classe base para serviços de aplicação que implementam operações CRUD completas.
/// Estende o AbstractKeyCrudAppService do ABP Framework com funcionalidades específicas do sistema.
/// </summary>
/// <typeparam name="TEntity">Tipo da entidade de domínio</typeparam>
/// <typeparam name="TGetOutputDto">DTO para operações de leitura individual</typeparam>
/// <typeparam name="TGetListOutputDto">DTO para operações de listagem</typeparam>
/// <typeparam name="TKey">Tipo da chave primária da entidade</typeparam>
/// <typeparam name="TGetListInput">DTO para parâmetros de listagem</typeparam>
/// <typeparam name="TCreateInput">DTO para operação de criação</typeparam>
/// <typeparam name="TUpdateInput">DTO para operação de atualização</typeparam>
/// <remarks>
/// Fornece implementação padrão para operações CRUD com:
/// - Gerenciamento de políticas de autorização através das propriedades VisualizarPolicyName e GravarPolicyName
/// - Integração com DomainManager para execução das operações
/// - Suporte a auditoria (desabilitada por padrão)
/// - Verificações de autorização automáticas
/// </remarks>
/// <example>
/// Para implementar um serviço CRUD completo:
/// <code>
/// public class MeuAppService : BaseCrudAppService&lt;MinhaEntidade, MeuDto, int, MeuFiltroDto, MeuCreateDto, MeuUpdateDto&gt;
/// {
///     public MeuAppService(IRepository&lt;MinhaEntidade&gt; repository, EntidadeManager manager)
///         : base(repository, manager)
///     {
///         VisualizarPolicyName = "MinhaEntidade.Visualizar";
///         GravarPolicyName = "MinhaEntidade.Gravar";
///     }
/// }
/// </code>
/// </example>
[DisableAuditing]
[Authorize]
public abstract class BaseCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : AbstractKeyCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{
    protected virtual string? VisualizarPolicyName { get; set; }
    protected virtual string? GravarPolicyName { get; set; }
    protected virtual IBaseDomainManager<TEntity> Manager { get; }

    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository)
    {
        Manager = manager;
        // Atribuir as políticas de criação, atualização, exclusão e visualização com base nas propriedades configuradas
        GetPolicyName = VisualizarPolicyName;
        GetListPolicyName = VisualizarPolicyName;
        CreatePolicyName = GravarPolicyName;
        UpdatePolicyName = GravarPolicyName;
        DeletePolicyName = GravarPolicyName;
    }

    public override async Task<TGetOutputDto> CreateAsync(TCreateInput input)
    {
        await CheckCreatePolicyAsync();

        var entity = await MapToEntityAsync(input);

        TryToSetTenantId(entity);

        await Manager.InserirAsync(entity, autoSave: true);

        return await MapToGetOutputDtoAsync(entity);
    }

    public override async Task<TGetOutputDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await GetEntityByIdAsync(id);
        await MapToEntityAsync(input, entity);
        await Manager.AlterarAsync(entity, autoSave: true);

        return await MapToGetOutputDtoAsync(entity);
    }

    protected override async Task DeleteByIdAsync(TKey id)
    {
        var entity = await GetEntityByIdAsync(id);

        await Manager.ExcluirAsync(k => k.Equals(entity));
    }
}
