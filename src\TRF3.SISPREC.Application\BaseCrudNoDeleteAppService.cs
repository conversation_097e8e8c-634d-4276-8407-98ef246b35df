using Microsoft.AspNetCore.Authorization;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Domain;
using Volo.Abp;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC;

/// <summary>
/// Classe base para serviços de aplicação CRUD que não permitem operações de exclusão.
/// Herda de BaseCrudAppService e sobrescreve os métodos de exclusão para lançar exceções.
/// </summary>
/// <typeparam name="TEntity">Tipo da entidade de domínio</typeparam>
/// <typeparam name="TEntityDto">DTO utilizado para operações de leitura, criação e atualização</typeparam>
/// <typeparam name="TKey">Tipo da chave primária da entidade</typeparam>
/// <typeparam name="TGetListInput">DTO para parâmetros de listagem</typeparam>
/// <typeparam name="TCreateInput">DTO para operação de criação</typeparam>
/// <typeparam name="TUpdateInput">DTO para operação de atualização</typeparam>
/// <remarks>
/// Utilize esta classe base quando a entidade não deve permitir exclusão via API.
/// Os métodos DeleteAsync e DeleteByIdAsync são sobrescritos para lançar InvalidOperationException.
/// A classe herda as funcionalidades padrão de CRUD do BaseCrudAppService, mantendo apenas as operações de
/// criação, leitura e atualização.
/// </remarks>
/// <example>
/// Para implementar um serviço sem exclusão:
/// <code>
/// public class MeuAppService : BaseCrudNoDeleteAppService<MinhaEntidade, MeuDto, int, MeuFiltroDto, MeuCreateDto, MeuUpdateDto>
/// {
///     public MeuAppService(IRepository<MinhaEntidade> repository, EntidadeManager manager)
///         : base(repository, manager)
///     {
///     }
/// }
/// </code>
/// </example>
/// Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[Authorize]
[ExcludeFromCodeCoverage]
public abstract class BaseCrudNoDeleteAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TEntityDto, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{

    protected BaseCrudNoDeleteAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager)
    {

    }

    [RemoteService(false)]
    public override Task DeleteAsync(TKey id)
    {
        throw new InvalidOperationException("Não é possível excluir essa entidade!");
    }

    [RemoteService(false)]
    protected override Task DeleteByIdAsync(TKey id)
    {
        throw new InvalidOperationException("Não é possível excluir essa entidade!");
    }
}
