using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC;

/// <summary>
/// Classe base para serviços de aplicação que fornecem apenas operações de leitura.
/// Herda de AbstractKeyReadOnlyAppService do ABP Framework e adiciona controle de autorização específico.
/// </summary>
/// <typeparam name="TEntity">Tipo da entidade de domínio</typeparam>
/// <typeparam name="TEntityDto">DTO para operações de leitura</typeparam>
/// <typeparam name="TKey">Tipo da chave primária da entidade</typeparam>
/// <typeparam name="TGetListInput">DTO para parâmetros de listagem</typeparam>
/// <remarks>
/// Utilize esta classe base quando a entidade permitir apenas operações de leitura.
/// Fornece controle de autorização através da propriedade VisualizarPolicyName.
/// A auditoria é desabilitada por padrão através do atributo [DisableAuditing].
/// </remarks>
/// <example>
/// Para implementar um serviço somente-leitura:
/// <code>
/// public class MeuAppService : BaseReadOnlyAppService&lt;MinhaEntidade, MeuDto, int, MeuFiltroDto&gt;
/// {
///     public MeuAppService(IRepository&lt;MinhaEntidade&gt; repository) 
///         : base(repository)
///     {
///         VisualizarPolicyName = "MinhaEntidade.Visualizar";
///     }
/// }
/// </code>
/// </example>
/// Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
[Authorize]
public abstract class BaseReadOnlyAppService<TEntity, TEntityDto, TKey, TGetListInput> : AbstractKeyReadOnlyAppService<TEntity, TEntityDto, TKey, TGetListInput> where TEntity : class, IEntity
{
    protected virtual string? VisualizarPolicyName { get; set; }

    protected BaseReadOnlyAppService(IRepository<TEntity> repository)
        : base(repository)
    {
        GetPolicyName = VisualizarPolicyName;
        GetListPolicyName = VisualizarPolicyName;
    }
}
