using TRF3.SISPREC.Domain;
using Volo.Abp;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC;

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
public abstract class BaseSincronizavelAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseSincronizavelAppService<TEntity, TEntityDto, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseEntidadeDominio, IEntity
{
    protected BaseSincronizavelAppService(IRepository<TEntity> repository, ISincronizavelManager<TEntity> manager)
        : base(repository, manager)
    {

    }
}

//Desabilita AuditLog por padrão. Habilite para AppServices específicos, se necessário.
[DisableAuditing]
public abstract class BaseSincronizavelAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseAtivaDesativaAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseEntidadeDominio, IEntity

{
    protected override ISincronizavelManager<TEntity> Manager { get; }

    protected BaseSincronizavelAppService(IRepository<TEntity> repository, ISincronizavelManager<TEntity> manager)
        : base(repository, manager)
    {
        Manager = manager;
    }

    public override async Task<TGetOutputDto> CreateAsync(TCreateInput input)
    {
        await CheckCreatePolicyAsync();

        var entidade = await MapToEntityAsync(input);
        entidade.FoiSincronizadoCjf = false;

        await Manager.InserirAsync(entidade);

        return await MapToGetOutputDtoAsync(entidade);
    }

    public override async Task<TGetOutputDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        await CheckUpdatePolicyAsync();

        var entidade = await GetEntityByIdAsync(id);

        if (await Manager.RegistroFoiSincronizadoCjf(entidade))
            throw new UserFriendlyException("Não é permitido alterar registros sincronizados com o CJF.");


        await MapToEntityAsync(input, entidade);

        entidade.FoiSincronizadoCjf = false;
        await Manager.AlterarAsync(entidade, autoSave: true);

        return await MapToGetOutputDtoAsync(entidade);
    }
}
