using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.BlocosSisprec.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.BlocosSisprec
{
    public class BlocoSisprecAppService(IBlocoSisprecRepository repository, IBlocoSisprecManager manager) : BaseCrudAppService<BlocoSisprec, BlocoSisprecDto, int, BlocoSisprecGetListInput, CreateUpdateModeloBlocoSisprecDto, CreateUpdateModeloBlocoSisprecDto>(repository, manager), IBlocoSisprecAppService
    {

        protected override IQueryable<BlocoSisprec> ApplyDefaultSorting(IQueryable<BlocoSisprec> query)
        {
            return query.OrderByDescending(e => e.BlocoSisprecId);
        }

        protected override async Task<BlocoSisprec> GetEntityByIdAsync(int id)
        {
            return await repository.FindAsync(m => m.BlocoSisprecId == id) ?? new BlocoSisprec();
        }

        protected override async Task<IQueryable<BlocoSisprec>> CreateFilteredQueryAsync(BlocoSisprecGetListInput input)
        {
            return (await repository.GetQueryableAsync())
                            .Include(x => x.ExpedienteAdministrativo)
                                .ThenInclude(ea => ea.RequisicaoExpedienteAdministrativo)
                            .WhereIf(input.NumeroBloco.HasValue, x => x.BlocoSisprecId == input.NumeroBloco)
                            .WhereIf(!string.IsNullOrWhiteSpace(input.NumeroExpediente), x =>
                                x.ExpedienteAdministrativo.Any(ea => ea.NumeroProcessoSei == input.NumeroExpediente))
                            .WhereIf(input.DataCriacaoInicio.HasValue, x => x.DataCriacao.Date >= input.DataCriacaoInicio)
                            .WhereIf(input.DataCriacaoFim.HasValue, x => x.DataCriacao.Date <= input.DataCriacaoFim)
                            .WhereIf(!string.IsNullOrWhiteSpace(input.Usuario), x => x.NomeUsuario.Contains(input.Usuario!))
                            .WhereIf(input.Status.HasValue, x => x.StatusBloco == input.Status)
                            .WhereIf(!string.IsNullOrWhiteSpace(input.Requisicao), x =>
                                x.ExpedienteAdministrativo.Any(ea =>
                                    ea.RequisicaoExpedienteAdministrativo != null &&
                                    ea.RequisicaoExpedienteAdministrativo.Any(rea =>
                                        rea.NumeroProtocoloRequisicao == input.Requisicao))).Select(x => new BlocoSisprec
                                        {
                                            BlocoSisprecId = x.BlocoSisprecId,
                                            DataCriacao = x.DataCriacao,
                                            NomeUsuario = x.NomeUsuario,
                                            StatusBloco = x.StatusBloco
                                        })
                            .Distinct();
        }

        public async Task<IList<BlocoSisprec>> GetBlocosGerados(string tipoExpediente)
        {
            ETipoExpedienteAdministrativo tipoExpedienteAdministrativo = ETipoExpedienteAdministrativoHelper.Parse(tipoExpediente.Substring(0, 1).ToUpper());

            return await (await repository.GetQueryableAsync())
                    .Include(x => x.ExpedienteAdministrativo)
                    .Where(x => x.StatusBloco == Enums.EStatusBloco.GERADO &&
                          (!x.ExpedienteAdministrativo.Any() || x.ExpedienteAdministrativo.Any(x => x.TipoExpedienteAdministrativo == tipoExpedienteAdministrativo))
                    )
                    .OrderByDescending(x => x.BlocoSisprecId)
                    .ToListAsync();
        }
    }
}