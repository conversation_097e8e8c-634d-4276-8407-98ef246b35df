using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.ConferirObservacoes.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExcelServices;
using TRF3.SISPREC.Pdf;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.RequisicaoObservacoes;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ConferirObservacoes
{
    public class ConferirObservacaoAppService : BaseAppService, IConferirObservacaoAppService
    {
        private readonly IRequisicaoObservacaoRepository _repository;
        private readonly IExcelFileGeneratorService _excelFileGeneratorService;
        private readonly IPdfFileGeneratorService _pdfService;

        public ConferirObservacaoAppService(IRequisicaoObservacaoRepository repository, IExcelFileGeneratorService excelFileGeneratorService, IPdfFileGeneratorService pdfService)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _excelFileGeneratorService = excelFileGeneratorService ?? throw new ArgumentNullException(nameof(excelFileGeneratorService));
            _pdfService = pdfService ?? throw new ArgumentNullException(nameof(repository));
        }

        public async Task<PagedResultDto<ConferirObservacaoDto>> GetListAsync(ConferirObservacaoGetListInputDto input)
        {
            var query = await _repository.ListarConferirObservacao(
                input.TipoProcedimento, input.Mes, input.Ano, input.NumeroRequisicao, input.OpcoesCheckbox, input.OutrosTermos, input.PossuiJustificativa);

            int totalCount = await query.CountAsync();
            query = query
                .OrderByIf<ConferirObservacao, IQueryable<ConferirObservacao>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting!)
                .PageBy(input);

            var mapper = ObjectMapper.Map<IList<ConferirObservacao>, IList<ConferirObservacaoDto>>(query.ToList());

            var retorno = new PagedResultDto<ConferirObservacaoDto>
            {
                Items = mapper.ToList(),
                TotalCount = totalCount,
            };

            return retorno;
        }

        public async Task<byte[]> ExportarExcel(ConferirObservacaoGetListInputDto input)
        {
            var query = await _repository.ListarConferirObservacao(input.TipoProcedimento, input.Mes, input.Ano, input.NumeroRequisicao, input.OpcoesCheckbox, input.OutrosTermos, input.PossuiJustificativa);
            var lista = await query
                    .Select(x => new ConferirObservacaoExcelDto
                    {
                        NumeroProtocolo = x.NumeroProtocolo,
                        Observacao = x.Observacao,
                        TipoJustificativa = x.TipoJustificativa
                    }).ToListAsync();

            var filtro = new ConferirObservacaoFiltroExcelDto()
            {
                NumeroRequisicao = input.NumeroRequisicao,
                Total = lista.Count
            };

            if (input.NumeroRequisicao.IsNullOrEmpty())
            {
                filtro.TipoProcedimento = input.TipoProcedimento;
                filtro.Ano = input.Ano;
                filtro.Mes = input.Mes;
                filtro.PossuiJustificativa = input.PossuiJustificativa;

                var filtrosCombinados = CombinarFiltros(input.OpcoesCheckbox, input.OutrosTermos);
                filtro.Termos = string.Join(", ", filtrosCombinados);
            }

            return _excelFileGeneratorService.GenerateExcelFile(lista, EStyleFile.TITULO_NEGRITO, filtro);
        }

        public async Task<byte[]> GerarPDF(ConferirObservacaoGetListInputDto input)
        {
            var query = await _repository.ListarConferirObservacao(
                input.TipoProcedimento, input.Mes, input.Ano, input.NumeroRequisicao, input.OpcoesCheckbox, input.OutrosTermos, input.PossuiJustificativa);

            input.OcultarFiltro = input.NumeroRequisicao.IsNullOrEmpty() ? string.Empty : PdfConsts.HIDDEN;
            input.OcultarRequisicaoFiltro = !input.NumeroRequisicao.IsNullOrEmpty() ? string.Empty : PdfConsts.HIDDEN;

            var filtrosCombinados = CombinarFiltros(input.OpcoesCheckbox, input.OutrosTermos);
            var dto = new ConferirObservacaoPdfDto()
            {
                Filtro = input,
                Termos = string.Join(", ", filtrosCombinados),
                ListConferirObservacao = await query.ToListAsync(),
                Usuario = CurrentUser.UserName ?? string.Empty
            };

            return await _pdfService.GenerateFileAsync("ConferirObservacaoTemplate", dto, "RELATÓRIO DE OBSERVAÇÕES");
        }

        private static List<string> CombinarFiltros(List<string> opcoesCheckbox, string? outrosTermos)
        {
            var checkboxList = opcoesCheckbox is null ? [] : opcoesCheckbox;

            var termosList = !string.IsNullOrWhiteSpace(outrosTermos)
                ? outrosTermos.Split(',').Select(term => term.Trim()).ToList()
                : new List<string>();

            return checkboxList.Concat(termosList).ToList();
        }
    }
}