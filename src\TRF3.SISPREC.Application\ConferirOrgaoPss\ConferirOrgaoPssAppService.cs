using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.ConferirOrgaoPss.Dtos;
using TRF3.SISPREC.PDFServices;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ConferirOrgaoPss;
public class ConferirOrgaoPssAppService : BaseAppService, IConferirOrgaoPssAppService
{
    private readonly IConferirOrgaoPssRepository _repository;
    private readonly IPdfFileGeneratorService _pdfService;

    public ConferirOrgaoPssAppService(IConferirOrgaoPssRepository repository, IPdfFileGeneratorService pdfService)
    {
        _repository = repository;
        _pdfService = pdfService;
    }

    public async Task<PagedResultDto<ConferirOrgaoPss>> GetListAsync(ConferirOrgaoPssGetListInput input)
    {
        var query = await _repository.ObterListaOrgaoPss(input.TipoProcedimento, input.Ano, input.Mes);

        int totalCount = await query.CountAsync();

        query = query.OrderByIf<ConferirOrgaoPss, IQueryable<ConferirOrgaoPss>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting)
                    .PageBy(input);

        return new PagedResultDto<ConferirOrgaoPss>
        {
            Items = await query.ToListAsync(),
            TotalCount = totalCount
        };
    }

    public async Task<byte[]> GerarPDF(ConferirOrgaoPssGetListInput input)
    {
        var query = await _repository.ObterListaOrgaoPss(input.TipoProcedimento, input.Ano, input.Mes);

        var dto = new ConferirOrgaoPssPdfDto()
        {
            Filtro = input,
            ConferirOrgaoPss = await query.ToListAsync()
        };

        return await _pdfService.GenerateFileAsync("ConferirOrgaoPssTemplate", dto, "Conferir Órgão PSS");
    }
}
