using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.Analises.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ConfirmarLiberacoes
{
    public class ConfirmarLiberacaoAppService : BaseAppService, IConfirmarLiberacaoAppService
    {
        private readonly IConfirmarLiberacaoRepository _repository;

        public ConfirmarLiberacaoAppService(IConfirmarLiberacaoRepository confirmarLiberacaoRepository)
        {
            _repository = confirmarLiberacaoRepository;
        }

        public async Task<PagedResultDto<RequisicoesPendentes>> GetRequisicoes(AnaliseGetListInput input)
        {
            var requisicoes = await _repository.BuscarRequisicoesPendentesParaLiberacao(
                input.TipoProcedimento,
                input.Ano,
                input.Mes,
                input.DataInicio,
                input.DataTermino,
                input.NumeroProtocoloRequisicao);

            requisicoes = requisicoes.OrderBy(p => p.NumeroProtocoloRequisicaoPendente).ToList();

            return new PagedResultDto<RequisicoesPendentes>
            {
                Items = requisicoes,
                TotalCount = requisicoes.Count
            };
        }

        public async Task<DadosBasicoParaConfirmarLiberacao?> GetInformacaoBasica(string numeroRequisicao)
        {
            var requisicoes = await _repository.BuscaPorInformacaoBasicaRequisicoes(numeroRequisicao);

            return requisicoes;
        }

        public async Task<PagedResultDto<DadosOcorrenciasParaConfirmarLiberacao>> GetOcorrencias(string numeroRequisicao)
        {
            var requisicoes = await _repository.BuscaPorOcorrenciasRequisicoes(numeroRequisicao);

            return new PagedResultDto<DadosOcorrenciasParaConfirmarLiberacao>
            {
                Items = requisicoes,
                TotalCount = requisicoes.Count
            };
        }

        public async Task<PagedResultDto<JustificativasAnalises>> GetJustificastivaAnalises(string numeroRequisicao)
        {
            var requisicoes = await _repository.BuscaPorJustificativasAnalises(numeroRequisicao);

            return new PagedResultDto<JustificativasAnalises>
            {
                Items = requisicoes,
                TotalCount = requisicoes.Count
            };
        }
    }
}
