using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.ConfirmarLiberacoes.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ConfirmarLiberacoes
{
    public class ConfirmarLiberacoesAppService : BaseAppService, IConfirmarLiberacaoAppService
    {
        private readonly IConfirmarLiberacaoRepository _repository;

        public ConfirmarLiberacoesAppService(IConfirmarLiberacaoRepository confirmarLiberacaoRepository)
        {
            _repository = confirmarLiberacaoRepository;
        }

        public async Task<PagedResultDto<RequisicoesPendentes>> GetRequisicoes(ConfirmarcaoLiberacoesGetListInput input)
        {
            var requisicoes = await _repository.BuscarRequisicoesPendentesParaLiberacao(
                                                                 input.TipoProcedimento,
                                                                 input.Ano,
                                                                 input.Mes,
                                                                 input.DataInicio,
                                                                 input.DataTermino,
                                                                 input.NumeroProtocoloRequisicao);

            requisicoes = requisicoes.OrderBy(p => p.NumeroProtocoloRequisicaoPendente).ToList();

            return new PagedResultDto<RequisicoesPendentes>
            {
                Items = requisicoes,
                TotalCount = requisicoes.Count
            };
        }

        public async Task<DadosBasicoParaConfirmarLiberacao?> GetInformacaoBasica(string numeroProtocoloRequisicao)
        {
            var requisicoes = await _repository.BuscaPorInformacaoBasicaRequisicoes(numeroProtocoloRequisicao);

            return requisicoes;
        }

        public async Task<PagedResultDto<DadosOcorrenciasParaConfirmarLiberacao>> GetOcorrencias(string numeroProtocoloRequisicao)
        {
            var requisicoes = await _repository.BuscaPorOcorrenciasRequisicoes(numeroProtocoloRequisicao);

            return new PagedResultDto<DadosOcorrenciasParaConfirmarLiberacao>
            {
                Items = requisicoes,
                TotalCount = requisicoes.Count
            };
        }

        public async Task<PagedResultDto<JustificativasAnalises>> GetJustificastivaAnalises(string numeroProtocoloRequisicao)
        {
            var requisicoes = await _repository.BuscaPorJustificativasAnalises(numeroProtocoloRequisicao);

            return new PagedResultDto<JustificativasAnalises>
            {
                Items = requisicoes,
                TotalCount = requisicoes.Count
            };
        }
    }
}
