using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.AcoesJustificativa;
using TRF3.SISPREC.ConsultaAjuizamentos.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExcelServices;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesPropostas;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ConsultaAjuizamentos
{
    public class ConsultaAjuizamentoAppService : BaseAppService, IConsultaAjuizamentoAppService
    {
        private readonly IRequisicaoPropostaRepository _repository;
        private readonly IExcelFileGeneratorService _excelFileGeneratorService;

        public ConsultaAjuizamentoAppService(IRequisicaoPropostaRepository repository,
                                             IExcelFileGeneratorService excelFileGeneratorService,
                                             IAcaoJustificativaRepository acaoJustificativaRepository)
        {
            _repository = repository ?? throw new ArgumentException(nameof(repository));
            _excelFileGeneratorService = excelFileGeneratorService ?? throw new ArgumentException(nameof(excelFileGeneratorService));
        }

        public async Task<PagedResultDto<ConsultaAjuizamentoDto>> GetListAsync(ConsultaAjuizamentoGetListInputDto input)
        {

            if (input.Ano is null)
            {
                throw new UserFriendlyException("O campo Ano é obrigatório.");
            }

            var query = await GerarFiltro(input);
            return new PagedResultDto<ConsultaAjuizamentoDto>
            {
                Items = await query.Item1.ToListAsync(),
                TotalCount = query.Item2,
            };
        }
        public async Task<byte[]> ExportarExcel(ConsultaAjuizamentoGetListInputDto input)
        {
            input.SkipCount = 0;
            input.MaxResultCount = int.MaxValue;
            var query = await GerarFiltro(input);
            var list = await query.Item1
                    .Select(x => new ConsultaAjuizamentoExcelDto
                    {
                        Procedimento = x.Procedimento,
                        Ano = x.Ano,
                        Mes = x.Mes,
                        StatusProposta = x.StatusProposta,
                        NumeroRequisicao = x.NumeroRequisicao,
                        DataProtocoloOriginario = x.DataProtocoloOriginario.HasValue
                            ? x.DataProtocoloOriginario.Value.ToString("dd/MM/yyyy")
                            : string.Empty,
                        Originario = x.Originario,
                    }).ToListAsync();
            var filtro = new ConsultaAjuizamentoFiltroExcelDto()
            {
                Ano = input.Ano,
                DataProtocoloOrigemLimite = input.DataProtocoloOrigemLimite?.ToString("dd/MM/yyyy"),
                Mes = input.Mes,
                TipoProcedimento = input.TipoProcedimento?.ToString() ?? string.Empty,
                Total = query.Item2
            };

            return _excelFileGeneratorService.GenerateExcelFile(list, EStyleFile.TITULO_NEGRITO, filtro);
        }
        private async Task<(IQueryable<ConsultaAjuizamentoDto>, int)> GerarFiltro(ConsultaAjuizamentoGetListInputDto input)
        {
            var context = await _repository.GetDbContextAsync();
            var query = (
                from requisicaoProposta in context.Set<RequisicaoProposta>().AsNoTracking()
                join proposta in context.Set<Proposta>().AsNoTracking()
                    on requisicaoProposta.PropostaId equals proposta.PropostaId
                join requisicaoProcessoOrigem in context.Set<RequisicaoProcessoOrigem>().AsNoTracking()
                    on requisicaoProposta.NumeroProtocoloRequisicao equals requisicaoProcessoOrigem.NumeroProtocoloRequisicao
                where (input.TipoProcedimento == null || proposta.TipoProcedimentoId == input.TipoProcedimento.ToString()) &&
                      (input.Ano == null || proposta.AnoProposta == input.Ano) &&
                      (input.Mes == null || proposta.MesProposta == input.Mes) &&
                      requisicaoProposta.SituacaoRequisicaoProposta != ESituacaoRequisicaoProposta.CANCELADO &&
                      (input.DataProtocoloOrigemLimite == null || requisicaoProcessoOrigem.DataProtocoloProcessoOriginal <= input.DataProtocoloOrigemLimite)
                select new ConsultaAjuizamentoDto()
                {
                    Procedimento = proposta.TipoProcedimentoId,
                    Ano = proposta.AnoProposta,
                    Mes = proposta.MesProposta,
                    StatusProposta = requisicaoProposta.SituacaoRequisicaoProposta.ToString(),
                    NumeroRequisicao = requisicaoProposta.NumeroProtocoloRequisicao,
                    DataProtocoloOriginario = requisicaoProcessoOrigem.DataProtocoloProcessoOriginal,
                    Originario = requisicaoProcessoOrigem.NumeroProcessoOriginario
                }).AsQueryable();

            int totalCount = await query.CountAsync();

            query = query.OrderByIf<ConsultaAjuizamentoDto, IQueryable<ConsultaAjuizamentoDto>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting)
                         .OrderByIf<ConsultaAjuizamentoDto, IQueryable<ConsultaAjuizamentoDto>>(string.IsNullOrWhiteSpace(input.Sorting), "numeroRequisicao desc")
                         .PageBy(input);

            return (query.AsQueryable(), totalCount);
        }
    }
}
