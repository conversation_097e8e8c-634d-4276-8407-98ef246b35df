using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.ConsultasCnpjAlimenticio;
using TRF3.SISPREC.ConsultasCnpjAlimenticio.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.Pessoas;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesPartesRequerentes;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ConsultaCnpjAlimenticio
{
    public class ConsultaCnpjAlimenticioAppService : BaseAppService, IConsultaCnpjAlimenticioAppService
    {
        private readonly IRequisicaoPropostaRepository _repository;
        private readonly IPdfFileGeneratorService _pdfService;

        public ConsultaCnpjAlimenticioAppService(IRequisicaoPropostaRepository repository, IPdfFileGeneratorService pdfService)
        {
            _repository = repository ?? throw new ArgumentException(nameof(repository));
            _pdfService = pdfService;
        }

        public async Task<PagedResultDto<ConsultaCnpjAlimenticioDto>> GetListAsync(ConsultaCnpjAlimenticioGetListInputDto input)
        {

            if (input.Ano is null)
            {
                throw new UserFriendlyException("O campo Ano é obrigatório.");
            }

            var context = await _repository.GetDbContextAsync();
            var query = (
                from requisicaoProposta in context.Set<RequisicaoProposta>().AsNoTracking()
                join proposta in context.Set<Proposta>().AsNoTracking()
                    on requisicaoProposta.PropostaId equals proposta.PropostaId
                join requisicaoProtocolo in context.Set<RequisicaoProtocolo>().AsNoTracking()
                    on requisicaoProposta.NumeroProtocoloRequisicao equals requisicaoProtocolo.NumeroProtocoloRequisicao
                join requisicaoParte in context.Set<RequisicaoParte>().AsNoTracking()
                    on requisicaoProtocolo.NumeroProtocoloRequisicao equals requisicaoParte.NumeroProtocoloRequisicao
                join requisicaoParteRequerente in context.Set<RequisicaoParteRequerente>().AsNoTracking()
                    on requisicaoParte.RequisicaoParteId equals requisicaoParteRequerente.RequisicaoParteId
                join pessoa in context.Set<Pessoa>().AsNoTracking()
                    on requisicaoParte.PessoaId equals pessoa.PessoaId
                where
                    (input.TipoProcedimento == null || proposta.TipoProcedimentoId == input.TipoProcedimento.ToString()) &&
                    (input.Ano == null || proposta.AnoProposta == input.Ano) &&
                    (input.Mes == null || proposta.MesProposta == input.Mes) &&
                    requisicaoProtocolo.NaturezaCredito == ENaturezaCredito.ALIMENTICIA &&
                    requisicaoProtocolo.TipoHonorario == ETipoHonorario.NAO_SUCUMBENCIAL &&
                    requisicaoProposta.SituacaoRequisicaoProposta != ESituacaoRequisicaoProposta.CANCELADO &&
                    pessoa.NumeroCnpjCpf.Length != 11
                select new ConsultaCnpjAlimenticioDto
                {
                    NomePessoa = pessoa.Nome,
                    NumeroCnpj = pessoa.NumeroCnpjCpf,
                    NumeroProtocolo = requisicaoProposta.NumeroProtocoloRequisicao
                }).AsQueryable();

            var count = await query.CountAsync();

            query = query.OrderByIf<ConsultaCnpjAlimenticioDto, IQueryable<ConsultaCnpjAlimenticioDto>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting)
                         .OrderByIf<ConsultaCnpjAlimenticioDto, IQueryable<ConsultaCnpjAlimenticioDto>>(string.IsNullOrWhiteSpace(input.Sorting), "numeroProtocolo desc")
                         .PageBy(input)
                         .AsQueryable();

            return new PagedResultDto<ConsultaCnpjAlimenticioDto>
            {
                TotalCount = count,
                Items = await query.ToListAsync()
            };
        }

        public async Task<byte[]> GerarPDF(ConsultaCnpjAlimenticioGetListInputDto input)
        {
            var list = await GetListAsync(input);

            var dto = new ConsultaCnpjAlimenticioPdfDto()
            {
                Filtro = input,
                ListConsultaCnpjAlimenticio = list.Items.ToList(),
                Usuario = CurrentUser.UserName ?? string.Empty
            };

            return await _pdfService.GenerateFileAsync("ConsultaCnpjAlimenticioTemplate", dto, "REQUISIÇÕES COM CNPJ E NATUREZA ALIMENTÍCIA");
        }
    }
}