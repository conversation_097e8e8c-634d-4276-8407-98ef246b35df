using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.ConsultaComplementares.Dtos;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.RequisicoesPropostas;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ConsultaComplementares
{
    public class ConsultaComplementaresAppService : BaseAppService, IConsultaComplementaresAppService
    {
        public const string AnoExcecao = "O campo Ano é obrigatório.";
        private readonly IConsultaRequisicaoRepository _repository;
        private readonly IPdfFileGeneratorService _pdfService;

        public ConsultaComplementaresAppService(IConsultaRequisicaoRepository consultaRequisicaoRepository, IPdfFileGeneratorService pdfService)
        {
            _repository = consultaRequisicaoRepository;
            _pdfService = pdfService;
        }

        public async Task<PagedResultDto<ConsultaComplementar>> GetListAsync(ConsultaComplementaresGetListInputDto input)
        {
            if (input.Ano is null)
            {
                throw new UserFriendlyException(AnoExcecao);
            }

            IQueryable<ConsultaComplementar> query = (await _repository.GetQueryConsultaComplementares(input.TipoProcedimento.ToString(), input.Ano, input.Mes))!;

            int totalCount = await query.CountAsync();

            query = query.OrderByIf<ConsultaComplementar, IQueryable<ConsultaComplementar>>(!string.IsNullOrWhiteSpace(input.Sorting), !string.IsNullOrEmpty(input.Sorting) ? input.Sorting : string.Empty)
                         .OrderByIf<ConsultaComplementar, IQueryable<ConsultaComplementar>>(string.IsNullOrWhiteSpace(input.Sorting), "numeroProtocoloRequisicao desc")
                         .PageBy(input);

            return new PagedResultDto<ConsultaComplementar>
            {
                TotalCount = totalCount,
                Items = await query.ToListAsync()
            };
        }

        public async Task<byte[]> GerarPDF(ConsultaComplementaresGetListInputDto input)
        {

            if (input.Ano is null)
            {
                throw new UserFriendlyException(AnoExcecao);
            }

            var list = await _repository.GetQueryConsultaComplementares(input.TipoProcedimento.ToString(), input.Ano, input.Mes);

            var listConsultaComplementares = await list.ToListAsync();

            var mapListConsultaComplementares = ObjectMapper.Map<List<ConsultaComplementar>, List<ConsultaComplementaresDto>>(listConsultaComplementares);

            var dto = new ConsultaComplementaresPdfDto()
            {
                Filtro = input,
                ListConsultaComplementares = mapListConsultaComplementares,
                Usuario = CurrentUser.UserName ?? string.Empty
            };

            return await _pdfService.GenerateFileAsync("ConsultaComplementaresTemplate", dto, "REQUISIÇÕES COMPLEMENTARES");
        }
    }
}

