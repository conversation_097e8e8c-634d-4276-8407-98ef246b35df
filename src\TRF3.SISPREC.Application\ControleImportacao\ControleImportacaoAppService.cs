using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.ControleImportacoes.Dtos;
using TRF3.SISPREC.ImportacaoRequisicoes.Jobs;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Pessoas.SinPessoasReqPag;
using TRF3.SISPREC.Settings;
using TRF3.SISPREC.Settings.Importacoes;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC.ControleImportacoes
{
    public class ControleImportacaoAppService : SISPRECBaseSettingsAppService, IControleImportacaoAppService
    {
        private readonly ISinPessoaReqPagManager _sinPessoaReqPagManager;
        private readonly IImportacoesSettingsAppService _importacaoRequisicoesSettingsAppService;
        private readonly IBackgroundJobManager _backgroundJobManager;
        public ControleImportacaoAppService
            (ISettingManager settingManager,
            IBackgroundJobsService backgroundJobsService,
            ISinPessoaReqPagManager sinPessoaReqPagManager,
            IImportacoesSettingsAppService importacaoRequisicoesSettingsAppService,
            IBackgroundJobManager backgroundJobManager) : base(settingManager, backgroundJobsService)
        {
            _sinPessoaReqPagManager = sinPessoaReqPagManager;
            _importacaoRequisicoesSettingsAppService = importacaoRequisicoesSettingsAppService;
            _importacaoRequisicoesSettingsAppService.IsConfiguracaoAtiva(ImportacaoRequisicoesSettings.ImportacaoAtiva);
            _importacaoRequisicoesSettingsAppService.HabilitarImportacaoRequisicaoManual();
            _backgroundJobManager = backgroundJobManager;
        }

        public async Task GetInserirControle(string numeroProtocolo)
        {
            try
            {
                var proposta = await _sinPessoaReqPagManager.ValidaProtocolo(numeroProtocolo);

                _backgroundJobManager.EnqueueAsync(new ImportarRequisicoesManualArgs(proposta.PropostaId, numeroProtocolo), BackgroundJobPriority.High)
                    .Wait(10);
            }
            catch (Exception)
            {
                throw;
            }

        }

        public async Task<PagedResultDto<ControleImportacaoRequisicaoDto>> GetObterListaErros(ViewErroRequisicaoFilterInput input)
        {
            try
            {
                var listaErros = await _sinPessoaReqPagManager.VerificaErrosImportacao();
                var filtro = listaErros.WhereIf(input.NumProtocoloRequis != null, x => x.NumeroProtocoloRequisicao == input.NumProtocoloRequis)
                    .WhereIf(input.DescricaoErro != null, x => x.ControleImportacaoRequisicoesErro.Any(e => e.Descricao.Contains(input.DescricaoErro)));

                var requerentes = ObjectMapper.Map<List<ControleImportacaoRequisicao>, List<ControleImportacaoRequisicaoDto>>(filtro.ToList());
                return new PagedResultDto<ControleImportacaoRequisicaoDto>
                {
                    TotalCount = filtro.Count(),
                    Items = requerentes
                };
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<ControleImportacaoRequisicao> UpdateAsync(string? numeroProtocoloRequisicao)
        {
            if (numeroProtocoloRequisicao == null)
                throw new UserFriendlyException("Não é possivel alterar valores para meses anteriores ao atual.");


            return await _sinPessoaReqPagManager.AtualizaControleImportacaoRequisicao(numeroProtocoloRequisicao);
        }

    }
}
