using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using TRF3.SISPREC.ControleProcessamentos;
using TRF3.SISPREC.Controles;
using TRF3.SISPREC.Controles.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Fases;
using TRF3.SISPREC.FileUtils;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.MdbReaders;
using TRF3.SISPREC.MdbReaders.Models;
using TRF3.SISPREC.ProcessaPrecatorio.ConfiguracaoServicos;
using TRF3.SISPREC.TiposEtapaProcessamentos;
using Volo.Abp;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.MdbReaderAppService
{
    [RequestSizeLimit(10L * 1024L * 1024L * 1024L)]
    public class ControleAppService : BaseAppService, IControleAppService
    {
        #region Read-Only Fields

        private readonly IUnitOfWorkManager _unitOfWork;
        private readonly IControleProcessamentoRepository _controleProcessamentoRepository;
        private readonly IBackgroundWorkersService _backgroundWorkersService;
        private readonly IFileUtil _fileUtils;
        private readonly IFileSystem _fileSystem;
        private readonly IMdbValidador _mdbReader;
        private readonly IMapper _mapper;
        private readonly ITipoEtapaProcessamentoRepository _tipoEtapaProcessamentoRepository;
        private readonly IControleProcessamentoManager _controleProcessamentoManager;
        private readonly IFaseRepository _faseRepository;

        #endregion

        #region Constructors

        public ControleAppService(IUnitOfWorkManager unitOfWork,
                                  IControleProcessamentoRepository controleProcessamento,
                                  IBackgroundWorkersService backgroundWorkersService,
                                  IFileUtil fileUtils,
                                  IFileSystem fileSystem,
                                  IMdbValidador mdbReader,
                                  IMapper mapper,
                                  ITipoEtapaProcessamentoRepository tipoEtapaProcessamentoRepository,
                                  IControleProcessamentoManager controleProcessamentoManager,
                                  IFaseRepository faseRepository)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentException(nameof(unitOfWork));
            _controleProcessamentoRepository = controleProcessamento ?? throw new ArgumentException(nameof(controleProcessamento));
            _backgroundWorkersService = backgroundWorkersService ?? throw new ArgumentException(nameof(backgroundWorkersService));
            _fileUtils = fileUtils ?? throw new ArgumentException(nameof(fileUtils));
            _fileSystem = fileSystem ?? throw new ArgumentException(nameof(fileSystem));
            _mdbReader = mdbReader ?? throw new ArgumentException(nameof(_mdbReader));
            _mapper = mapper ?? throw new ArgumentException(nameof(mapper));
            _tipoEtapaProcessamentoRepository = tipoEtapaProcessamentoRepository ?? throw new ArgumentException(nameof(tipoEtapaProcessamentoRepository));
            _controleProcessamentoManager = controleProcessamentoManager ?? throw new ArgumentException(nameof(controleProcessamentoManager));
            _faseRepository = faseRepository;
        }

        #endregion

        #region ControleAppService Members

        public async Task<string> Upload(IRemoteStreamContent streamContent, int faseId)
        {

            //valida tamanho do arquivo
            if (streamContent.ContentLength >= 1024000000)
                throw new UserFriendlyException("Não é permitido arquivo com tamanho superior a 1GB");

            if (await _faseRepository.ValidarFaseUpload(faseId))
                throw new InvalidOperationException("Não é possível fazer upload para esta fase, pois já existe controle ativo");

            if (!await _faseRepository.ValidaSeFaseAberta(faseId))
                throw new UserFriendlyException("Não é possível fazer upload para uma fase fechada.");

            //valida extensão do arquivo.
            var fileInfo = new FileInfo(streamContent.FileName);
            if (fileInfo.Extension != ".mdb")
                throw new UserFriendlyException("A extensão do arquivo deve ser tipo .mdb");

            //define um caminho em uma pasta temporária.
            return await _fileUtils.CriarArquivo(streamContent, "arquivos_mdb", "temp");
        }

        public async Task<List<VerificarTabelasDto>> AnalisaArquivoMdbAsync(string caminhoArquivo, int faseId)
        {
            if (!await _faseRepository.ValidaSeFaseAberta(faseId))
                throw new UserFriendlyException("Não é possivel fazer upload para uma fase fechada.");

            var fase = await _faseRepository.GetAsync(s => s.Id == faseId);
            var resultado = await _mdbReader.AnalisaArquivoMdbAsync(caminhoArquivo, fase.TipoPrecatorio, fase.FaseTipo.Codigo);
            return _mapper.Map<List<VerificarTabelas>, List<VerificarTabelasDto>>(resultado);
        }

        public async Task ImportarMdb(ControleProcessamentoDto inputDto)
        {
            using (var uow = _unitOfWork.Begin(requiresNew: true, isTransactional: true))
            {

                var etapaId = await _tipoEtapaProcessamentoRepository.FirstOrDefaultAsync(x => x.Etapa == ETipoEtapaProcessamento.Extracao);

                // Adiciona a entidade ao contexto (não salva ainda)
                if (etapaId is null)
                    throw new UserFriendlyException("Erro ao obter id da etapa do tipo extração");

                var ctrl = new ControleProcessamento()
                {
                    EtapaId = etapaId.Id,
                    FaseId = inputDto.FaseId
                };
                await _controleProcessamentoRepository.InsertAsync(ctrl);

                if (!_fileSystem.FileExists(inputDto.CaminhoArquivo))
                    throw new UserFriendlyException("Arquivo não encontrado no caminho indicado");

                ctrl.Arquivo.CaminhoArquivo = _fileUtils.MoverArquivo(inputDto.CaminhoArquivo, "arquivos_mdb", $"{Guid.NewGuid().ToString()}");

                // Salva as alterações no banco de dados
                await uow.CompleteAsync();
            }
        }

        public void CancelarUpload(string caminhoArquivo)
        {
            _fileUtils.DeletarArquivo(caminhoArquivo);
        }

        public async Task ReprocessarEtapaAtual(int controleProcessamentoId)
        {
            await _controleProcessamentoManager.AlterarStatusParaReprocessarEtapaAsync(controleProcessamentoId);
        }

        public async Task MoverParaProximaEtapa(int controleProcessamentoId)
        {
            await _controleProcessamentoManager.MoverParaProximaEtapaAsync(controleProcessamentoId);
        }

        public async Task PausarControle(int controleProcessamentoId)
        {
            bool servicoProcessamentoEtapaAtivo = ExisteJobAtivo(controleProcessamentoId);
            await _controleProcessamentoManager.PausarControle(controleProcessamentoId, servicoProcessamentoEtapaAtivo);
        }

        public async Task RetomarControle(int controleProcessamentoId)
        {
            await _controleProcessamentoManager.RetomarControle(controleProcessamentoId);

            await AtivarProcessamentoEtapaPorControleId(controleProcessamentoId);
        }

        public async Task CancelarControle(int controleProcessamentoId)
        {
            bool servicoProcessamentoEtapaAtivo = ExisteJobAtivo(controleProcessamentoId);
            await _controleProcessamentoManager.CancelarControle(controleProcessamentoId, servicoProcessamentoEtapaAtivo);
        }

        public async Task ValidarFaseAberta(int faseId)
        {
            if (!await _faseRepository.ValidaSeFaseAberta(faseId))
                throw new UserFriendlyException("Não é possivel fazer upload para uma fase fechada.");
        }

        #endregion

        #region Private Methods 

        private bool ExisteJobAtivo(int controleId)
        {
            var etapa = RetornaTipoEtapaProcessamento(controleId);

            return _backgroundWorkersService.ObterBackgroundWorker(ServicosBackground.ObterTipoServicoDaEtapa(etapa)).IsAtivo();
        }

        private async Task AtivarProcessamentoEtapaPorControleId(int controleId)
        {
            var etapa = RetornaTipoEtapaProcessamento(controleId);

            IBaseBackgroundWorker worker = _backgroundWorkersService.ObterBackgroundWorker(ServicosBackground.ObterTipoServicoDaEtapa(etapa));
            worker.Retomar();
        }

        private ETipoEtapaProcessamento RetornaTipoEtapaProcessamento(int controleId)
        {
            var etapa = _controleProcessamentoRepository.GetAsync(x => x.Id == controleId).Result?.EtapaId;

            if (etapa is null)
                throw new UserFriendlyException("Etapa não encontrada para o ID controle informado.");

            return (ETipoEtapaProcessamento)etapa;
        }

        #endregion
    }
}
