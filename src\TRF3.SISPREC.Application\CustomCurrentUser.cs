using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;
using Volo.Abp.Users;

namespace TRF3.SISPREC;

[ExcludeFromCodeCoverage]
[Dependency(ServiceLifetime.Transient, ReplaceServices = true)]
[ExposeServices(typeof(ICurrentUser))]
public class CustomCurrentUser : CurrentUser
{
    public CustomCurrentUser(ICurrentPrincipalAccessor principalAccessor) : base(principalAccessor)
    {
    }

    public override string? UserName => String.IsNullOrWhiteSpace(Email?.Split("@")[0]) ? null : Email?.Split("@")[0];

    public override bool IsAuthenticated => UserName?.Length > 3;
}
