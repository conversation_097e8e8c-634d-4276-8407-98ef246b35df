using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.DespesaClassificacoes.Dtos;
using TRF3.SISPREC.Permissoes;

namespace TRF3.SISPREC.DespesaClassificacoes;

public class DespesaClassificacaoAppService : BaseReadOnlyAppService<DespesaClassificacao, DespesaClassificacaoDto, int, DespesaClassificacaoGetListInput>, IDespesaClassificacaoAppService
{
    private readonly IDespesaClassificacaoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.DespesaClassificacao.Visualizar;

    public DespesaClassificacaoAppService(IDespesaClassificacaoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<DespesaClassificacao> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Classi_Despesa == id
            ));
    }

    protected override IQueryable<DespesaClassificacao> ApplyDefaultSorting(IQueryable<DespesaClassificacao> query)
    {
        return query.OrderBy(e => e.Seq_Classi_Despesa);
    }

    protected override async Task<IQueryable<DespesaClassificacao>> CreateFilteredQueryAsync(DespesaClassificacaoGetListInput input)
    {
        bool? alimentar = null;

        if (input.Alimentar != null)
            alimentar = input.Alimentar.Equals("1") ? true : false;

        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.Seq_Classi_Despesa > 0, x => x.Seq_Classi_Despesa == input.Seq_Classi_Despesa)
            .WhereIf(input.Alimentar != null, x => x.Alimentar == alimentar)
            .WhereIf(input.Seq_CJF != null, x => x.Seq_CJF == input.Seq_CJF)
            .WhereIf(input.DataUtilizacaoFim != null, x => x.DataUtilizacaoFim == input.DataUtilizacaoFim)
            .WhereIf(input.DespesaNaturezaId > 0, x => x.DespesaNaturezaId == input.DespesaNaturezaId)
            .WhereIf(input.DespesaTipoId > 0, x => x.DespesaTipoId == input.DespesaTipoId)
            .WhereIf(input.DespesaNaturezaId > 0, x => x.DespesaNatureza.Seq_Nature_Despesa == input.DespesaNaturezaId)
            .WhereIf(input.DespesaTipoId > 0, x => x.DespesaTipoId == input.DespesaTipoId)
            ;
    }

    public async Task<IEnumerable<DespesaClassificacaoDto>> GetListarTodosAsync(DespesaClassificacaoGetListInput input)
    {
        bool? alimentar = null;

        if (input.Alimentar != null)
            alimentar = input.Alimentar.Equals("1") ? true : false;

        var result = (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.DespesaNatureza)
            .Include(x => x.DespesaTipo)
            .WhereIf(input.Seq_Classi_Despesa > 0, x => x.Seq_Classi_Despesa == input.Seq_Classi_Despesa)
            .WhereIf(input.Alimentar != null, x => x.Alimentar == alimentar)
            .WhereIf(input.Seq_CJF != null, x => x.Seq_CJF == input.Seq_CJF)
            .WhereIf(input.DataUtilizacaoFim != null, x => x.DataUtilizacaoFim == input.DataUtilizacaoFim)
            .WhereIf(input.DespesaNaturezaId > 0, x => x.DespesaNaturezaId == input.DespesaNaturezaId)
            .WhereIf(input.DespesaTipoId > 0, x => x.DespesaTipoId == input.DespesaTipoId)
            .WhereIf(input.DespesaNaturezaId > 0, x => x.DespesaNatureza.Seq_Nature_Despesa == input.DespesaNaturezaId)
            .WhereIf(input.DespesaTipoId > 0, x => x.DespesaTipoId == input.DespesaTipoId)
            .ToList()
            ;

        return ObjectMapper.Map<List<DespesaClassificacao>, List<DespesaClassificacaoDto>>(result);
    }
}