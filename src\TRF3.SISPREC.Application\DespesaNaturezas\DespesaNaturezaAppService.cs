using TRF3.SISPREC.DespesaNaturezas.Dtos;
using TRF3.SISPREC.Permissoes;

namespace TRF3.SISPREC.DespesaNaturezas;

public class DespesaNaturezaAppService : BaseReadOnlyAppService<DespesaNatureza, DespesaNaturezaDto, DespesaNaturezaKey, DespesaNaturezaGetListInput>, IDespesaNaturezaAppService
{
    private readonly IDespesaNaturezaRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.DespesaNatureza.Visualizar;

    public DespesaNaturezaAppService(IDespesaNaturezaRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<DespesaNatureza> GetEntityByIdAsync(DespesaNaturezaKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Nature_Despesa == id.Seq_Nature_Despesa
            ));
    }

    protected override IQueryable<DespesaNatureza> ApplyDefaultSorting(IQueryable<DespesaNatureza> query)
    {
        return query.OrderBy(e => e.Seq_Nature_Despesa);
    }

    protected override async Task<IQueryable<DespesaNatureza>> CreateFilteredQueryAsync(DespesaNaturezaGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.Codigo != null, x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}