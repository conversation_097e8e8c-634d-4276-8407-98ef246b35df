using TRF3.SISPREC.DespesaTipos.Dtos;
using TRF3.SISPREC.Permissoes;

namespace TRF3.SISPREC.DespesaTipos;

public class DespesaTipoAppService : BaseReadOnlyAppService<DespesaTipo, DespesaTipoDto, DespesaTipoKey, DespesaTipoGetListInput>, IDespesaTipoAppService
{
    private readonly IDespesaTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.DespesaTipo.Visualizar;

    public DespesaTipoAppService(IDespesaTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<DespesaTipo> GetEntityByIdAsync(DespesaTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Tipo_Despesa == id.Seq_Tipo_Despesa
            ));
    }

    protected override IQueryable<DespesaTipo> ApplyDefaultSorting(IQueryable<DespesaTipo> query)
    {
        return query.OrderBy(e => e.Seq_Tipo_Despesa);
    }

    protected override async Task<IQueryable<DespesaTipo>> CreateFilteredQueryAsync(DespesaTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}