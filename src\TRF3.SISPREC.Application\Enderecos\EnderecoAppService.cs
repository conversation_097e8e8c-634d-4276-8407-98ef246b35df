using TRF3.SISPREC.Enderecos.Dtos;
using TRF3.SISPREC.Municipios;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.Enderecos;
public class EnderecoAppService : AbstractKeyReadOnlyAppService<Municipio, MunicipioDto, int, MunicipioGetListInput>, IEnderecoAppService
{
    private readonly IMunicipioRepository _municipiorepository;

    public EnderecoAppService(IMunicipioRepository repository) : base(repository)
    {
        _municipiorepository = repository;
    }

    public async Task<IEnumerable<MunicipioDto>> GetCidadePorNome(string nomeCidade)
    {
        var cidades = await AsyncExecuter.ToListAsync(
            (await _municipiorepository.WithDetailsAsync()).Where(e => e.Nome.Contains(nomeCidade)).OrderBy(e => e.Nome)
           );

        return await MapToGetListOutputDtosAsync(cidades);
    }

    public async Task<IEnumerable<MunicipioDto>> GetCidadesPorUF(string uf)
    {
        var cidades = await AsyncExecuter.ToListAsync(
            (await _municipiorepository.WithDetailsAsync()).Where(e => e.SiglaUF == uf).OrderBy(e => e.SiglaUF)
            );

        return await MapToGetListOutputDtosAsync(cidades);
    }

    public async Task<List<MunicipioDto>> GetListarTodasCidades()
    {
        await CheckGetListPolicyAsync();

        var query = await _municipiorepository.GetQueryableAsync();
        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = new List<Municipio>();
        var entityDtos = new List<MunicipioDto>();

        if (totalCount > 0)
        {
            entities = await AsyncExecuter.ToListAsync(query);
            entityDtos = await MapToGetListOutputDtosAsync(entities);
        }

        return entityDtos;
    }

    protected override Task<Municipio> GetEntityByIdAsync(int id)
    {
        throw new NotImplementedException();
    }
}
