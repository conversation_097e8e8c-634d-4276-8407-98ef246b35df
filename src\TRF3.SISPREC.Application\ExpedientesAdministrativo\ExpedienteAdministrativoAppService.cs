using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.ObjectMapping;

namespace TRF3.SISPREC.ExpedientesAdministrativo
{
    public class ExpedienteAdministrativoAppService : BaseAppService, IExpedienteAdministrativoAppService
    {
        #region Read-Only Fields

        private readonly IExpedienteAdministrativoRepository _repository;
        private readonly IExpedienteAdministrativoManager _manager;
        private readonly IObjectMapper _mapper;

        #endregion

        #region Constructors

        public ExpedienteAdministrativoAppService(IExpedienteAdministrativoRepository repository, IObjectMapper objectMapper, IExpedienteAdministrativoManager manager)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
            _mapper = objectMapper ?? throw new ArgumentNullException(nameof(objectMapper));
        }

        #endregion

        #region IExpedienteAdministrativoAppService Members

        public async Task<PagedResultDto<ExpedienteAdministrativoDto>> GetExpedientes(ExpedienteAdministrativoGetListInput input)
        {
            var query = (await _repository.GetQueryableAsync())
                            .Include(x => x.RequisicaoExpedienteAdministrativo)
                            .WhereIf(input.NumeroProcessoSei != null, x => x.NumeroProcessoSei == input.NumeroProcessoSei)
                            .WhereIf(input.TipoExpedienteAdministrativo != null, x => x.TipoExpedienteAdministrativo == input.TipoExpedienteAdministrativo)
                            .WhereIf(!input.NomeUsuario.IsNullOrWhiteSpace(), x => x.NomeUsuario.Contains(input.NomeUsuario))
                            .WhereIf(input.StatusExpedienteAdminstrativo != null, x => x.StatusExpedienteAdminstrativo == input.StatusExpedienteAdminstrativo)
                            .WhereIf(input.BlocoSisprecId != null, x => x.BlocoSisprecId == input.BlocoSisprecId)
                            .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoExpedienteAdministrativo.Any(r => r.NumeroProtocoloRequisicao == input.NumeroRequisicao))
                            .Select(x => new ExpedienteAdministrativo()
                            {
                                NumeroProcessoSei = x.NumeroProcessoSei,
                                TipoExpedienteAdministrativo = x.TipoExpedienteAdministrativo,
                                DataExpedienteAdministrativo = x.DataExpedienteAdministrativo,
                                NomeUsuario = x.NomeUsuario,
                                ObservacaoExpedienteAdministrativo = x.ObservacaoExpedienteAdministrativo,
                                StatusExpedienteAdminstrativo = x.StatusExpedienteAdminstrativo,
                                BlocoSisprecId = x.BlocoSisprecId,
                                ExpedienteAdministrativoId = x.ExpedienteAdministrativoId
                            })
                            .Distinct()
                            .OrderByDescending(x => x.DataExpedienteAdministrativo)
                            .ThenByDescending(x => x.NumeroProcessoSei)
                            .AsQueryable();

            var totalCount = await query.CountAsync();
            query = query.OrderByIf<ExpedienteAdministrativo, IQueryable<ExpedienteAdministrativo>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting).PageBy(input);
            var dto = _mapper.Map<List<ExpedienteAdministrativo>, List<ExpedienteAdministrativoDto>>(query.ToList());
            return new PagedResultDto<ExpedienteAdministrativoDto>
            {
                Items = dto.ToList(),
                TotalCount = totalCount
            };
        }

        public async Task IncluirExpedienteEmBlocoExistente(CreateSemBlocoDto blocoDto)
        {
            foreach (var expedienteId in blocoDto.ExpedienteId)
                await _manager.IncluirExpedienteEmBlocoExistenteAsync(expedienteId, blocoDto.BlocoId);
        }

        public async Task GerarBloco(List<SemBlocosDto> blocosDto)
        {
            var tiposUnicos = blocosDto
                .Select(x => x.Tipo.ToLower())
                .Distinct()
                .ToList();

            if (tiposUnicos.Count > 1)
                throw new UserFriendlyException("Para geração do bloco, todos os expedientes devem ser do mesmo Tipo.");

            await _manager.GerarBlocoAsync(blocosDto.Select(x => x.ExpedienteAdministrativoId).ToList());
        }

        public async Task<PagedResultDto<ExpedienteAdministrativoSemBlocoDto>> GetExpedientesSemBloco(ExpedienteAdministrativoSemBlocoGetListInput input)
        {
            #region Query Expediente Administrativo

            var query = (await _repository.GetQueryableAsync())
                    .Include(x => x.RequisicaoExpedienteAdministrativo)
                .WhereIf(input.NumeroProcessoSei != null, x => x.NumeroProcessoSei == input.NumeroProcessoSei)
                .WhereIf(input.TipoExpedienteAdministrativo != null, x => x.TipoExpedienteAdministrativo == input.TipoExpedienteAdministrativo)
                .WhereIf(!input.NomeUsuario.IsNullOrWhiteSpace(), x => x.NomeUsuario.Contains(input.NomeUsuario))
                .WhereIf(!input.NumeroRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoExpedienteAdministrativo.Any(r => r.NumeroProtocoloRequisicao == input.NumeroRequisicao))
                .Where(x => x.BlocoSisprecId == null && x.NumeroExpedienteAdministrativo == null)
                .Select(g => new ExpedienteAdministrativo
                {
                    NumeroProcessoSei = g.NumeroProcessoSei,
                    TipoExpedienteAdministrativo = g.TipoExpedienteAdministrativo,
                    DataExpedienteAdministrativo = g.DataExpedienteAdministrativo,
                    NomeUsuario = g.NomeUsuario,
                    ObservacaoExpedienteAdministrativo = g.ObservacaoExpedienteAdministrativo,
                    StatusExpedienteAdminstrativo = g.StatusExpedienteAdminstrativo,
                    BlocoSisprecId = g.BlocoSisprecId,
                    ExpedienteAdministrativoId = g.ExpedienteAdministrativoId
                })
                .Distinct()
                .AsQueryable();

            #endregion

            var totalCount = await query.CountAsync();
            query = query.OrderByIf<ExpedienteAdministrativo, IQueryable<ExpedienteAdministrativo>>(!string.IsNullOrWhiteSpace(input.Sorting), input.Sorting).PageBy(input);
            var dto = _mapper.Map<List<ExpedienteAdministrativo>, List<ExpedienteAdministrativoSemBlocoDto>>(query.ToList());
            return new PagedResultDto<ExpedienteAdministrativoSemBlocoDto>
            {
                Items = dto.ToList(),
                TotalCount = totalCount
            };
        }

        #endregion
    }
}
