using TRF3.SISPREC.FaseTipos.Dtos;
using TRF3.SISPREC.Permissoes;

namespace TRF3.SISPREC.FaseTipos;

public class FaseTipoAppService : BaseReadOnlyAppService<FaseTipo, FaseTipoDto, FaseTipoKey, FaseTipoGetListInput>, IFaseTipoAppService
{
    private readonly IFaseTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.FaseTipo.Visualizar;

    public FaseTipoAppService(IFaseTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<FaseTipo> GetEntityByIdAsync(FaseTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Id == id.Id
            ));
    }

    protected override IQueryable<FaseTipo> ApplyDefaultSorting(IQueryable<FaseTipo> query)
    {
        return query.OrderBy(e => e.Id);
    }

    protected override async Task<IQueryable<FaseTipo>> CreateFilteredQueryAsync(FaseTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.Codigo != null, x => x.Codigo == input.Codigo)
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}