using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Quartz;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundJobs;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.Settings.Importacoes;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.ImportacaoRequisicoes.Jobs;

[ExcludeFromCodeCoverage]
[DisallowConcurrentExecution]
public class EnfileiraImportacaoControleRequisicaoPeriodicoJob : SchedulingBackroundJob<EnfileiraImportacaoControleRequisicaoPeriodicoJob>, IEnfileiraImportacaoControleRequisicaoPeriodicoJob
{
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly IPropostaRepository _propostaRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public override string JobName => ImportacaoRequisicoesSettings.EnfileiraImportacaoControleRequisicaoJobName;
    public override string JobGroupName => ImportacaoRequisicoesSettings.EnfileiraImportacaoControleRequisicaoJobGroupName;

    public EnfileiraImportacaoControleRequisicaoPeriodicoJob(IGetLoggerService getLoggerService, IScheduler scheduler, IBackgroundJobManager backgroundJobManager, IPropostaRepository propostaRepository, IUnitOfWorkManager unitOfWorkManager) : base(getLoggerService, scheduler)
    {
        _backgroundJobManager = backgroundJobManager;
        _propostaRepository = propostaRepository;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public async override Task Execute(IJobExecutionContext context)
    {
        try
        {
            using (var uow = _unitOfWorkManager.Begin(new AbpUnitOfWorkOptions(isTransactional: true, timeout: 2 * 60 * 1000), requiresNew: true))
            {
                Logger.LogInformation(">>>> Executando Importar Controle de Requisições");

                var propostaPraImportar = (await _propostaRepository.GetQueryableAsync()).Include(x => x.TipoProcedimento).Where(p => p.SituacaoProposta == Enums.ESituacaoProposta.PENDENTE);

                foreach (var proposta in propostaPraImportar)
                {
                    if (context.CancellationToken.IsCancellationRequested)
                    {
                        Logger.LogWarning($"Job EnfileiraImportacaoControleRequisicaoPeriodicoJob foi interrompido.");
                        await uow.CompleteAsync();
                        return;
                    }
                    await _backgroundJobManager.EnqueueAsync(new ImportacaoControleArgs((long)proposta.PropostaId));
                }

                await uow.CompleteAsync();
            }
        }
        catch (OperationCanceledException)
        {
            Logger.LogWarning("Job EnfileiraImportacaoControleRequisicaoPeriodicoJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError($"Erro ao executar EnfileiraImportacaoControleRequisicaoPeriodicoJob. Exceção: {nameof(ex)}. Exceção: {nameof(ex)} - {ex.Message}");
        }
    }
}
