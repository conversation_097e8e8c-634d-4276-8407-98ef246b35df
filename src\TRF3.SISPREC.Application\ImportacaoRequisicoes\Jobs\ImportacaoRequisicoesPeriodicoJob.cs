using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Quartz;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundJobs;
using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.Settings.Importacoes;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.ImportacaoRequisicoes.Jobs;

[ExcludeFromCodeCoverage]
[DisallowConcurrentExecution]
public class ImportacaoRequisicoesPeriodicoJob : SchedulingBackroundJob<ImportacaoRequisicoesPeriodicoJob>, IImportacaoRequisicoesPeriodicoJob
{
    private readonly IControleImportacaoRequisicaoRepository _controleRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IImportarRequisicoesService _importarRequisicoesService;
    public override string JobName => ImportacaoRequisicoesSettings.ImportacaoRequisicoesPeriodicoJobName;
    public override string JobGroupName => ImportacaoRequisicoesSettings.ImportacaoRequisicoesPeriodicoJobGroupName;
    protected override int IntervalInSeconds => 60 * 30;

    public ImportacaoRequisicoesPeriodicoJob(IGetLoggerService getLoggerService, IScheduler scheduler, IControleImportacaoRequisicaoRepository controleRepository, IUnitOfWorkManager unitOfWorkManager, IImportarRequisicoesService importarRequisicoesService) : base(getLoggerService, scheduler)
    {
        _controleRepository = controleRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _importarRequisicoesService = importarRequisicoesService;
    }

    public async override Task Execute(IJobExecutionContext context)
    {
        Logger.LogInformation(">>>> ImportacaoRequisicoesPeriodicoJob: Iniciando... ");

        List<ControleImportacaoRequisicao> controlesParaImportar = new();

        using (var uow = _unitOfWorkManager.Begin())
        using (_controleRepository.DisableTracking())
        {
            controlesParaImportar = await (await _controleRepository.GetQueryableAsync())
                                .Where(p => p.Status == Enums.EStatusImportacao.PENDENTE)
                                .ToListAsync();
            await uow.CompleteAsync();
        }

        try
        {
            foreach (var controle in controlesParaImportar)
            {
                /*
                 * Há uma nova transação para a importação da requisição que é transacional=true
                 * 
                 * Esta abaixo não é transacional, para que, em caso de exceção, sejam salvos os erros no registro de controle.
                 */
                using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false, timeout: 5 * 60 * 1000))
                {
                    context.CancellationToken.ThrowIfCancellationRequested();

                    await _importarRequisicoesService.ImportarAsync(controle.PropostaId, controle.NumeroProtocoloRequisicao);
                    await uow.CompleteAsync();
                }
            }

            Logger.LogInformation(">>>> ImportacaoRequisicoesPeriodicoJob: Finalizada! ");
        }
        catch (OperationCanceledException)
        {
            Logger.LogWarning("Job ImportacaoRequisicoesPeriodicoJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar ImportacaoRequisicoesPeriodicoJob");
        }
    }

}
