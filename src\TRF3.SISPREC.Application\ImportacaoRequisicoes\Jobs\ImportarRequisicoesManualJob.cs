using Microsoft.Extensions.Logging;
using Quartz;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.RequisicoesProtocolos;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Threading;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.ImportacaoRequisicoes.Jobs;

[ExcludeFromCodeCoverage]
[DisallowConcurrentExecution]
public class ImportarRequisicoesManualJob : AsyncBackgroundJob<ImportarRequisicoesManualArgs>, IImportarRequisicoesManualJob
{
    private readonly ICancellationTokenProvider _cancelationToken;
    private readonly IImportarRequisicoesService _importarRequisicoesService;
    private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

    public ImportarRequisicoesManualJob(IImportarRequisicoesService importarRequisicoesService,
                                  ICancellationTokenProvider cancellationTokenProvider)
    {
        _importarRequisicoesService = importarRequisicoesService ?? throw new ArgumentException(nameof(importarRequisicoesService));
        _cancelationToken = cancellationTokenProvider ?? throw new ArgumentException(nameof(cancellationTokenProvider));
    }

    [UnitOfWork(isTransactional: false, IsolationLevel.RepeatableRead, timeout: Timeout.Infinite)]
    public override async Task ExecuteAsync(ImportarRequisicoesManualArgs args)
    {
        _cancelationToken.Token.ThrowIfCancellationRequested();

        await _semaphore.WaitAsync();
        try
        {
            Logger.LogInformation($">>> Importando requisição Manualmente: propostaId: {args.PropostaId}, numProtocolo: {args.NumProtocolo}.");

            long propostaId = args.PropostaId;
            string numProtocolo = args.NumProtocolo;

            if (propostaId <= 0)
                throw new ArgumentException($"Erro ao importar requisição manualmente. PropostaID inválido. propostaId: {args.PropostaId}");

            if (String.IsNullOrEmpty(numProtocolo))
                throw new ArgumentException($"Erro ao importar requisição manualmente. Número de protocolo inválido. numProtocolo: {args.NumProtocolo}");

            await _importarRequisicoesService.ImportarAsync(propostaId, numProtocolo);
        }
        catch (Exception ex)
        {
            Logger.LogError($"Erro ao importar requisição manualmente: numProtocolo: {args.NumProtocolo}, propostaId: {args.PropostaId}. Exceção: {ex.Message}");
            throw;
        }
        finally
        {
            _semaphore.Release(); // Libera o semáforo
        }
    }
}

[ExcludeFromCodeCoverage]
public class ImportarRequisicoesManualArgs
{
    public long PropostaId { get; set; }
    public string NumProtocolo { get; set; }

    public ImportarRequisicoesManualArgs(long propostaId, string numProtocolo)
    {
        PropostaId = propostaId;
        NumProtocolo = numProtocolo;
    }
}
