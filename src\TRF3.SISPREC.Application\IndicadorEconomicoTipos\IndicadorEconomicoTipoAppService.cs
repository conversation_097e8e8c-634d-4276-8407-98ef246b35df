using TRF3.SISPREC.Enums;
using TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.IndicadorEconomicoTipos
{
    public class IndicadorEconomicoTipoAppService : BaseCrudAppService<IndicadorEconomicoTipo, IndicadorEconomicoTipoDto, int, IndicadorEconomicoTipoGetListInput, CreateUpdateIndicadorEconomicoTipoDto, CreateUpdateIndicadorEconomicoTipoDto>, IIndicadorEconomicoTipoAppService
    {
        public IndicadorEconomicoTipoAppService(IIndicadorEconomicoTipoRepository repository, IIndicadorEconomicoTipoManager manager) : base(repository, manager)
        {
        }

        protected override Task DeleteByIdAsync(int id)
        {
            return Manager.ExcluirAsync(e =>
                e.TipoIndicadorEconomicoId == id
            );
        }

        protected override Task<IndicadorEconomicoTipo> GetEntityByIdAsync(int id)
        {
            return Repository.FirstAsync(x => x.TipoIndicadorEconomicoId == id);
        }

        protected override IQueryable<IndicadorEconomicoTipo> ApplyDefaultSorting(IQueryable<IndicadorEconomicoTipo> query)
        {
            return query.OrderBy(e => e.TipoIndicadorEconomicoId);
        }

        protected override async Task<IQueryable<IndicadorEconomicoTipo>> CreateFilteredQueryAsync(IndicadorEconomicoTipoGetListInput input)
        {
            return (await base.CreateFilteredQueryAsync(input))
                .WhereIf(input.TipoIndicadorEconomicoId > 0, x => x.TipoIndicadorEconomicoId == input.TipoIndicadorEconomicoId)
                .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo!.Contains(input.Codigo!))
                .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao!.Contains(input.Descricao!))
                .WhereIf(input.Ativo != null, x => x.Ativo == (input.Ativo == ESimNao.SIM));
        }

        public async Task<ListResultDto<IndicadorEconomicoTipoDto>> GetLookupAsync()
        {
            var query = await Repository.GetQueryableAsync();

            var filteredQuery = query
                .Where(x => x.Ativo)
                .OrderBy(x => x.Codigo)
                .Select(x => new IndicadorEconomicoTipoDto
                {
                    TipoIndicadorEconomicoId = x.TipoIndicadorEconomicoId,
                    Codigo = x.Codigo,
                    Descricao = x.Descricao,
                    Ativo = x.Ativo
                });

            var result = await AsyncExecuter.ToListAsync(filteredQuery);
            return new ListResultDto<IndicadorEconomicoTipoDto>(result);
        }
    }
}