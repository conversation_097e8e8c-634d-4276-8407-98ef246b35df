using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.IndicadorEconomicos.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.IndicadorEconomicos
{
    public class IndicadorEconomicoAppService : BaseCrudAppService<IndicadorEconomico, IndicadorEconomicoDto, int, IndicadorEconomicoGetListInput>, IIndicadorEconomicoAppService
    {
        public IndicadorEconomicoAppService(IIndicadorEconomicoRepository repository, IIndicadorEconomicoManager manager) : base(repository, manager)
        {
        }

        protected override Task DeleteByIdAsync(int id)
        {
            return Manager.ExcluirAsync(e =>
                e.IndicadorEconomicoId == id
            );
        }

        protected override Task<IndicadorEconomico> GetEntityByIdAsync(int id)
        {
            return Repository.FirstAsync(x => x.IndicadorEconomicoId == id);
        }

        protected override IQueryable<IndicadorEconomico> ApplyDefaultSorting(IQueryable<IndicadorEconomico> query)
        {
            return query.OrderBy(e => e.IndicadorEconomicoId);
        }

        protected override async Task<IQueryable<IndicadorEconomico>> CreateFilteredQueryAsync(IndicadorEconomicoGetListInput input)
        {
            var query = (await base.CreateFilteredQueryAsync(input))
                .Include(x => x.TipoIndicadorEconomico)
                .Where(x => x.IndicadorEconomicoTipoId == input.TipoIndicadorEconomicoId)
                .Where(x => !x.IsDeleted);

            return query;
        }
    }
}