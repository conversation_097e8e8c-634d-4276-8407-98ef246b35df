using TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.Dtos;

namespace TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos;
public class IndiceAtualizacaoMonetariaTipoAppService : BaseCrudAppService<
                                                            IndiceAtualizacaoMonetariaTipo,
                                                            IndiceAtualizacaoMonetariaTipoDto,
                                                            int,
                                                            IndiceAtualizacaoMonetariaTipoGetListInput,
                                                            CreateUpdateIndiceAtualizacaoMonetariaTipoDto,
                                                            CreateUpdateIndiceAtualizacaoMonetariaTipoDto>,
                                                            IIndiceAtualizacaoMonetariaTipoAppService
{

    private readonly IIndiceAtualizacaoMonetariaTipoManager _manager;
    private readonly IIndiceAtualizacaoMonetariaTipoRepository _repository;

    public IndiceAtualizacaoMonetariaTipoAppService(IIndiceAtualizacaoMonetariaTipoRepository repository, IIndiceAtualizacaoMonetariaTipoManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    public async override Task<IndiceAtualizacaoMonetariaTipoDto> CreateAsync(CreateUpdateIndiceAtualizacaoMonetariaTipoDto input)
    {
        var entity = ObjectMapper.Map<CreateUpdateIndiceAtualizacaoMonetariaTipoDto, IndiceAtualizacaoMonetariaTipo>(input);
        return ObjectMapper.Map<IndiceAtualizacaoMonetariaTipo, IndiceAtualizacaoMonetariaTipoDto>(await _manager.CreateAsync(entity));
    }

    public async override Task<IndiceAtualizacaoMonetariaTipoDto> UpdateAsync(int id, CreateUpdateIndiceAtualizacaoMonetariaTipoDto input)
    {
        var entity = ObjectMapper.Map<CreateUpdateIndiceAtualizacaoMonetariaTipoDto, IndiceAtualizacaoMonetariaTipo>(input);
        return ObjectMapper.Map<IndiceAtualizacaoMonetariaTipo, IndiceAtualizacaoMonetariaTipoDto>(await _manager.UpdateAsync(entity));
    }

    protected async override Task DeleteByIdAsync(int id)
    {
        await _manager.ExcluirAsync(id);
    }

    protected override async Task<IndiceAtualizacaoMonetariaTipo> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Indice_Atuali_Moneta_Tipo == id
            ));
    }

    protected override IQueryable<IndiceAtualizacaoMonetariaTipo> ApplyDefaultSorting(IQueryable<IndiceAtualizacaoMonetariaTipo> query)
    {
        return query.OrderBy(e => e.Seq_Indice_Atuali_Moneta_Tipo);
    }

    protected override async Task<IQueryable<IndiceAtualizacaoMonetariaTipo>> CreateFilteredQueryAsync(IndiceAtualizacaoMonetariaTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.Seq_Indice_Atuali_Moneta_Tipo > 0, x => x.Seq_Indice_Atuali_Moneta_Tipo == input.Seq_Indice_Atuali_Moneta_Tipo)
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            .WhereIf(!input.DescricaoUsoCodigo.IsNullOrWhiteSpace(), x => x.DescricaoUsoCodigo.Contains(input.DescricaoUsoCodigo))
            .WhereIf(input.SequencialCJF != null, x => x.SequencialCJF == input.SequencialCJF)
            ;
    }
}
