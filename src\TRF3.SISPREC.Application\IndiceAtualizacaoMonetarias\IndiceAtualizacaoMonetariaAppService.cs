using TRF3.SISPREC.IndiceAtualizacaoMonetarias.Dtos;
using TRF3.SISPREC.Permissoes;
using Volo.Abp;

namespace TRF3.SISPREC.IndiceAtualizacaoMonetarias;

public class IndiceAtualizacaoMonetariaAppService : BaseReadOnlyAppService<IndiceAtualizacaoMonetaria, IndiceAtualizacaoMonetariaDto, int, IndiceAtualizacaoMonetariaGetListInput>, IIndiceAtualizacaoMonetariaAppService
{
    private readonly IIndiceAtualizacaoMonetariaRepository _repository;
    private readonly IIndiceAtualizacaoMonetariaManager _manager;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.IndiceAtualizacaoMonetaria.Visualizar;

    public IndiceAtualizacaoMonetariaAppService(IIndiceAtualizacaoMonetariaRepository repository,
                                                IIndiceAtualizacaoMonetariaManager manager) : base(repository)
    {
        _repository = repository;
        _manager = manager;
    }

    public async Task<IndiceAtualizacaoMonetariaDto> GetAsync(int id)
    {
        var entity = await _repository.GetAsync(x => x.Seq_Indice_Atuali_Moneta == id);
        return ObjectMapper.Map<IndiceAtualizacaoMonetaria, IndiceAtualizacaoMonetariaDto>(entity);
    }

    public async Task CreateAsync(IndiceAtualizacaoMonetariaDto dto)
    {
        var entity = ObjectMapper.Map<IndiceAtualizacaoMonetariaDto, IndiceAtualizacaoMonetaria>(dto);
        await _manager.CreateAsync(entity);
    }

    public async Task UpdateAsync(IndiceAtualizacaoMonetariaDto dto)
    {
        if (dto.Mes != DateTime.Now.Month && dto.Ano != DateTime.Now.Year)
            throw new UserFriendlyException("Não é possivel alterar valores para meses anteriores ao atual.");

        var entity = ObjectMapper.Map<IndiceAtualizacaoMonetariaDto, IndiceAtualizacaoMonetaria>(dto);
        await _manager.UpdateAsync(entity);
    }

    protected override async Task<IndiceAtualizacaoMonetaria> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
                (await _repository.WithDetailsAsync()).Where(e => e.Seq_Indice_Atuali_Moneta == id)
            );
    }

    protected override IQueryable<IndiceAtualizacaoMonetaria> ApplyDefaultSorting(IQueryable<IndiceAtualizacaoMonetaria> query)
    {
        return query.OrderBy(e => e.Seq_Indice_Atuali_Moneta);
    }

    protected override async Task<IQueryable<IndiceAtualizacaoMonetaria>> CreateFilteredQueryAsync(IndiceAtualizacaoMonetariaGetListInput input)
    {
        return (await _repository.WithDetailsAsync())
            .WhereIf(input.Ano > 0, x => x.Ano == input.Ano)
            .WhereIf(input.Mes > 0, x => x.Mes == input.Mes)
            .WhereIf(input.DataValidadeInicio != null, x => x.DataValidadeInicio == input.DataValidadeInicio)
            .WhereIf(input.DataValidadeFim != null, x => x.DataValidadeFim == input.DataValidadeFim)
            .WhereIf(input.DataRegistro != null, x => x.DataRegistro == input.DataRegistro)
            .WhereIf(input.Valor != null, x => x.Valor == input.Valor)
            .WhereIf(input.TipoIndiceEnum != null, x => x.TipoIndiceEnum == input.TipoIndiceEnum)
            .WhereIf(input?.IndiceAtualizacaoMonetariaTipoId > 0, x => x.TipoIndice.Seq_Indice_Atuali_Moneta_Tipo == input.IndiceAtualizacaoMonetariaTipoId)
            ;
    }
}