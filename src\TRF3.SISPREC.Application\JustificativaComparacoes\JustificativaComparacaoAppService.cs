using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.JustificativaComparacoes.Dtos;

namespace TRF3.SISPREC.JustificativaComparacoes;

public class JustificativaComparacaoAppService : BaseCrudAppService<JustificativaComparacao, JustificativaComparacaoDto, JustificativaComparacaoKey, JustificativaComparacaoGetListInput, CreateUpdateJustificativaComparacaoDto, CreateUpdateJustificativaComparacaoDto>,
    IJustificativaComparacaoAppService
{

    private readonly IJustificativaComparacaoManager _manager;
    private readonly IJustificativaComparacaoRepository _repository;

    public JustificativaComparacaoAppService(IJustificativaComparacaoRepository repository, IJustificativaComparacaoManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    protected override Task DeleteByIdAsync(JustificativaComparacaoKey id)
    {
        return _manager.ExcluirAsync(e =>
            e.NumeroProtocoloRequisicao == id.NumeroProtocoloRequisicao &&
            e.RequisicaoJustificativaId == id.RequisicaoJustificativaId
        );
    }

    protected override async Task<JustificativaComparacao> GetEntityByIdAsync(JustificativaComparacaoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.NumeroProtocoloRequisicao == id.NumeroProtocoloRequisicao &&
                e.RequisicaoJustificativaId == id.RequisicaoJustificativaId
            ));
    }

    protected override IQueryable<JustificativaComparacao> ApplyDefaultSorting(IQueryable<JustificativaComparacao> query)
    {
        return query.OrderBy(e => e.NumeroProtocoloRequisicao);
    }

    protected override async Task<IQueryable<JustificativaComparacao>> CreateFilteredQueryAsync(JustificativaComparacaoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.JustificativaComparacaoId.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.JustificativaComparacaoId!))
            .WhereIf(input.RequisicaoJustificativaId != null, x => x.RequisicaoJustificativaId == input.RequisicaoJustificativaId)
            .WhereIf(input.IsDeleted != null, x => x.IsDeleted == input.IsDeleted)
            .WhereIf(input.RequisicaoJustificativaId > 0, x => x.RequisicaoJustificativaId == input.RequisicaoJustificativaId)
            ;
    }

    public async Task<List<JustificativaComparacao>> ObterJustificativaComparacaoPorRequisicaoJustificativa(long requisicaoJustificativaId)
    {
        return await (await _repository.GetQueryableAsync()).Where(x => x.RequisicaoJustificativaId == requisicaoJustificativaId).ToListAsync();
    }
}