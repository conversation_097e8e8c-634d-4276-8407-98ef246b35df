using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.JustificativaDocumentos.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.JustificativaDocumentos;

public class JustificativaDocumentoAppService : BaseCrudAppService<JustificativaDocumento, JustificativaDocumentoDto, long, JustificativaDocumentoGetListInput, CreateUpdateJustificativaDocumentoDto, CreateUpdateJustificativaDocumentoDto>,
    IJustificativaDocumentoAppService
{
    private readonly IRequisicaoPropostaRepository _requisicaoPropostaRepository;
    private readonly IJustificativaDocumentoManager _manager;
    private readonly IJustificativaDocumentoRepository _repository;
    private readonly IJustificativaDocumentoService _service;

    public JustificativaDocumentoAppService(
        IRequisicaoPropostaRepository requisicaoPropostaRepository,
        IJustificativaDocumentoRepository repository,
        IJustificativaDocumentoManager manager,
        IJustificativaDocumentoService service
        ) : base(repository, manager)
    {
        _requisicaoPropostaRepository = requisicaoPropostaRepository ?? throw new ArgumentNullException(nameof(requisicaoPropostaRepository));
        _service = service ?? throw new ArgumentNullException(nameof(service));
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _manager = manager ?? throw new ArgumentNullException(nameof(manager));
    }

    protected override Task DeleteByIdAsync(long id)
    {
        return _manager.ExcluirAsync(e =>
            e.JustificativaDocumentoId == id
        );
    }

    public async Task<List<EspelhoRequisicaoJustificativaDocumento>> GerarEspelhoRequisicaoAsync(CreateEspelhoRequisicaoDto input)
    {
        try
        {
            List<EspelhoRequisicaoJustificativaDocumento> espelhoRequisicaoJustificativaDocumentos = [];

            foreach (string requisicaoPrincipal in input.NumeroRequisicoes)
            {
                var proposta = await (await _requisicaoPropostaRepository.GetQueryableAsync())
                    .Include(x => x.Proposta)
                    .Where(x => x.NumeroProtocoloRequisicao == requisicaoPrincipal)
                    .Select(x => new { x.Proposta.AnoProposta, x.Proposta.MesProposta })
                    .FirstOrDefaultAsync();

                if (proposta is null)
                    throw new ArgumentException($"Não foi possível localizar a proposta para o número da requisição {requisicaoPrincipal}");

                espelhoRequisicaoJustificativaDocumentos.Add(await _service.GerarEspelhoRequisicaoAsync(input.RequisicaoJustificativaId, requisicaoPrincipal, input.Procedimento, proposta.AnoProposta, proposta.MesProposta, input.Observacao));

                //Gerar requisições comparadas
                if (input.RequisicoesComparada is not null)
                {
                    foreach (var requisicaoComparada in input.RequisicoesComparada)
                    {
                        //ignora caso a requisição não exista no sisprec.
                        var existeRequisicao = await _requisicaoPropostaRepository.AnyAsync(x => x.NumeroProtocoloRequisicao == requisicaoComparada.NumeroRequisicao);
                        if (!existeRequisicao)
                            continue;

                        var espelhoRequisicaoDocumento = await _service.GerarEspelhoRequisicaoAsync(input.RequisicaoJustificativaId, requisicaoComparada.NumeroRequisicao!, input.Procedimento, proposta.AnoProposta, proposta.MesProposta, requisicaoComparada.Observacoes, requisicaoPrincipal);

                        espelhoRequisicaoJustificativaDocumentos.Add(espelhoRequisicaoDocumento);
                    }
                }
            }

            return espelhoRequisicaoJustificativaDocumentos;
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(ex.Message, innerException: ex);
        }
    }

    public async Task ExcluirEspelhoRequisicaoAsync(long requisicaoJustificativaId)
    {
        await _service.ExcluirEspelhoRequisicaoAsync(requisicaoJustificativaId);
    }

    public async Task SalvarExtratoRFBAsync(long requisicaoJustificativaId, string numeroRequisicao, string procedimento, IFormFile arquivo)
    {
        var proposta = await (await _requisicaoPropostaRepository.GetQueryableAsync())
                    .Include(x => x.Proposta)
                    .Where(x => x.NumeroProtocoloRequisicao == numeroRequisicao)
                    .Select(x => new { x.Proposta.AnoProposta, x.Proposta.MesProposta })
                    .FirstAsync();

        await _service.SalvarExtratoRFBAsync(requisicaoJustificativaId, numeroRequisicao, procedimento, proposta.AnoProposta, proposta.MesProposta, arquivo);
    }

    protected override async Task<JustificativaDocumento> GetEntityByIdAsync(long id)
    {
        return await AsyncExecuter.FirstAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.JustificativaDocumentoId == id
            ));
    }

    protected override IQueryable<JustificativaDocumento> ApplyDefaultSorting(IQueryable<JustificativaDocumento> query)
    {
        return query.OrderBy(e => e.JustificativaDocumentoId);
    }

    protected override async Task<IQueryable<JustificativaDocumento>> CreateFilteredQueryAsync(JustificativaDocumentoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.RequisicaoJustificativa)
            .WhereIf(input.RequisicaoDocumentoId != null, x => x.JustificativaDocumentoId == input.RequisicaoDocumentoId)
            .WhereIf(!input.NomeDocumento.IsNullOrWhiteSpace(), x => x.NomeDocumento!.Contains(input.NomeDocumento!))
            .WhereIf(!input.Path.IsNullOrWhiteSpace(), x => x.Path!.Contains(input.Path!))
            .WhereIf(input.DataCriacao != null, x => x.DataCriacao == input.DataCriacao)
            .WhereIf(!input.NumeroProtocoloRequisicao.IsNullOrWhiteSpace(), x => x.RequisicaoJustificativa!.NumeroProtocoloRequisicao.Contains(input.NumeroProtocoloRequisicao!))
            .WhereIf(input.IsDeleted != null, x => x.IsDeleted == input.IsDeleted)
            ;
    }
}