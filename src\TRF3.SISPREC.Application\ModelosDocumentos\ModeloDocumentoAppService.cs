using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.ModelosDocumentos.Dtos;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.ModelosDocumentos;

public class ModeloDocumentoAppService(IModeloDocumentoRepository repository, IModeloDocumentoManager manager) : BaseCrudAppService<ModeloDocumento, ModeloDocumentoDto, int, ModeloDocumentoGetListInput, CreateUpdateModeloDocumentoDto, CreateUpdateModeloDocumentoDto>(repository, manager), IModeloDocumentoAppService
{
    protected override Task DeleteByIdAsync(int id)
    {
        return manager.ExcluirAsync(e =>
            e.ModeloDocumentoId == id
        );
    }

    protected override async Task<ModeloDocumento> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await repository.WithDetailsAsync()).Include(x => x.Setor).Where(e =>
                e.ModeloDocumentoId == id
            ));
    }

    protected override IQueryable<ModeloDocumento> ApplyDefaultSorting(IQueryable<ModeloDocumento> query)
    {
        return query.OrderBy(e => e.ModeloDocumentoId);
    }

    protected override async Task<IQueryable<ModeloDocumento>> CreateFilteredQueryAsync(ModeloDocumentoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Setor)
            .WhereIf(input.ModeloDocumentoId > 0, x => x.ModeloDocumentoId == input.ModeloDocumentoId)
            .WhereIf(!input.NomeModelo.IsNullOrWhiteSpace(), x => x.NomeModelo.Contains(input.NomeModelo!));
    }

    public override async Task<ModeloDocumentoDto> CreateAsync(CreateUpdateModeloDocumentoDto dto)
    {
        await ValidarModeloDocumento(null, dto);

        return await base.CreateAsync(dto);
    }

    public override async Task<ModeloDocumentoDto> UpdateAsync(int id, CreateUpdateModeloDocumentoDto input)
    {
        await ValidarModeloDocumento(id, input);

        return await base.UpdateAsync(id, input);
    }

    private async Task ValidarModeloDocumento(int? id, CreateUpdateModeloDocumentoDto dto)
    {
        if (dto is null || string.IsNullOrWhiteSpace(dto.NomeModelo) || (string.IsNullOrWhiteSpace(dto.TextoDocumento) || dto.TextoDocumento.Equals("<p><br></p>", StringComparison.OrdinalIgnoreCase)))
            throw new UserFriendlyException("Nome do modelo e HTML devem estar preenchidos!");

        if (await Repository.AnyAsync(x => x.NomeModelo.ToUpper().Equals(dto.NomeModelo.ToUpper()) && x.ModeloDocumentoId != id && x.SetorId == dto.SetorId))
            throw new UserFriendlyException("Já existe um Modelo de Documento cadastrado com este nome para o Setor!");

        if (await Repository.AnyAsync(x => x.NomeModelo.ToUpper().Equals(dto.NomeModelo.ToUpper()) && x.ModeloDocumentoId != id))
            throw new UserFriendlyException("Já existe um modelo com o mesmo nome cadastrado. Por favor, utilizar outro nome de modelo.");
    }
}