using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.MotivosExpedientesAdministrativos.Dtos;
using Volo.Abp;

namespace TRF3.SISPREC.MotivosExpedientesAdministrativos
{
    public class MotivoExpedienteAdministrativoAppService : BaseCrudAppService<MotivoExpedienteAdministrativo, MotivoExpedienteAdministrativoDto,
        int, MotivoExpedienteAdministrativoGetListInput, CreateUpdateMotivoExpedienteAdministrativoDto, CreateUpdateMotivoExpedienteAdministrativoDto>, IMotivoExpedienteAdministrativoAppService
    {

        private readonly IMotivoExpedienteAdministrativoRepository _repository;

        public MotivoExpedienteAdministrativoAppService(IMotivoExpedienteAdministrativoRepository motivoExpedienteAdministrativoRepository, IMotivoExpedienteAdministrativoManager motivoExpedienteAdministrativoManager) : base(motivoExpedienteAdministrativoRepository, motivoExpedienteAdministrativoManager)
        {
            _repository = motivoExpedienteAdministrativoRepository;
        }

        protected override async Task<MotivoExpedienteAdministrativo> GetEntityByIdAsync(int id)
        {
            var entity = await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.ExpedienteAdministrativoMotivoId == id
            ));
            return entity is null ? throw new UserFriendlyException("Motivo Expediente inexistente!") : entity;
        }

        [ExcludeFromCodeCoverage]
        protected override IQueryable<MotivoExpedienteAdministrativo> ApplyDefaultSorting(IQueryable<MotivoExpedienteAdministrativo> query)
        {
            return query.OrderBy(e => e.DescricaoMotivo);
        }

        protected override async Task<IQueryable<MotivoExpedienteAdministrativo>> CreateFilteredQueryAsync(MotivoExpedienteAdministrativoGetListInput input)
        {
            var query = (await base.CreateFilteredQueryAsync(input))
                .WhereIf(input.DescricaoMotivo != null, x => x.DescricaoMotivo != null && x.DescricaoMotivo.Contains(input.DescricaoMotivo!));

            return query;
        }


        public async override Task<MotivoExpedienteAdministrativoDto> UpdateAsync(int id, CreateUpdateMotivoExpedienteAdministrativoDto dto)
        {
            var entitySisprec = await (await _repository.GetQueryableAsync())
                .AsNoTracking()
                .Where(p => p.ExpedienteAdministrativoMotivoId.Equals(id))
                .FirstOrDefaultAsync();

            if (entitySisprec is null)
                throw new UserFriendlyException("Não foi possivel atualizar, Motivo Expediente inexistente!");

            MotivoExpedienteAdministrativoDto result = await base.UpdateAsync(id, dto);

            return result;
        }

        public async Task<MotivoExpedienteAdministrativo> BuscaPorId(int id)
        {
            return await this.GetEntityByIdAsync(id);
        }

        protected override Task DeleteByIdAsync(int id)
        {
            return Manager.ExcluirAsync(e => e.ExpedienteAdministrativoMotivoId == id);
        }
    }
}
