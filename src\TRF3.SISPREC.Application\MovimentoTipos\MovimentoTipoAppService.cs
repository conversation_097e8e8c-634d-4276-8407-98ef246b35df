using TRF3.SISPREC.MovimentoTipos.Dtos;
using TRF3.SISPREC.Permissoes;

namespace TRF3.SISPREC.MovimentoTipos;

public class MovimentoTipoAppService : BaseReadOnlyAppService<MovimentoTipo, MovimentoTipoDto, MovimentoTipoKey, MovimentoTipoGetListInput>, IMovimentoTipoAppService
{
    private readonly IMovimentoTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.MovimentoTipo.Visualizar;

    public MovimentoTipoAppService(IMovimentoTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<MovimentoTipo> GetEntityByIdAsync(MovimentoTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Movime_Tipo == id.Seq_Movime_Tipo
            ));
    }

    protected override IQueryable<MovimentoTipo> ApplyDefaultSorting(IQueryable<MovimentoTipo> query)
    {
        return query.OrderBy(e => e.Seq_Movime_Tipo);
    }

    protected override async Task<IQueryable<MovimentoTipo>> CreateFilteredQueryAsync(MovimentoTipoGetListInput input)
    {
        var retorno = (await base.CreateFilteredQueryAsync(input));
        return retorno
            .WhereIf(input.Codigo != null, x => x.Codigo == input.Codigo)
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}