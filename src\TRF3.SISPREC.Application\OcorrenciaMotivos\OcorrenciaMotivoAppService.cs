using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos.Dtos;
using TRF3.SISPREC.OcorrenciaMotivos.Servicos;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.SincronizacaoLegado;
using Volo.Abp;
namespace TRF3.SISPREC.OcorrenciaMotivos;
public class OcorrenciaMotivoAppService : BaseCrudAppService<OcorrenciaMotivo, OcorrenciaMotivoDto, int, OcorrenciaMotivoGetListInput, CreateUpdateOcorrenciaMotivoDto, CreateUpdateOcorrenciaMotivoDto>,
    IOcorrenciaMotivoAppService
{

    private readonly IOcorrenciaMotivoManager _manager;
    private readonly IOcorrenciaMotivoRepository _repository;
    private readonly IReqPagUnitOfWork _reqPagUnitOfWork;
    private readonly IAcaoTipoRepository _acaoTipoRepository;
    private readonly IPdfFileGeneratorService _pdf;

    public OcorrenciaMotivoAppService(IPdfFileGeneratorService pDFFileGeneratorService, IOcorrenciaMotivoRepository repository, IOcorrenciaMotivoManager manager, IAcaoTipoRepository acaoTipoRepository, IReqPagUnitOfWork reqPagUnitOfWork) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
        _acaoTipoRepository = acaoTipoRepository;
        _reqPagUnitOfWork = reqPagUnitOfWork;
        _pdf = pDFFileGeneratorService;
    }

    [ExcludeFromCodeCoverage]
    public async Task<byte[]> GerarPDF(OcorrenciaMotivoGetListInput input)
    {
        var filter = await CreateFilteredQueryAsync(input);

        return await _pdf.GenerateFileAsync("OcorrenciaMotivoTemplate", await filter.ToListAsync());
    }

    public async Task<OcorrenciaMotivo> InserirAsync(CreateUpdateOcorrenciaMotivoDto ocorrenciaMotivoDto)
    {
        OcorrenciaMotivo ocorrenciaMotivo = ObjectMapper.Map<CreateUpdateOcorrenciaMotivoDto, OcorrenciaMotivo>(ocorrenciaMotivoDto);

        var entity = await (await _repository.GetQueryableAsync())
           .Include(p => p.AcaoTipo)
           .Where(p => p.AcaoTipoId.Equals(ocorrenciaMotivo.AcaoTipoId)
           && p.AnaliseTelaId.Equals(ocorrenciaMotivo.AnaliseTelaId)
           && p.DescricaoMotivo.ToUpper().Equals(ocorrenciaMotivo.DescricaoMotivo.ToUpper())
           || p.CodigoMotivo.Equals(ocorrenciaMotivoDto.CodigoMotivo))
           .FirstOrDefaultAsync();

        var entityReqPag = await _reqPagUnitOfWork.MotivoOcorrenciaRepository.BuscaListaCodigo(ocorrenciaMotivo.CodigoMotivo);

        if (entity is not null || entityReqPag.Any())
            throw new UserFriendlyException("Esta ocorrência já está cadastrada!");

        var ocorrenciaSisprec = await _repository.InsertAsync(ocorrenciaMotivo, true);
        ocorrenciaSisprec.AcaoTipo = await _acaoTipoRepository.GetAsync(p => p.AcaoTipoId.Equals(ocorrenciaMotivo.AcaoTipoId));

        var mapReqPag = OcorrenciaMotivoMapper.ToOcorrenciaMotivoReqPag(ocorrenciaSisprec);
        await _reqPagUnitOfWork.MotivoOcorrenciaRepository.InserirTrans(mapReqPag);

        await _reqPagUnitOfWork.SaveChangesAsync();

        return ocorrenciaSisprec;
    }

    public async override Task<OcorrenciaMotivoDto> UpdateAsync(int id, CreateUpdateOcorrenciaMotivoDto ocorrenciaMotivoDto)
    {
        var entitySisprec = await (await _repository.GetQueryableAsync())
            .AsNoTracking()
            .Include(p => p.AcaoTipo)
            .Where(p => p.OcorrenciaMotivoId.Equals(id))
            .FirstOrDefaultAsync();

        if (entitySisprec is null)
            throw new UserFriendlyException("Não foi possivel atualizar, Ocorrência Motivo inexistente!");

        OcorrenciaMotivoDto result = await base.UpdateAsync(id, ocorrenciaMotivoDto);

        result.AcaoTipo = await _acaoTipoRepository.GetAsync(p => p.AcaoTipoId.Equals(ocorrenciaMotivoDto.AcaoTipoId));


        OcorrenciaMotivo ocorrenciaMotivo = ObjectMapper.Map<OcorrenciaMotivoDto, OcorrenciaMotivo>(result);

        var entityReqPagMapped = OcorrenciaMotivoMapper.ToOcorrenciaMotivoReqPag(ocorrenciaMotivo);
        await _reqPagUnitOfWork.MotivoOcorrenciaRepository.UpdateTrans(entityReqPagMapped);
        await _reqPagUnitOfWork.SaveChangesAsync();

        return result;
    }

    protected override Task DeleteByIdAsync(int id)
    {
        return _manager.ExcluirAsync(e =>
            e.OcorrenciaMotivoId == id
        );
    }

    protected override async Task<OcorrenciaMotivo> GetEntityByIdAsync(int id)
    {
        var entity = await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.OcorrenciaMotivoId == id
            ));
        return entity is null ? throw new UserFriendlyException("Ocorrência Motivo inexistente!") : entity;
    }

    protected override IQueryable<OcorrenciaMotivo> ApplyDefaultSorting(IQueryable<OcorrenciaMotivo> query)
    {
        return query.OrderBy(e => e.OcorrenciaMotivoId);
    }

    protected override async Task<IQueryable<OcorrenciaMotivo>> CreateFilteredQueryAsync(OcorrenciaMotivoGetListInput input)
    {
        var query = (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.AcaoTipo)
            .WhereIf(input.CodigoMotivo.HasValue, x => x.CodigoMotivo == input.CodigoMotivo)
            .WhereIf(input.AcaoTipoId > 0, x => x.AcaoTipoId == Convert.ToInt32(input.AcaoTipoId))
            .WhereIf(input.AnaliseTelaId.HasValue, x => x.AnaliseTelaId == Convert.ToInt32(input.AnaliseTelaId))
            .WhereIf(input.DescricaoMotivo != null, x => x.DescricaoMotivo.Contains(!string.IsNullOrEmpty(input.DescricaoMotivo) ? input.DescricaoMotivo : string.Empty))
            .WhereIf(input.Ativo != null, x => x.Ativo == (input.Ativo == ESimNao.SIM));
        return query;
    }
}
