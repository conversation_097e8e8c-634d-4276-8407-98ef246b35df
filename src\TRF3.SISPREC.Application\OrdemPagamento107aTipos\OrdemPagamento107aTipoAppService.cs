using TRF3.SISPREC.OrdemPagamento107aTipos.Dtos;
using TRF3.SISPREC.Permissoes;

namespace TRF3.SISPREC.OrdemPagamento107aTipos;

public class OrdemPagamento107aTipoAppService : BaseReadOnlyAppService<OrdemPagamento107aTipo, OrdemPagamento107aTipoDto, OrdemPagamento107aTipoKey, OrdemPagamento107aTipoGetListInput>, IOrdemPagamento107aTipoAppService
{
    private readonly IOrdemPagamento107aTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.OrdemPagamento107aTipo.Visualizar;

    public OrdemPagamento107aTipoAppService(IOrdemPagamento107aTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<OrdemPagamento107aTipo> GetEntityByIdAsync(OrdemPagamento107aTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Ordem_Pagame_107a_Tipo == id.Seq_Ordem_Pagame_107a_Tipo
            ));
    }

    protected override IQueryable<OrdemPagamento107aTipo> ApplyDefaultSorting(IQueryable<OrdemPagamento107aTipo> query)
    {
        return query.OrderBy(e => e.Seq_Ordem_Pagame_107a_Tipo);
    }

    protected override async Task<IQueryable<OrdemPagamento107aTipo>> CreateFilteredQueryAsync(OrdemPagamento107aTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}