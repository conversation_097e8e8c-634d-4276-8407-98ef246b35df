using TRF3.SISPREC.Apoio;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Peritos.Dtos;
using TRF3.SISPREC.VerificacoesCnpjCpf.ConsultaCNPJ;
using TRF3.SISPREC.VerificacoesCnpjCpf.ConsultaCPF;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.Peritos;

public class PeritoAppService : BaseAtivaDesativaAppService<Perito, PeritoDto, int, PeritoGetListInput, CreateUpdatePeritoDto, CreateUpdatePeritoDto>,
    IPeritoAppService
{
    private readonly IPeritoManager _manager;
    private readonly IConsultaCPFService _consultaCPFService;
    private readonly IConsultaCNPJService _consultaCNPJService;
    private readonly IPeritoRepository _repository;

    public PeritoAppService(IPeritoRepository repository, IPeritoManager manager, IConsultaCPFService consultaCPFService, IConsultaCNPJService consultaCNPJService) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
        _consultaCPFService = consultaCPFService;
        _consultaCNPJService = consultaCNPJService;
    }

    protected override Task DeleteByIdAsync(int id)
    {
        return _manager.ExcluirAsync(e =>
            e.PeritoId == id
        );
    }

    protected override async Task<Perito> GetEntityByIdAsync(int id)
    {
        return await _repository.FirstAsync(e => e.PeritoId == id);
    }

    protected override IQueryable<Perito> ApplyDefaultSorting(IQueryable<Perito> query)
    {
        return query.OrderBy(e => e.NumeroCnpjCpf);
    }

    protected override async Task<IQueryable<Perito>> CreateFilteredQueryAsync(PeritoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.NumeroCnpjCpf != null, x => x.NumeroCnpjCpf == input.NumeroCnpjCpf.OnlyNumbers())
            .WhereIf(input.NomePessoa != null, x => x.NomePessoa!.Contains(input.NomePessoa!))
            .WhereIf(input.VerificacaoTipoId > 0, x => x.VerificacaoTipoId == input.VerificacaoTipoId)
            .WhereIf(input.Ativo != null, x => x.Ativo == (input.Ativo == ESimNao.SIM));
    }

    public async Task<Perito?> GetPeritoPorCpfCnpj(string numeroDocumento, int tipoVerificacao)
    {
        if (numeroDocumento != null)
            return await _repository.FirstOrDefaultAsync(x => x.NumeroCnpjCpf!.Equals(numeroDocumento) && x.VerificacaoTipoId == tipoVerificacao);

        return null;
    }

    public async Task<string?> ObterNomePorCPF(string cpf)
    {
        var result = await _consultaCPFService.ConsultarDadosCpfAsync(cpf);

        return result?.Nome;
    }

    public async Task<string?> ObterNomePorCNPJ(string cnpj)
    {
        var result = await _consultaCNPJService.ConsultarDadosCNPJAsync(cnpj, false);

        return result?.RazaoSocial;
    }
}