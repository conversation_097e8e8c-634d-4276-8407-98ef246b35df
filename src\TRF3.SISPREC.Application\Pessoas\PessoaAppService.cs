using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.Pessoas.Dtos;
using TRF3.SISPREC.Pessoas.Dtos.EnderecosPessoas;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.Interfaces;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Pessoas;
public class PessoaAppService : BaseCrudAppService<Pessoa, PessoaDto, long, PessoaGetListInput, CreateUpdatePessoaDto, CreateUpdatePessoaDto>,
    IPessoaAppService
{
    protected override string VisualizarPolicyName { get; set; } = SISPRECPermissoes.Pessoa.Visualizar;
    protected override string GravarPolicyName { get; set; } = SISPRECPermissoes.Pessoa.Gravar;

    private readonly IPessoaManager _manager;
    private readonly IPessoaRepository _repository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public PessoaAppService(IPessoaRepository repository, IPessoaManager manager, IUnitOfWorkManager unitOfWorkManager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
        _unitOfWorkManager = unitOfWorkManager;
    }

    [RemoteService(false)]
    public override Task<PessoaDto> CreateAsync(CreateUpdatePessoaDto input)
    {
        throw new InvalidOperationException("Não é possível realizar a operação.");
    }

    protected override Task DeleteByIdAsync(long id)
    {
        return _manager.ExcluirAsync(e =>
            e.PessoaId == id
        );
    }

    protected override async Task<Pessoa> GetEntityByIdAsync(long id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.PessoaId == id
            ).Include(x => x.Endereco).ThenInclude(x => x.Municipio));
    }

    protected override IQueryable<Pessoa> ApplyDefaultSorting(IQueryable<Pessoa> query)
    {
        return query.OrderBy(e => e.PessoaId);
    }

    protected override async Task<IQueryable<Pessoa>> CreateFilteredQueryAsync(PessoaGetListInput input)
    {
        var pessoa = (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.PessoaId != null, x => x.PessoaId == input.PessoaId)
            .WhereIf(!input.Nome.IsNullOrWhiteSpace(), x => x.Nome.Contains(input.Nome))
            .WhereIf(!input.NomeSocial.IsNullOrWhiteSpace(), x => x.NomeSocial.Contains(input.NomeSocial))
            .WhereIf(input.TipoPessoa != null, x => x.TipoPessoa == input.TipoPessoa)
            .WhereIf(!input.NumeroCnpjCpf.IsNullOrWhiteSpace(), x => x.NumeroCnpjCpf.Contains(input.NumeroCnpjCpf))
            ;

        return pessoa;
    }


    public override async Task<PessoaDto> UpdateAsync(long id, CreateUpdatePessoaDto input)
    {
        // Obtendo a dependência no método e não no construtor para não depender da conexão sempre que o AppService for instanciado.
        // Caso contrário, para qualquer acionamento do AppService, será feita uma conexão com a base legado reqpag, pois o construtor da implementação do IReqPagUnitOfWork abre a conexão com a base.
        using (var uow = _unitOfWorkManager.Begin(isTransactional: true))
        {
            var pessoaendereco = await GetEntityByIdAsync(id);

            if (pessoaendereco != null)
            {
                input.Endereco = new CreateUpdateEnderecoPessoaDto()
                {
                    DescricaoEndereco = pessoaendereco.Endereco.DescricaoEndereco,
                    NomeBairro = pessoaendereco.Endereco.NomeBairro,
                    Cep = pessoaendereco.Endereco.Cep,
                    MunicipioId = pessoaendereco.Endereco.MunicipioId
                };
            }
            var result = await base.UpdateAsync(id, input);

            // Obtendo uma instância de IReqPagUnitOfWork do ServiceProvider
            IReqPagUnitOfWork? reqPagUnitOfWork = ServiceProvider.GetService<IReqPagUnitOfWork>();
            // Obtendo o repositório  Cadastro Pessoa do ReqPagUnitOfWork
            var reqCadastroPessoaRepository = ServiceProvider.GetService<ICadastroPessoaRepository>();
            if (reqCadastroPessoaRepository is not null)
            {
                var reqCadastroEnderecoRepository = ServiceProvider.GetService<ICadastroEnderecoRepository>();
                var cadastroPessoa = reqCadastroPessoaRepository.BuscaPorCodigo((long)id).Result;
                // Inserindo os dados no repositório Cadastro Pessoa do sistema legado
                await reqCadastroPessoaRepository.UpdateTrans(new SincronizacaoLegado.Models.ReqPag.CadastroPessoa()
                {
                    cod_cadast_pessoa = cadastroPessoa?.cod_cadast_pessoa,
                    cod_tipo_pessoa = input.TipoPessoa.ToString(),
                    num_cnpj_cpf = input.NumeroCnpjCpf,
                    nom_pessoa = input.Nome,
                    ide_advoga_procur = cadastroPessoa?.ide_advoga_procur,
                    ide_situac_pessoa = cadastroPessoa?.ide_situac_pessoa
                });

                // Inserindo os dados no repositório Endereço Pessoa do sistema legado
                SincronizacaoLegado.Models.ReqPag.CadastroEndereco cE_DTO = new SincronizacaoLegado.Models.ReqPag.CadastroEndereco()
                {
                    cod_cadast_pessoa = cadastroPessoa?.cod_cadast_pessoa,
                    cod_cidade = input?.Endereco?.MunicipioId,
                    des_endere = input?.Endereco?.DescricaoEndereco,
                    nom_bairro = input?.Endereco?.NomeBairro,
                    num_cep = input?.Endereco?.Cep
                };
                await reqCadastroEnderecoRepository.UpdateTrans(cE_DTO);
                // Caso ocorra exceção, o rollback de `base.CreateAsync(input)` é automático
                await reqPagUnitOfWork.SaveChangesAsync();
            }

            await uow.CompleteAsync();

            // Retornando o resultado da criação
            return result;
        }
    }
}
