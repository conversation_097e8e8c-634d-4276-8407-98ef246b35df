using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.BackgroundJobs;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Settings.Importacoes;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Propostas.jobs;

[ExcludeFromCodeCoverage]
[DisallowConcurrentExecution]
public class ImportarPropostaPeriodicoJob : SchedulingBackroundJob<ImportarPropostaPeriodicoJob>, IImportarPropostaPeriodicoJob
{
    private readonly IImportarPropostaService _importarPropostaService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public override string JobName => ImportacaoPropostaSettings.ImportarPropostaPeriodicoJobName;
    public override string JobGroupName => JobName;

    //protected override int IntervalInSeconds => 60 * 60 * 12;
    protected override int IntervalInSeconds => 60 * 3;

    public ImportarPropostaPeriodicoJob(IGetLoggerService getLoggerService, IScheduler scheduler, IImportarPropostaService importarPropostaService, IUnitOfWorkManager unitOfWorkManager) : base(getLoggerService, scheduler)
    {
        _importarPropostaService = importarPropostaService;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public async override Task Execute(IJobExecutionContext context)
    {
        try
        {
            using (var uow = _unitOfWorkManager.Begin(new AbpUnitOfWorkOptions(isTransactional: false, timeout: 2 * 60 * 1000), requiresNew: true))
            {
                Logger.LogInformation(">>>> Importação de Propostas: Iniciando...");

                await _importarPropostaService.Importar();

                await uow.CompleteAsync();

                Logger.LogInformation(">>>> Importação de Propostas: Finalizada!");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError($"Erro ao executar ImportarPropostaPeriodicoJob. Exceção: {nameof(ex)}. Exceção: {nameof(ex)} - {ex.Message}");
        }
    }
}
