using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.Propostas.Dtos;
using Volo.Abp;

namespace TRF3.SISPREC.Propostas;
public class PropostaAppService : BaseCrudAppService<Proposta, PropostaDto, int, PropostaGetListInput, CreateUpdatePropostaDto, CreateUpdatePropostaDto>,
    IPropostaAppService
{
    protected override string VisualizarPolicyName { get; set; } = SISPRECPermissoes.Proposta.Visualizar;
    protected override string GravarPolicyName { get; set; } = SISPRECPermissoes.Proposta.Gravar;

    private readonly IPropostaRepository _repository;

    public PropostaAppService(IPropostaRepository repository, IPropostaManager manager) : base(repository, manager)
    {
        _repository = repository;
    }

    [RemoteService(false)]
    public override Task DeleteAsync(int id)
    {
        throw new InvalidOperationException("Não é possível excluir uma proposta.");
    }

    [RemoteService(false)]
    public override Task<PropostaDto> CreateAsync(CreateUpdatePropostaDto input)
    {
        throw new InvalidOperationException("Não é possível cadastrar uma proposta pelo AppService.");
    }

    protected override async Task<Proposta> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.PropostaId == id
            ));
    }

    protected override IQueryable<Proposta> ApplyDefaultSorting(IQueryable<Proposta> query)
    {
        return query.OrderBy(e => e.PropostaId);
    }

    protected override async Task<IQueryable<Proposta>> CreateFilteredQueryAsync(PropostaGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input)).Include(p => p.Unidade).Include(p => p.TipoProcedimento).Include(p => p.IndicadorEconomico).ThenInclude(p => p!.TipoIndicadorEconomico)
            .WhereIf(input.PropostaId > 0, x => x.PropostaId == input.PropostaId)
            .WhereIf(input.TipoProcedimentoId != null, x => x.TipoProcedimentoId.Contains(input.TipoProcedimentoId.ToString()!))
            .WhereIf(input.UnidadeId > 0, x => x.UnidadeId == input.UnidadeId)
            .WhereIf(input.AnoProposta > 0, x => x.AnoProposta == input.AnoProposta)
            .WhereIf(input.MesProposta > 0, x => x.MesProposta == input.MesProposta)
            .WhereIf(input.DataAtualizacao != null, x => x.DataAtualizacao == input.DataAtualizacao)
            .WhereIf(input.ValorMinimo != null, x => x.ValorMinimo == input.ValorMinimo)
            .WhereIf(input.ValorMaximo != null, x => x.ValorMaximo == input.ValorMaximo)
            .WhereIf(input.ValorMinimoParcela != null, x => x.ValorMinimoParcela == input.ValorMinimoParcela)
            .WhereIf(input.QtdMaximaParcelaAlimetar > 0, x => x.QtdMaximaParcelaAlimetar == input.QtdMaximaParcelaAlimetar)
            .WhereIf(input.QtdMaximaParcelaComum > 0, x => x.QtdMaximaParcelaComum == input.QtdMaximaParcelaComum)
            .WhereIf(input.QtdMaximaParcelaDesapropriacaoUnico > 0, x => x.QtdMaximaParcelaDesapropriacaoUnico == input.QtdMaximaParcelaDesapropriacaoUnico)
            .WhereIf(input.QtdMaximaParcelaDesapropriacao > 0, x => x.QtdMaximaParcelaDesapropriacao == input.QtdMaximaParcelaDesapropriacao)
            .WhereIf(input.SituacaoProposta != null, x => x.SituacaoProposta == input.SituacaoProposta)
            .WhereIf(input.DataInicioCalculoJuros != null, x => x.DataInicioCalculoJuros == input.DataInicioCalculoJuros)
            .WhereIf(input.IsDeleted != null, x => x.IsDeleted == input.IsDeleted)
            .WhereIf(input.UnidadeId > 0, x => x.Unidade.UnidadeId == input.UnidadeId)
            .WhereIf(input.TipoIndicadorEconomicoId > 0, x => x.IndicadorEconomico!.IndicadorEconomicoTipoId == input.TipoIndicadorEconomicoId)
            ;
    }
}
