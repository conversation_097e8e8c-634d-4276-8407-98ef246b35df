using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.ExcelServices;
using TRF3.SISPREC.RelatorioPorJuizos.Dtos;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.UnidadesJudiciais;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.RelatorioPorJuizos
{
    public class RelatorioPorJuizoAppService : BaseAppService, IRelatorioPorJuizoAppService
    {
        private readonly IRequisicaoProtocoloRepository _repository;
        private readonly IExcelFileGeneratorService _excelFileGeneratorService;

        public RelatorioPorJuizoAppService(IRequisicaoProtocoloRepository repository,
                                               IExcelFileGeneratorService excelFileGeneratorService)
        {
            _repository = repository ?? throw new ArgumentException(nameof(repository));
            _excelFileGeneratorService = excelFileGeneratorService ?? throw new ArgumentException(nameof(excelFileGeneratorService));
        }

        public async Task<PagedResultDto<RelatorioPorJuizoDto>> GetListAsync(RelatorioPorJuizoGetListInputDto input)
        {
            IQueryable<RelatorioPorJuizoDto> query = await queryFilter(input);
            return new PagedResultDto<RelatorioPorJuizoDto>
            {
                TotalCount = query.Count(),
                Items = await query.ToListAsync(),
            };
        }
        public async Task<TotalizadoresDto> GetTotalizadores(string? desUnidadeJudici, DateTime? dataInicial, DateTime? dataFinal)
        {
            RelatorioPorJuizoGetListInputDto input = new RelatorioPorJuizoGetListInputDto
            {
                DesUnidadJudici = desUnidadeJudici,
                DataInicio = dataInicial,
                DataFim = dataFinal
            };
            IQueryable<RelatorioPorJuizoDto> query = await queryFilter(input);
            var resultado = await query.ToListAsync();
            return new TotalizadoresDto
            {
                RPVJuizados = resultado.Where(x => x.CodEspeciTipoJuizo == "JEF" && x.CodTipoProced == "RPV").Count(),
                RPVFederal = resultado.Where(x => x.CodEspeciTipoJuizo == "JE1" && x.CodTipoProced == "RPV").Count(),
                RPVEstadual = resultado.Where(x => x.CodEspeciTipoJuizo == "JF1" && x.CodTipoProced == "RPV").Count(),
                RPVTRF = resultado.Where(x => x.CodEspeciTipoJuizo == "TRF" && x.CodTipoProced == "RPV").Count(),
                PRCJuizados = resultado.Where(x => x.CodEspeciTipoJuizo == "JEF" && x.CodTipoProced == "PRC").Count(),
                PRCFederal = resultado.Where(x => x.CodEspeciTipoJuizo == "JE1" && x.CodTipoProced == "PRC").Count(),
                PRCEstadual = resultado.Where(x => x.CodEspeciTipoJuizo == "JF1" && x.CodTipoProced == "PRC").Count(),
                PRCTRF = resultado.Where(x => x.CodEspeciTipoJuizo == "TRF" && x.CodTipoProced == "PRC").Count(),
            };
        }
        public async Task<byte[]> ExportarExcel(RelatorioPorJuizoGetListInputDto input)
        {
            IQueryable<RelatorioPorJuizoDto> query = await queryFilter(input);
            input.Total = query.Count();
            var filtroExcel = new RelatorioPorJuizoFiltroExcelDto()
            {
                DataInicio = input.DataInicio,
                DesUnidadJudici = input.DesUnidadJudici,
                DataFim = input.DataFim,
                Total = input.Total,
            };
            return _excelFileGeneratorService.GenerateExcelFile(query.ToList(), EStyleFile.TITULO_NEGRITO, filtroExcel);
        }

        private async Task<IQueryable<RelatorioPorJuizoDto>> queryFilter(RelatorioPorJuizoGetListInputDto input)
        {
            var context = await _repository.GetDbContextAsync();
            var query = (
                    from requisicaoProtocolo in context.Set<RequisicaoProtocolo>().AsNoTracking()
                    join unidadeJudicial in context.Set<UnidadeJudicial>().AsNoTracking()
                        on requisicaoProtocolo.UnidadeJudicialId equals unidadeJudicial.Seq_Unidad_Judici
                    join unidadeJudicialOrigem in context.Set<UnidadeJudicialOrigem>().AsNoTracking()
                        on requisicaoProtocolo.UnidadeJudicialId equals unidadeJudicialOrigem.UnidadeJudicialOrigemId
                    join situacaoRequisicaoProtocolo in context.Set<SituacaoRequisicaoProtocolo>().AsNoTracking()
                        on requisicaoProtocolo.SituacaoRequisicaoId equals situacaoRequisicaoProtocolo.SituacaoRequisicaoProtocoloId
                    where requisicaoProtocolo.DataHoraProtocoloRequisicao >= input.DataInicio &&
                          requisicaoProtocolo.DataHoraProtocoloRequisicao <= input.DataFim &&
                          (string.IsNullOrEmpty(input.DesUnidadJudici) || unidadeJudicial.Descricao == input.DesUnidadJudici)
                    select new RelatorioPorJuizoDto()
                    {
                        DatHoraProtocRequis = requisicaoProtocolo.DataHoraProtocoloRequisicao,
                        NumOficioRequit = requisicaoProtocolo.NumeroOficioRequisitorio,
                        StaProtocRequis = requisicaoProtocolo.StatusProtocoloRequisicao.ToString(),
                        IdeProtocRequis = requisicaoProtocolo.IdentificadorProtocoloRequisicao.ToString(),
                        CodTipoProced = requisicaoProtocolo.TipoProcedimentoId,
                        NumProtocRequis = requisicaoProtocolo.NumeroProtocoloRequisicao,
                        CodEspeciTipoJuizo = unidadeJudicialOrigem.CodigoEspecicacaoTipoJuizo.ToString(),
                        CodSiafiUnidad = unidadeJudicial.CodigoSiafi,
                        DesUnidadJudici = unidadeJudicial.Descricao,
                        NomMagist = requisicaoProtocolo.NomeMagistrado,
                        ValRequis = requisicaoProtocolo.ValorRequisicao,
                    }
                   );
            return query;
        }
    }
}
