using TRF3.SISPREC.RequisicaoEstornos.Dtos;

namespace TRF3.SISPREC.RequisicaoEstornos;
public class RequisicaoEstornoAppService : BaseCrudAppService<RequisicaoEstorno, RequisicaoEstornoDto, string, RequisicaoEstornoGetListInput, CreateUpdateRequisicaoEstornoDto, CreateUpdateRequisicaoEstornoDto>,
    IRequisicaoEstornoAppService
{

    private readonly IRequisicaoEstornoManager _manager;
    private readonly IRequisicaoEstornoRepository _repository;

    public RequisicaoEstornoAppService(IRequisicaoEstornoRepository repository, IRequisicaoEstornoManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    protected override Task DeleteByIdAsync(string id)
    {
        return _manager.ExcluirAsync(e =>
            e.NumeroProtocoloRequisicaoId == id
        );
    }

    protected override async Task<RequisicaoEstorno> GetEntityByIdAsync(string id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.NumeroProtocoloRequisicaoId == id
            ));
    }

    protected override IQueryable<RequisicaoEstorno> ApplyDefaultSorting(IQueryable<RequisicaoEstorno> query)
    {
        return query.OrderBy(e => e.NumeroProtocoloRequisicaoId);
    }

    protected override async Task<IQueryable<RequisicaoEstorno>> CreateFilteredQueryAsync(RequisicaoEstornoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.NumeroProtocoloRequisicaoId.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicaoId.Contains(input.NumeroProtocoloRequisicaoId!))
            .WhereIf(!input.NumeroRequisicaoOriginal.IsNullOrWhiteSpace(), x => x.NumeroRequisicaoOriginal.Contains(input.NumeroRequisicaoOriginal!))
            .WhereIf(!input.NumeroBanco.IsNullOrWhiteSpace(), x => x.NumeroBanco.Contains(input.NumeroBanco!))
            .WhereIf(!input.NumeroContaCorrente.IsNullOrWhiteSpace(), x => x.NumeroContaCorrente.Contains(input.NumeroContaCorrente!))
            .WhereIf(!input.NomeBeneficiario.IsNullOrWhiteSpace(), x => x.NomeBeneficiario.Contains(input.NomeBeneficiario!))
            .WhereIf(input.CodigoBeneficiario != null, x => x.CodigoBeneficiario == input.CodigoBeneficiario)
            .WhereIf(input.DataRecolhimentoConta != null, x => x.DataRecolhimentoConta == input.DataRecolhimentoConta)
            .WhereIf(input.ValorRecolhimentoConta != null, x => x.ValorRecolhimentoConta == input.ValorRecolhimentoConta)
            .WhereIf(input.DataHoraProtocoloRequisicao != null, x => x.DataHoraProtocoloRequisicao == input.DataHoraProtocoloRequisicao);
    }
}
