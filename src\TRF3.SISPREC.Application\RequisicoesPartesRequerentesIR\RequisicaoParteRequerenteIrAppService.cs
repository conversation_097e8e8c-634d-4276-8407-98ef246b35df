using TRF3.SISPREC.RequisicoesPartesRequerentesIR.Dtos;

namespace TRF3.SISPREC.RequisicoesPartesRequerentesIR;
public class RequisicaoParteRequerenteIrAppService : BaseCrudNoDeleteAppService<RequisicaoParteRequerenteIr, RequisicaoParteRequerenteIrDto, long, RequisicaoParteRequerenteIrGetListInput, CreateUpdateRequisicaoParteRequerenteIrDto, CreateUpdateRequisicaoParteRequerenteIrDto>,
    IRequisicaoParteRequerenteIrAppService
{

    private readonly IRequisicaoParteRequerenteIrManager _manager;
    private readonly IRequisicaoParteRequerenteIrRepository _repository;

    public RequisicaoParteRequerenteIrAppService(IRequisicaoParteRequerenteIrRepository repository, IRequisicaoParteRequerenteIrManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    protected override Task DeleteByIdAsync(long id)
    {
        return _manager.ExcluirAsync(e =>
            e.RequisicaoParteRequerenteIrId == id
        );
    }

    protected override async Task<RequisicaoParteRequerenteIr> GetEntityByIdAsync(long id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.RequisicaoParteRequerenteIrId == id
            ));
    }

    protected override IQueryable<RequisicaoParteRequerenteIr> ApplyDefaultSorting(IQueryable<RequisicaoParteRequerenteIr> query)
    {
        return query.OrderBy(e => e.RequisicaoParteRequerenteIrId);
    }

    protected override async Task<IQueryable<RequisicaoParteRequerenteIr>> CreateFilteredQueryAsync(RequisicaoParteRequerenteIrGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.RequisicaoParteRequerenteIrId != null, x => x.RequisicaoParteRequerenteIrId == input.RequisicaoParteRequerenteIrId)
            .WhereIf(input.RequisicaoParteRequerenteId != null, x => x.RequisicaoParteRequerenteId == input.RequisicaoParteRequerenteId)
            .WhereIf(input.NumMesesExerciAnteri > 0, x => x.NumMesesExerciAnteri == input.NumMesesExerciAnteri)
            .WhereIf(input.ValDeducaIndivi != null, x => x.ValDeducaIndivi == input.ValDeducaIndivi)
            .WhereIf(input.NumMesExerciCorren > 0, x => x.NumMesExerciCorren == input.NumMesExerciCorren)
            .WhereIf(input.AnoExerciCorren > 0, x => x.AnoExerciCorren == input.AnoExerciCorren)
            .WhereIf(input.ValExerciCorren != null, x => x.ValExerciCorren == input.ValExerciCorren)
            .WhereIf(input.ValExerciAnteri != null, x => x.ValExerciAnteri == input.ValExerciAnteri)
            .WhereIf(input.ValAtualiDeducaIndivi != null, x => x.ValAtualiDeducaIndivi == input.ValAtualiDeducaIndivi)
            .WhereIf(input.ValAtualiExerciCorren != null, x => x.ValAtualiExerciCorren == input.ValAtualiExerciCorren)
            .WhereIf(input.ValAtualiExerciAnteri != null, x => x.ValAtualiExerciAnteri == input.ValAtualiExerciAnteri)
            .WhereIf(input.IsDeleted != null, x => x.IsDeleted == input.IsDeleted)
            .WhereIf(input.RequisicaoParteRequerenteId > 0, x => x.RequisicaoParteRequerente.RequisicaoParteId == input.RequisicaoParteRequerenteId)
            ;
    }
}
