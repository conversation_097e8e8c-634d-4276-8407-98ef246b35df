using TRF3.SISPREC.RequisicoesPartesRequerentesPSS.Dtos;

namespace TRF3.SISPREC.RequisicoesPartesRequerentesPSS
{
    public class RequisicaoParteRequerentePssAppService : BaseCrudNoDeleteAppService<RequisicaoParteRequerentePss, RequisicaoParteRequerentePssDto, long, RequisicaoParteRequerentePssGetListInput, CreateUpdateRequisicaoParteRequerentePssDto, CreateUpdateRequisicaoParteRequerentePssDto>,
    IRequisicaoParteRequerentePssAppService
    {
        private readonly IRequisicaoParteRequerentePssManager _manager;
        private readonly IRequisicaoParteRequerentePssRepository _repository;

        public RequisicaoParteRequerentePssAppService(IRequisicaoParteRequerentePssManager manager, IRequisicaoParteRequerentePssRepository repository) : base(repository, manager)
        {
            _manager = manager;
            _repository = repository;
        }

        protected override Task DeleteByIdAsync(long id)
        {
            return _manager.ExcluirAsync(e =>
                e.RequisicaoParteRequerentePssId == id
            );
        }

        protected override async Task<RequisicaoParteRequerentePss> GetEntityByIdAsync(long id)
        {
            return await AsyncExecuter.FirstOrDefaultAsync(
                (await _repository.WithDetailsAsync()).Where(e =>
                    e.RequisicaoParteRequerentePssId == id
                ));
        }

        protected override IQueryable<RequisicaoParteRequerentePss> ApplyDefaultSorting(IQueryable<RequisicaoParteRequerentePss> query)
        {
            return query.OrderBy(e => e.RequisicaoParteRequerentePssId);
        }

        protected override async Task<IQueryable<RequisicaoParteRequerentePss>> CreateFilteredQueryAsync(RequisicaoParteRequerentePssGetListInput input)
        {
            return (await base.CreateFilteredQueryAsync(input))
                .WhereIf(input.RequisicaoParteRequerentePssId > 0, x => x.RequisicaoParteRequerentePssId == input.RequisicaoParteRequerentePssId)
                .WhereIf(input.RequisicaoParteRequerenteId > 0, x => x.RequisicaoParteRequerenteId == input.RequisicaoParteRequerenteId)
                .WhereIf(input.UnidadeOrcamentariaId > 0, x => x.UnidadeOrcamentariaId == input.UnidadeOrcamentariaId)
                .WhereIf(input.IdeCondicServid > 0, x => x.IdeCondicServid == input.IdeCondicServid)
                .WhereIf(input.ValRequisPartePss > 0, x => x.ValRequisPartePss == input.ValRequisPartePss)
                .WhereIf(input.ValAtualiRequisPartePss == null, x => x.ValAtualiRequisPartePss == input.ValAtualiRequisPartePss)
                .WhereIf(input.IsDeleted != null, x => x.IsDeleted == input.IsDeleted)
                .WhereIf(input.RequisicaoParteRequerenteId > 0, x => x.RequisicaoParteRequerente.RequisicaoParteId == input.RequisicaoParteRequerenteId)
                ;
        }
    }
}
