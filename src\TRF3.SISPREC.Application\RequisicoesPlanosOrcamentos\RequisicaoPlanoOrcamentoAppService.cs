using TRF3.SISPREC.RequisicoesPlanosOrcamentos.Dtos;

namespace TRF3.SISPREC.RequisicoesPlanosOrcamentos;
public class RequisicaoPlanoOrcamentoAppService : BaseCrudAppService<RequisicaoPlanoOrcamento, RequisicaoPlanoOrcamentoDto, RequisicaoPlanoOrcamentoKey, RequisicaoPlanoOrcamentoGetListInput, CreateUpdateRequisicaoPlanoOrcamentoDto, CreateUpdateRequisicaoPlanoOrcamentoDto>,
    IRequisicaoPlanoOrcamentoAppService
{
    private readonly IRequisicaoPlanoOrcamentoManager _manager;
    private readonly IRequisicaoPlanoOrcamentoRepository _repository;

    public RequisicaoPlanoOrcamentoAppService(IRequisicaoPlanoOrcamentoRepository repository, IRequisicaoPlanoOrcamentoManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    protected override Task DeleteByIdAsync(RequisicaoPlanoOrcamentoKey id)
    {
        return _manager.ExcluirAsync(e =>
            e.NumeroProtocoloRequisicaoId == id.NumeroProtocoloRequisicaoId &&
            e.DataPlanoOrcamentoId == id.DataPlanoOrcamentoId
        );
    }

    protected override async Task<RequisicaoPlanoOrcamento> GetEntityByIdAsync(RequisicaoPlanoOrcamentoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.NumeroProtocoloRequisicaoId == id.NumeroProtocoloRequisicaoId &&
                e.DataPlanoOrcamentoId == id.DataPlanoOrcamentoId
            ));
    }

    protected override IQueryable<RequisicaoPlanoOrcamento> ApplyDefaultSorting(IQueryable<RequisicaoPlanoOrcamento> query)
    {
        return query.OrderBy(e => e.NumeroProtocoloRequisicaoId);
    }

    protected override async Task<IQueryable<RequisicaoPlanoOrcamento>> CreateFilteredQueryAsync(RequisicaoPlanoOrcamentoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.NumeroProtocoloRequisicaoId != null, x => x.NumeroProtocoloRequisicaoId == input.NumeroProtocoloRequisicaoId)
            .WhereIf(input.DataPlanoOrcamentoId != null, x => x.DataPlanoOrcamentoId == input.DataPlanoOrcamentoId)
            .WhereIf(input.SentencaTipoId > 0, x => x.SentencaTipoId == input.SentencaTipoId)
            .WhereIf(input.BeneficiarioIdentificacaoTipoId > 0, x => x.BeneficiarioIdentificacaoTipoId == input.BeneficiarioIdentificacaoTipoId)
            .WhereIf(input.TipoMovimentoId > 0, x => x.TipoMovimentoId == input.TipoMovimentoId)
            .WhereIf(input.TipoDespesaId > 0, x => x.TipoDespesaId == input.TipoDespesaId)
            .WhereIf(input.DespesaNaturezaId > 0, x => x.DespesaNaturezaId == input.DespesaNaturezaId)
            .WhereIf(input.SentencaTipoId > 0, x => x.SentencaTipoId == input.SentencaTipoId)
            .WhereIf(input.BeneficiarioIdentificacaoTipoId > 0, x => x.BeneficiarioIdentificacaoTipoId == input.BeneficiarioIdentificacaoTipoId)
            .WhereIf(input.TipoMovimentoId > 0, x => x.TipoMovimentoId == input.TipoMovimentoId)
            .WhereIf(input.TipoDespesaId > 0, x => x.TipoDespesaId == input.TipoDespesaId)
            .WhereIf(input.DespesaNaturezaId > 0, x => x.DespesaNaturezaId == input.DespesaNaturezaId)
            ;
    }
}
