using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesProcessosOrigens.Dtos;

namespace TRF3.SISPREC.RequisicoesProcessosOrigens;
public class RequisicaoProcessoOrigemAppService : BaseCrudAppService<RequisicaoProcessoOrigem, RequisicaoProcessoOrigemDto, RequisicaoProcessoOrigemKey, RequisicaoProcessoOrigemGetListInput, CreateUpdateRequisicaoProcessoOrigemDto, CreateUpdateRequisicaoProcessoOrigemDto>,
    IRequisicaoProcessoOrigemAppService
{
    protected override string VisualizarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProcessoOrigem.Visualizar;
    protected override string GravarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProcessoOrigem.Gravar;

    private readonly IRequisicaoProcessoOrigemManager _manager;
    private readonly IRequisicaoProcessoOrigemRepository _repository;

    public RequisicaoProcessoOrigemAppService(IRequisicaoProcessoOrigemRepository repository, IRequisicaoProcessoOrigemManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    protected override Task DeleteByIdAsync(RequisicaoProcessoOrigemKey id)
    {
        return _manager.ExcluirAsync(e =>
            e.NumeroProcessoOriginario == id.NumeroProcessoOriginario &&
            e.NumeroProtocoloRequisicao == id.NumeroProtocoloRequisicao
        );
    }

    protected override async Task<RequisicaoProcessoOrigem> GetEntityByIdAsync(RequisicaoProcessoOrigemKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.NumeroProcessoOriginario == id.NumeroProcessoOriginario &&
                e.NumeroProtocoloRequisicao == id.NumeroProtocoloRequisicao
            ));
    }

    protected override IQueryable<RequisicaoProcessoOrigem> ApplyDefaultSorting(IQueryable<RequisicaoProcessoOrigem> query)
    {
        return query.OrderBy(e => e.NumeroProcessoOriginario);
    }

    protected override async Task<IQueryable<RequisicaoProcessoOrigem>> CreateFilteredQueryAsync(RequisicaoProcessoOrigemGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.NumeroProcessoOriginario.IsNullOrWhiteSpace(), x => x.NumeroProcessoOriginario.Contains(input.NumeroProcessoOriginario))
            .WhereIf(!input.NumeroProtocoloRequisicao.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.NumeroProtocoloRequisicao))
            .WhereIf(input.UnidadeJudicialOrigemId > 0, x => x.UnidadeJudicialId == input.UnidadeJudicialOrigemId)
            .WhereIf(input.DataProtocoloProcessoOriginal != null, x => x.DataProtocoloProcessoOriginal == input.DataProtocoloProcessoOriginal)
            .WhereIf(input.UnidadeJudicialOrigemId > 0, x => x.UnidadeJudicial.Seq_Unidad_Judici == input.UnidadeJudicialOrigemId)
            ;
    }
}
