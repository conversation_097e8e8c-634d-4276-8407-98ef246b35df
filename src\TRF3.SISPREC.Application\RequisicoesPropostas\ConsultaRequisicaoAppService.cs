using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Pdf;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesPartes;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesProcessosOrigens.Dtos;
using TRF3.SISPREC.RequisicoesPropostas.Dtos;
using TRF3.SISPREC.RequisicoesPropostas.EntidadesPDF;
using TRF3.SISPREC.ViewRequisicoes.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicoesPropostas;

[Authorize(Policy = SISPRECPermissoes.RequisicaoProtocolo.Visualizar)]
public class ConsultaRequisicaoAppService : BaseAppService, IConsultaRequisicaoAppService
{
    private readonly IConsultaRequisicaoRepository _repository;
    private readonly IPdfFileGeneratorService _pdfService;

    public ConsultaRequisicaoAppService(IConsultaRequisicaoRepository repository, IPdfFileGeneratorService pdfService)
    {
        _repository = repository;
        _pdfService = pdfService;
    }

    public async Task<PagedResultDto<RequisicaoPropostaDto>> GetRequisicoes(ViewRequisicaoGetListInput input)
    {

        if (input.AnoPropos is null)
        {
            throw new UserFriendlyException("O campo Ano é obrigatório.");
        }

        var requisicoes = ObjectMapper.Map<List<RequisicaoProposta>, List<RequisicaoPropostaDto>>(
            await _repository.ListarRequisicoes(input.CodTipoProced?.ToString(), input.AnoPropos, input.MesPropos, input.NumProtocRequis));

        return new PagedResultDto<RequisicaoPropostaDto>
        {
            TotalCount = requisicoes.Count,
            Items = requisicoes
        };
    }

    public async Task<DetalhesRequisicaoPrincipalDto> GetDetalhesRequisicao(string numProtocRequis)
    {
        return ObjectMapper.Map<DetalhesRequisicaoPrincipal, DetalhesRequisicaoPrincipalDto>
            (await _repository.ObterDetalhesRequisicao(numProtocRequis));
    }

    public async Task<DetalhesRequisicaoRequeridoCompensacaoDto> GetDetalhesRequisicaoRequeridoCompensacao(string numProtocRequis)
    {
        return ObjectMapper.Map<DetalhesRequisicaoRequeridoCompensacao, DetalhesRequisicaoRequeridoCompensacaoDto>
            (await _repository.ObterDetalhesRequsicaoRequeridoCompensacao(numProtocRequis));
    }

    public async Task<RequisicaoPropostaDto> GetDetalhesRequisicaoOriginarios(string numProtocRequis)
    {
        return ObjectMapper.Map<RequisicaoProposta, RequisicaoPropostaDto>
            (await _repository.ObterDetalhesRequisicaoOriginarios(numProtocRequis));
    }

    public async Task<DetalhesRequisicaoIrPssDto> GetDetalhesRequisicaoIrPss(string numProtocRequis)
    {
        return ObjectMapper.Map<DetalhesRequisicaoIrPss, DetalhesRequisicaoIrPssDto>
            (await _repository.ObterDetalhesRequisicaoIrPss(numProtocRequis));
    }

    public async Task<DetalhesRequisicaoRequerenteHonorariosDto> GetDetalhesRequisicaoRequerenteHonorarios(string numProtocRequis, DateTime dataHoraProtocoloRequisicao, DateTime dataHoraContaLiquidacao)
    {
        return ObjectMapper.Map<DetalhesRequisicaoRequerenteHonorarios, DetalhesRequisicaoRequerenteHonorariosDto>
            (await _repository.ObterDetalhesRequisicaoRequerenteHonorarios(numProtocRequis, dataHoraProtocoloRequisicao, dataHoraContaLiquidacao));
    }

    public async Task<ListResultDto<DetalhesRequisicaoRequerenteHonorariosListagemDto>> GetDetalhesRequisicaoRequerenteHonorariosListagem(string numProtocRequis)
    {
        var requisicoes = ObjectMapper.Map<List<DetalhesRequisicaoRequerenteHonorariosListagem>, List<DetalhesRequisicaoRequerenteHonorariosListagemDto>>
            (await _repository.ObterDetalhesRequisicaoRequerenteHonorariosListagem(numProtocRequis));

        return new ListResultDto<DetalhesRequisicaoRequerenteHonorariosListagemDto>
        {
            Items = requisicoes
        };
    }

    public async Task<PagedResultDto<DetalhesRequisicaoAutorExpedienteListagemDto>> GetDetalhesRequisicaoAutorExpedienteListagem(GetRequisicoesPropostasInput numProtocRequis)
    {
        var autores = await _repository.ObterDetalhesRequisicaoAutorExpedienteListagem(numProtocRequis.NumeroRequisicaoProtocolo);

        autores = autores.OrderByIf<DetalhesRequisicaoAutorExpedienteListagem, IQueryable<DetalhesRequisicaoAutorExpedienteListagem>>(!string.IsNullOrWhiteSpace(numProtocRequis.Sorting), numProtocRequis.Sorting)
                     .OrderByIf<DetalhesRequisicaoAutorExpedienteListagem, IQueryable<DetalhesRequisicaoAutorExpedienteListagem>>(string.IsNullOrWhiteSpace(numProtocRequis.Sorting), "numeroProtocoloRequisicao desc")
                     .PageBy(numProtocRequis)
                     .AsQueryable();

        var count = await autores.CountAsync();

        var result = await autores.ToListAsync();

        var autoresExpediente = ObjectMapper.Map<List<DetalhesRequisicaoAutorExpedienteListagem>, List<DetalhesRequisicaoAutorExpedienteListagemDto>>(result);

        return new PagedResultDto<DetalhesRequisicaoAutorExpedienteListagemDto>
        {
            TotalCount = count,
            Items = autoresExpediente
        };
    }

    public async Task<DetalhesRequisicaoAutorExpedienteDto> GetDetalhesRequisicaoAutorExpediente(string numProtocRequis)
    {
        return ObjectMapper.Map<DetalhesRequisicaoAutorExpediente, DetalhesRequisicaoAutorExpedienteDto>
            (await _repository.ObterDetalhesRequisicaoAutorExpediente(numProtocRequis));
    }

    public async Task<PagedResultDto<DetalhesRequisicaoRequerenteDto>> GetRequisicaoRequerente(string cnpjCpf)
    {
        var requerentes = ObjectMapper.Map<List<DetalhesRequisicaoRequerente>, List<DetalhesRequisicaoRequerenteDto>>(await _repository.ObterDetalhesRequisicaoRequerente(cnpjCpf));

        return new PagedResultDto<DetalhesRequisicaoRequerenteDto>
        {
            TotalCount = requerentes.Count,
            Items = requerentes,
        };
    }

    public async Task<PagedResultDto<ReferenciaReinclusaoReferencia>> GetRequisicaoReferencias(GetRequisicoesPropostasInput requisicaoPropostaInput)
    {
        var query = await _repository.ObterRequisicaoReinclusaoReferencias(requisicaoPropostaInput.NumeroRequisicaoProtocolo);

        var count = await query.CountAsync();

        query = query.OrderByIf<ReferenciaReinclusaoReferencia, IQueryable<ReferenciaReinclusaoReferencia>>(!string.IsNullOrWhiteSpace(requisicaoPropostaInput.Sorting), requisicaoPropostaInput.Sorting!)
                .OrderByIf<ReferenciaReinclusaoReferencia, IQueryable<ReferenciaReinclusaoReferencia>>(string.IsNullOrWhiteSpace(requisicaoPropostaInput.Sorting), "nomePessoaRequerente desc")
                .PageBy(requisicaoPropostaInput)
                .AsQueryable();

        return new PagedResultDto<ReferenciaReinclusaoReferencia>
        {
            TotalCount = count,
            Items = await query.ToListAsync(),
        };
    }

    public async Task<PagedResultDto<ReferenciaReinclusao>> GetRequisicaoReinclusao(GetRequisicoesPropostasInput requisicaoPropostaInput)
    {
        var query = await _repository.ObterRequisicaoReinclusao(requisicaoPropostaInput.NumeroRequisicaoProtocolo);
        var count = await query.CountAsync();

        query = query.OrderByIf<ReferenciaReinclusao, IQueryable<ReferenciaReinclusao>>(!string.IsNullOrWhiteSpace(requisicaoPropostaInput.Sorting), requisicaoPropostaInput.Sorting!)
                .OrderByIf<ReferenciaReinclusao, IQueryable<ReferenciaReinclusao>>(string.IsNullOrWhiteSpace(requisicaoPropostaInput.Sorting), "dataHoraProtocRequisicao desc")
                .PageBy(requisicaoPropostaInput)
                .AsQueryable();

        return new PagedResultDto<ReferenciaReinclusao>
        {
            TotalCount = count,
            Items = await query.ToListAsync(),
        };
    }


    public async Task<PagedResultDto<DetalhesRequisicaoOcorrencias>> GetDetalhesRequisicaoOcorrencias(GetRequisicoesPropostasInput requisicaoPropostaInput)
    {

        var ocorrencias = await _repository.GetOcorrencias(!string.IsNullOrEmpty(requisicaoPropostaInput.NumeroRequisicaoProtocolo) ? requisicaoPropostaInput.NumeroRequisicaoProtocolo : string.Empty);

        var count = await ocorrencias.CountAsync();

        ocorrencias = ocorrencias.OrderByIf<DetalhesRequisicaoOcorrencias, IQueryable<DetalhesRequisicaoOcorrencias>>(!string.IsNullOrWhiteSpace(requisicaoPropostaInput.Sorting), !string.IsNullOrEmpty(requisicaoPropostaInput.Sorting) ? requisicaoPropostaInput.Sorting : string.Empty)
            .OrderByIf<DetalhesRequisicaoOcorrencias, IQueryable<DetalhesRequisicaoOcorrencias>>(string.IsNullOrWhiteSpace(requisicaoPropostaInput.Sorting), "dataRequisicaoOcorrencia desc")
            .PageBy(requisicaoPropostaInput)
            .AsQueryable();

        var result = await ocorrencias.ToListAsync();

        var resultPaged = new PagedResultDto<DetalhesRequisicaoOcorrencias>
        {
            TotalCount = count,
            Items = result,
        };

        return resultPaged;
    }


    [HttpGet]
    public async Task<byte[]> GerarPDF(string numProtocRequis)
    {
        var dto = new RequisicaoPropostaPdfDto
        {
            ReferenciaPdfDto = new ReferenciasListaPdfDto
            {
                Referencias = ObjectMapper.Map<List<RequisicaoReferenciaPdf>, List<RequisicaoReferenciaDto>>
                (await _repository.ObterRequisicaoReferencia(numProtocRequis)),
            },

            ReinclusaoPdfDto = ObjectMapper.Map<RequisicaoReinclusaoPdf, RequisicaoReinclusaoDto>
                (await _repository.ObterReinclucao(numProtocRequis)),

            ConsultaCpf = new ConsultaCpfListaPdfDto
            {
                ConsultaCpf = ObjectMapper.Map<List<ConsultaCpfPdf>, List<ConsultaCpfDto>>
                (await _repository.ObterConsultaCpf(numProtocRequis))
            },

            RequisicaoPdfDto = ObjectMapper.Map<RequisicaoPdf, RequisicaoPdfDto>
                (await _repository.ObterDetalhesRequisicaoPdf(numProtocRequis)),

            MagistradoPdfDto = ObjectMapper.Map<MagistradoPdf, MagistradoPdfDto>
                (await _repository.ObterDetalhesRequisicaoMagistradoPdf(numProtocRequis)),

            RequeridoPdfDto = ObjectMapper.Map<RequeridoPdf, RequeridoPdfDto>
                (await _repository.ObterDetalhesRequisicaoRequeridoPdf(numProtocRequis)),

            ParteAutoraPdfDto = ObjectMapper.Map<ParteAutoraPdf, ParteAutoraPdfDto>
                (await _repository.ObterDetalhesRequisicaoParteAutoraPdf(numProtocRequis)),

            RequerentePdfDto = ObjectMapper.Map<RequerentePdf, RequerentePdfDto>
                (await _repository.ObterDetalhesRequerentePdf(numProtocRequis)),

            RequerentesEcontratuaisPdfDto = ObjectMapper.Map<List<RequerentesEcontratuaisPdf>, List<RequerentesEcontratuaisPdfDto>>
                (await _repository.ObterDetalhesRequerentesEcontratuais(numProtocRequis)),

            AdvogadoPdfDto = ObjectMapper.Map<AdvogadoPdf, AdvogadoPdfDto>
                (await _repository.ObterDetalhesAdvogadoPdf(numProtocRequis)),

            ImpostoDeRendaPdfDto = ObjectMapper.Map<ImpostoDeRendaPdf, ImpostoDeRendaPdfDto>
                (await _repository.ObterDetalhesImpostoDeRendaPdf(numProtocRequis)),

            OriginarioListaPdfDto = new OriginarioListaPdfDto
            {
                Originarios = ObjectMapper.Map<List<OriginariosPdf>, List<OriginariosPdfDto>>
                (await _repository.ObterDetalhesRequisicaoOriginarioPdf(numProtocRequis))
            },

            ExpedientePdfDto = new ExpedientesListaPdfDto
            {
                Expedientes = ObjectMapper.Map<List<ExpedientePdf>, List<ExpedientePdfDto>>
                (await _repository.ObterDetalhesExpedientePdf(numProtocRequis))
            },

            ParcelasPdfDto = new ParcelasListaPdfDto
            {
                Parcelas = ObjectMapper.Map<List<ParcelasPdf>, List<ParcelasPdfDto>>
                (await _repository.ObterDetalhesParcelasPdf(numProtocRequis))
            },

            OcorrenciasPdfDto = new OcorrenciasListaPdfDto
            {
                Ocorrencias = ObjectMapper.Map<List<OcorrenciasPdf>, List<OcorrenciasPdfDto>>
                (await _repository.ObterOcorrenciasPdf(numProtocRequis))
            }
        };

        if (dto.ExpedientePdfDto.Expedientes == null || dto.ExpedientePdfDto.Expedientes.Count == 0)
        {
            dto.ExpedientePdfDto.OcultarTabela = PdfConsts.HIDDEN;
            dto.ExpedientePdfDto.OcultarParagrafo = string.Empty;
        }

        if (dto.AdvogadoPdfDto == null)
            dto.AdvogadoPdfDto = new AdvogadoPdfDto("hidden", string.Empty);

        if (dto.OriginarioListaPdfDto.Originarios == null || dto.OriginarioListaPdfDto.Originarios.Count == 0)
        {
            dto.OriginarioListaPdfDto.OcultarTabela = PdfConsts.HIDDEN;
            dto.OriginarioListaPdfDto.OcultarParagrafo = string.Empty;
        }

        if (dto.ParcelasPdfDto.Parcelas == null || dto.ParcelasPdfDto.Parcelas.Count == 0)
        {
            dto.ParcelasPdfDto.OcultarTabela = PdfConsts.HIDDEN;
            dto.ParcelasPdfDto.OcultarParagrafo = string.Empty;
        }

        if (dto.OcorrenciasPdfDto.Ocorrencias == null || dto.OcorrenciasPdfDto.Ocorrencias.Count == 0)
        {
            dto.OcorrenciasPdfDto.OcultarTabela = PdfConsts.HIDDEN;
            dto.OcorrenciasPdfDto.OcultarParagrafo = string.Empty;
        }

        if (dto.ReferenciaPdfDto.Referencias == null || dto.ReferenciaPdfDto.Referencias.Count == 0)
        {
            dto.ReferenciaPdfDto.OcultarTabela = PdfConsts.HIDDEN;
            dto.ReferenciaPdfDto.OcultarParagrafo = string.Empty;
        }

        if (dto.ReinclusaoPdfDto == null)
            dto.ReinclusaoPdfDto = new RequisicaoReinclusaoDto("hidden", string.Empty);

        if (dto.ConsultaCpf.ConsultaCpf == null || dto.ConsultaCpf.ConsultaCpf.Count == 0)
        {
            dto.ConsultaCpf.OcultarTabela = PdfConsts.HIDDEN;
            dto.ConsultaCpf.OcultarParagrafo = string.Empty;
        }

        return await _pdfService.GenerateFileAsync("DetalhesRequisicaoTemplate", dto, "EXTRATO DE REQUISIÇÃO PARA SIMPLES CONFERÊNCIA");
    }

    public async Task<PagedResultDto<DetalhesRequisicaoOriginariosDto>> GetRequisicaoOriginarios(GetRequisicaoOriginariosInput numProtocRequis)
    {

        var originarios = await _repository.GetOriginarios(numProtocRequis.NumeroRequisicaoProtocolo);

        originarios = originarios.OrderByIf<DetalhesRequisicaoOriginarios, IQueryable<DetalhesRequisicaoOriginarios>>(!string.IsNullOrWhiteSpace(numProtocRequis.Sorting), numProtocRequis.Sorting)
                     .OrderByIf<DetalhesRequisicaoOriginarios, IQueryable<DetalhesRequisicaoOriginarios>>(string.IsNullOrWhiteSpace(numProtocRequis.Sorting), "numeroProtocoloRequisicao desc")
                     .PageBy(numProtocRequis)
                     .AsQueryable();

        var count = await originarios.CountAsync();

        var result = await originarios.ToListAsync();

        var requisicaOriginarios = ObjectMapper.Map<List<DetalhesRequisicaoOriginarios>, List<DetalhesRequisicaoOriginariosDto>>(result);

        return new PagedResultDto<DetalhesRequisicaoOriginariosDto>
        {
            TotalCount = count,
            Items = requisicaOriginarios
        };
    }

    public async Task<ListResultDto<DetalhesRequisicaoInformacoesParcelas>> GetDetalhesRequisicaoInformacoesParcelas(string numProtocRequis)
    {
        var informacoesParcelas = await _repository.ObterInformacoesParcelas(numProtocRequis);

        informacoesParcelas = informacoesParcelas
            .OrderByIf<DetalhesRequisicaoInformacoesParcelas, IQueryable<DetalhesRequisicaoInformacoesParcelas>>
            (!string.IsNullOrWhiteSpace(numProtocRequis), numProtocRequis).AsQueryable();

        var result = await informacoesParcelas.ToListAsync();

        return new ListResultDto<DetalhesRequisicaoInformacoesParcelas>
        {
            Items = result
        };
    }

    public async Task<PagedResultDto<RequisicaoProcessoOrigemDto>> GetDetalhesRequisicaoOriginariosListagem(string numProtocRequis)
    {
        var requisicoes = ObjectMapper.Map<List<RequisicaoProcessoOrigem>, List<RequisicaoProcessoOrigemDto>>
            (await _repository.ObterDetalhesRequisicaoOriginariosListagem(numProtocRequis));

        return new PagedResultDto<RequisicaoProcessoOrigemDto>
        {
            TotalCount = requisicoes.Count,
            Items = requisicoes
        };
    }

    public async Task<PagedResultDto<DetalhesRequisicaoConsultaCpf>> GetDetalhesRequisicaoConsultaCpf(GetRequisicaoConsultaCpfInput input)
    {
        var requisicaoPartesQueryable = await _repository.ObterDetalhesRequisicaoConsultaCpf(input.NumeroRequisicaoProtocolo);

        requisicaoPartesQueryable = requisicaoPartesQueryable.OrderByIf<RequisicaoParte, IQueryable<RequisicaoParte>>(!string.IsNullOrWhiteSpace(input.Sorting) && !input.Sorting.Contains("tipoParte"), input.Sorting!)
            .OrderByIf<RequisicaoParte, IQueryable<RequisicaoParte>>(string.IsNullOrWhiteSpace(input.Sorting), "tipoParte desc, pessoa.nome asc")
            .OrderByIf<RequisicaoParte, IQueryable<RequisicaoParte>>(!string.IsNullOrWhiteSpace(input.Sorting) && input.Sorting.Contains("tipoParte desc"), "tipoParte desc, pessoa.nome asc")
            .OrderByIf<RequisicaoParte, IQueryable<RequisicaoParte>>(!string.IsNullOrWhiteSpace(input.Sorting) && input.Sorting.Contains("tipoParte asc"), "tipoParte asc, pessoa.nome asc");

        var requisicaoPartes = await requisicaoPartesQueryable.ToListAsync();

        var list = new List<DetalhesRequisicaoConsultaCpf>();

        foreach (var item in requisicaoPartes)
        {
            var verificacaoCnpjCpf = item.VerificacaoCnpjCpf.FirstOrDefault(x => x.MaisRecente);
            list.Add(new DetalhesRequisicaoConsultaCpf
            {
                TipoParte = item.TipoParte.GetEnumDescription(),
                Nome = item.Pessoa.Nome,
                CpfCnpj = item.Pessoa.NumeroCnpjCpf,
                NomeReceita = verificacaoCnpjCpf?.NomePessoaReceita,
                DataNascimentoReceita = verificacaoCnpjCpf?.DataNascimentoReceita,
                SituacaoCadastral = verificacaoCnpjCpf?.SituacaoCadastral,
                DataVerificacao = verificacaoCnpjCpf?.DataVerificacao,
                Divergencia = verificacaoCnpjCpf?.Divergente,
                Erro = verificacaoCnpjCpf?.DescricaoErro
            });
        }

        var result = new PagedResultDto<DetalhesRequisicaoConsultaCpf>
        {
            Items = list,
            TotalCount = list.Count
        };

        return result;
    }
}