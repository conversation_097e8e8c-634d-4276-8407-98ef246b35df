using System.Linq.Dynamic.Core;
using TRF3.SISPREC.PDFServices;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesPropostas.Dtos;

namespace TRF3.SISPREC.RequisicoesPropostas;

public class RequisicaoPropostaAppService : BaseCrudAppService<RequisicaoProposta, RequisicaoPropostaDto, RequisicaoPropostaKey, RequisicaoPropostaGetListInput, CreateUpdateRequisicaoPropostaDto, CreateUpdateRequisicaoPropostaDto>,
    IRequisicaoPropostaAppService
{
    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProtocolo.Visualizar;
    protected override string? GravarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProtocolo.Gravar;

    private readonly IRequisicaoPropostaManager _manager;
    private readonly IRequisicaoPropostaRepository _repository;

    public RequisicaoPropostaAppService(IRequisicaoPropostaRepository repository, IRequisicaoPropostaManager manager, IPdfFileGeneratorService pdfService) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    public override Task<RequisicaoPropostaDto> CreateAsync(CreateUpdateRequisicaoPropostaDto input)
    {
        return base.CreateAsync(input);
    }

    public override Task<RequisicaoPropostaDto> UpdateAsync(RequisicaoPropostaKey id, CreateUpdateRequisicaoPropostaDto input)
    {
        return base.UpdateAsync(id, input);
    }

    protected override Task DeleteByIdAsync(RequisicaoPropostaKey id)
    {
        return _manager.ExcluirAsync(e =>
            e.PropostaId == id.PropostaId &&
            e.NumeroProtocoloRequisicao == id.NumeroProtocoloRequisicao
        );
    }

    protected override async Task<RequisicaoProposta> GetEntityByIdAsync(RequisicaoPropostaKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.PropostaId == id.PropostaId &&
                e.NumeroProtocoloRequisicao == id.NumeroProtocoloRequisicao
            ));
    }

    protected override IQueryable<RequisicaoProposta> ApplyDefaultSorting(IQueryable<RequisicaoProposta> query)
    {
        return query.OrderBy(e => e.PropostaId);
    }

    protected override async Task<IQueryable<RequisicaoProposta>> CreateFilteredQueryAsync(RequisicaoPropostaGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.PropostaId > 0, x => x.PropostaId == input.PropostaId)
            .WhereIf(!input.NumeroProtocoloRequisicao.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.NumeroProtocoloRequisicao!))
            .WhereIf(input.SituacaoRequisicaoProposta != null, x => x.SituacaoRequisicaoProposta == input.SituacaoRequisicaoProposta)
            .WhereIf(input.PropostaInicial != null, x => x.PropostaInicial == input.PropostaInicial)
            .WhereIf(input.NumBanco != null, x => x.NumBanco == input.NumBanco)
            .WhereIf(input.NumAgencia != null, x => x.NumAgencia == input.NumAgencia)
            .WhereIf(input.NumContaCorrente != null, x => x.NumContaCorrente == input.NumContaCorrente)
            ;
    }


}
