using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicoesProtocolos;
public class RequisicaoProtocoloAppService : BaseCrudAppService<RequisicaoProtocolo, RequisicaoProtocoloDto, string, RequisicaoProtocoloGetListInput, CreateUpdateRequisicaoProtocoloDto, CreateUpdateRequisicaoProtocoloDto>,
    IRequisicaoProtocoloAppService
{
    protected override string VisualizarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProtocolo.Visualizar;
    protected override string GravarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProtocolo.Gravar;

    private readonly IRequisicaoProtocoloRepository _repository;

    public RequisicaoProtocoloAppService(IRequisicaoProtocoloRepository repository, IRequisicaoProtocoloManager manager) : base(repository, manager)
    {
        _repository = repository;
    }

    [RemoteService(false)]
    public override Task DeleteAsync(string id)
    {
        throw new InvalidOperationException("Não é possível excluir uma requisição.");
    }

    [RemoteService(false)]
    public override async Task<RequisicaoProtocoloDto> CreateAsync(CreateUpdateRequisicaoProtocoloDto input)
    {
        throw new InvalidOperationException("Não é possível criar uma requisição pelo serviço de aplicação.");
    }

    protected override async Task<RequisicaoProtocolo> GetEntityByIdAsync(string id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
             (await _repository.WithDetailsAsync()).Where(e =>
                 e.NumeroProtocoloRequisicao == id
             )) ?? new RequisicaoProtocolo();
    }

    public async Task<RequisicaoProtocoloTableDto?> GetRequisicaoByIdAsync(string id)
    {
        var resultado = await (await _repository.WithDetailsAsync())
            .Where(e => e.NumeroProtocoloRequisicao == id)
            .Select(e => new RequisicaoProtocoloTableDto
            {
                RequisicaoProtocoloId = e.NumeroProtocoloRequisicao,

                CpfCnpj = e.RequisicaoPartes
                    .Where(rp => rp.TipoParte == Enums.ETipoRequisicaoParte.REQUERENTE)
                    .Select(rp => rp.Pessoa.NumeroCnpjCpf)
                    .FirstOrDefault() ?? "",

                Requerente = e.RequisicaoPartes
                    .Where(rp => rp.TipoParte == Enums.ETipoRequisicaoParte.REQUERENTE)
                    .Select(rp => rp.Pessoa.Nome)
                    .FirstOrDefault() ?? "",

                CodSiafi = e.UnidadeJudicial != null ? e.UnidadeJudicial.CodigoSiafi : null,
                Juizo = e.UnidadeJudicial != null ? e.UnidadeJudicial.Descricao : null
            })
            .FirstOrDefaultAsync();

        if (resultado == null)
            throw new UserFriendlyException("Requisição não encontrada!");

        return resultado;
    }
    public override Task<PagedResultDto<RequisicaoProtocoloDto>> GetListAsync(RequisicaoProtocoloGetListInput input)
    {
        return base.GetListAsync(input);
    }

    protected override IQueryable<RequisicaoProtocolo> ApplyDefaultSorting(IQueryable<RequisicaoProtocolo> query)
    {
        return query.OrderBy(e => e.NumeroProtocoloRequisicao);
    }

    protected override async Task<IQueryable<RequisicaoProtocolo>> CreateFilteredQueryAsync(RequisicaoProtocoloGetListInput input)
    {
        var querable = (await base.CreateFilteredQueryAsync(input));
        return querable
            .WhereIf(!input.NumeroProtocoloRequisicao.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.NumeroProtocoloRequisicao!));
    }
}
