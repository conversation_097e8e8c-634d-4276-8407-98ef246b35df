using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.RequisicoesProtocolos;
public class RequisicaoProtocoloAppService : BaseCrudAppService<RequisicaoProtocolo, RequisicaoProtocoloDto, string, RequisicaoProtocoloGetListInput, CreateUpdateRequisicaoProtocoloDto, CreateUpdateRequisicaoProtocoloDto>,
    IRequisicaoProtocoloAppService
{
    protected override string VisualizarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProtocolo.Visualizar;
    protected override string GravarPolicyName { get; set; } = SISPRECPermissoes.RequisicaoProtocolo.Gravar;

    //private readonly IRequisicaoProtocoloManager _manager;
    private readonly IRequisicaoProtocoloRepository _repository;

    public RequisicaoProtocoloAppService(IRequisicaoProtocoloRepository repository, IRequisicaoProtocoloManager manager) : base(repository, manager)
    {
        _repository = repository;
        //_manager = manager;
    }

    [RemoteService(false)]
    public override Task DeleteAsync(string id)
    {
        throw new InvalidOperationException("Não é possível excluir uma requisição.");
    }

    [RemoteService(false)]
    public override async Task<RequisicaoProtocoloDto> CreateAsync(CreateUpdateRequisicaoProtocoloDto input)
    {
        throw new InvalidOperationException("Não é possível criar uma requisição pelo serviço de aplicação.");
    }

    protected override async Task<RequisicaoProtocolo> GetEntityByIdAsync(string id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.NumeroProtocoloRequisicao == id
            ));

    }

    public override Task<PagedResultDto<RequisicaoProtocoloDto>> GetListAsync(RequisicaoProtocoloGetListInput input)
    {
        return base.GetListAsync(input);
    }

    protected override IQueryable<RequisicaoProtocolo> ApplyDefaultSorting(IQueryable<RequisicaoProtocolo> query)
    {
        return query.OrderBy(e => e.NumeroProtocoloRequisicao);
    }

    protected override async Task<IQueryable<RequisicaoProtocolo>> CreateFilteredQueryAsync(RequisicaoProtocoloGetListInput input)
    {
        var querable = (await base.CreateFilteredQueryAsync(input));
        return querable
            .WhereIf(!input.NumeroProtocoloRequisicao.IsNullOrWhiteSpace(), x => x.NumeroProtocoloRequisicao.Contains(input.NumeroProtocoloRequisicao))
            //.WhereIf(input.AssuntoId > 0, x => x.AssuntoId == input.AssuntoId)
            //.WhereIf(input.TipoProcedimentoId.ToString().IsNullOrWhiteSpace(), x => x.TipoProcedimentoId.Contains(input.TipoProcedimentoId.ToString()))
            //.WhereIf(input.TipoHonorario != null, x => x.TipoHonorario == input.TipoHonorario)
            //.WhereIf(input.RenunciaValorLimite != null, x => x.RenunciaValorLimite == input.RenunciaValorLimite)
            //.WhereIf(input.ValorRequisicao != null, x => x.ValorRequisicao == input.ValorRequisicao)
            //.WhereIf(input.DataContaLiquidacao != null, x => x.DataContaLiquidacao == input.DataContaLiquidacao)
            //.WhereIf(input.TipoRequisicao != null, x => x.TipoRequisicao == input.TipoRequisicao)
            //.WhereIf(input.ValorConta != null, x => x.ValorConta == input.ValorConta)
            //.WhereIf(input.DataConta != null, x => x.DataConta == input.DataConta)
            //.WhereIf(input.DataTransitoJulgadoFase != null, x => x.DataTransitoJulgadoFase == input.DataTransitoJulgadoFase)
            //.WhereIf(input.DataTransitoJulgadoEmbargos != null, x => x.DataTransitoJulgadoEmbargos == input.DataTransitoJulgadoEmbargos)
            //.WhereIf(!input.NomeMagistrado.IsNullOrWhiteSpace(), x => x.NomeMagistrado.Contains(input.NomeMagistrado))
            //.WhereIf(input.DesignacaoMagistrado != null, x => x.DesignacaoMagistrado == input.DesignacaoMagistrado)
            //.WhereIf(input.NaturezaCredito != null, x => x.NaturezaCredito == input.NaturezaCredito)
            //.WhereIf(input.DesapropriacaoUnicoImovel != null, x => x.DesapropriacaoUnicoImovel == input.DesapropriacaoUnicoImovel)
            //.WhereIf(input.ValorAtualizadoRequisicao != null, x => x.ValorAtualizadoRequisicao == input.ValorAtualizadoRequisicao)
            //.WhereIf(input.SituacaoRequisicaoId > 0, x => x.SituacaoRequisicaoId == input.SituacaoRequisicaoId)
            //.WhereIf(input.ExecucaoFiscal != null, x => x.ExecucaoFiscal == input.ExecucaoFiscal)
            //.WhereIf(input.ValorCompensacao != null, x => x.ValorCompensacao == input.ValorCompensacao)
            //.WhereIf(input.BloqueioDepositoJudicial != null, x => x.BloqueioDepositoJudicial == input.BloqueioDepositoJudicial)
            //.WhereIf(input.LevantamentoOrdemJuizo != null, x => x.LevantamentoOrdemJuizo == input.LevantamentoOrdemJuizo)
            //.WhereIf(input.DataTransitoDeferimentoCompensacao != null, x => x.DataTransitoDeferimentoCompensacao == input.DataTransitoDeferimentoCompensacao)
            //.WhereIf(input.ValorCompensacaoAtualizado != null, x => x.ValorCompensacaoAtualizado == input.ValorCompensacaoAtualizado)
            //.WhereIf(input.ValorRequisicaoPrincipal != null, x => x.ValorRequisicaoPrincipal == input.ValorRequisicaoPrincipal)
            //.WhereIf(input.ValorRequisicaoJuros != null, x => x.ValorRequisicaoJuros == input.ValorRequisicaoJuros)
            //.WhereIf(input.ValorContaPrincipal != null, x => x.ValorContaPrincipal == input.ValorContaPrincipal)
            //.WhereIf(input.ValorContaJuros != null, x => x.ValorContaJuros == input.ValorContaJuros)
            //.WhereIf(input.ValorAtualizadoRequisicaoPrincipal != null, x => x.ValorAtualizadoRequisicaoPrincipal == input.ValorAtualizadoRequisicaoPrincipal)
            //.WhereIf(input.ValorAtualizadoRequisicaoJuros != null, x => x.ValorAtualizadoRequisicaoJuros == input.ValorAtualizadoRequisicaoJuros)
            //.WhereIf(input.Selic != null, x => x.Selic == input.Selic)
            //.WhereIf(input.ValorTotalReferencia != null, x => x.ValorTotalReferencia == input.ValorTotalReferencia)
            //.WhereIf(input.IndicadorJurosMora != null, x => x.IndicadorJurosMora == input.IndicadorJurosMora)
            //.WhereIf(input.ValorAliquotaJurosMora != null, x => x.ValorAliquotaJurosMora == input.ValorAliquotaJurosMora)
            //.WhereIf(input.ValorJurosMora != null, x => x.ValorJurosMora == input.ValorJurosMora)
            //.WhereIf(input.AssuntoId > 0, x => x.Assunto.Seq_Assunt == input.AssuntoId)
            //.WhereIf(input.SituacaoRequisicaoId > 0, x => x.SituacaoRequisicaoProtocolo.SituacaoRequisicaoProtocoloId == input.SituacaoRequisicaoId)
            ;
    }
}
