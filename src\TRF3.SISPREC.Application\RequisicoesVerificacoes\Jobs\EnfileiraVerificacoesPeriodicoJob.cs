using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Quartz;
using TRF3.SISPREC.BackgroundJobs;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Settings.VerificacaoRequisicoes;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.SettingManagement;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[DisallowConcurrentExecution]
[ExposeServices(typeof(IEnfileiraVerificacoesPeriodicoJob))]
public class EnfileiraVerificacoesPeriodicoJob : SchedulingBackroundJob<EnfileiraVerificacoesPeriodicoJob>, IEnfileiraVerificacoesPeriodicoJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ISettingManager _settingManager;
    private readonly IRequisicaoVerificacaoRepository _requisicaoVerificacaoRepository;

    public override string JobName => this.GetType().Name;
    public override string JobGroupName => this.GetType().Name;
    protected override int IntervalInSeconds => 5 * 60;//Executar a cada 5 minutos

    public EnfileiraVerificacoesPeriodicoJob(
        IGetLoggerService getLoggerService,
        IScheduler scheduler,
        IUnitOfWorkManager unitOfWorkManager,
        ISettingManager settingManager,
        IRequisicaoVerificacaoRepository requisicaoVerificacaoRepository) : base(getLoggerService, scheduler)
    {
        _unitOfWorkManager = unitOfWorkManager;
        _settingManager = settingManager;
        _requisicaoVerificacaoRepository = requisicaoVerificacaoRepository;
    }

    public async override Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            Logger.LogInformation(">>>> Executando EnfileiraVerificacoesPeriodicoJob");
            using (var uow = _unitOfWorkManager.Begin(true, true))
            {
                var verificacoesNaoExecutadas = await (await _requisicaoVerificacaoRepository.GetQueryableAsync())
                                                .Include(x => x.VerificacaoTipo)
                                                .Where(x => !x.Executado)
                                                .AsNoTracking()
                                                .ToListAsync();

                ConfigurarJobs();

                string triggerName;
                IDictionary<string, object> dados;
                bool configuracaoAtiva;

                foreach (var verificacoesNaoExecutada in verificacoesNaoExecutadas)
                {
                    context.CancellationToken.ThrowIfCancellationRequested();

                    triggerName = $"RequisicaoVerificacaoId_{verificacoesNaoExecutada.RequisicaoVerificacaoId}";
                    dados = new Dictionary<string, object>() {
                        { "RequisicaoVerificacaoId", verificacoesNaoExecutada.RequisicaoVerificacaoId.ToString() },
                        { "NumeroProtocoloRequisicao", verificacoesNaoExecutada.NumeroProtocoloRequisicaoId }
                    };
                    configuracaoAtiva = IsConfiguracaoVerificacaoAtiva(verificacoesNaoExecutada.VerificacaoTipoId);

                    // Mesmo que o serviço de verificação esteja desabilitado, agenda a execução da trigger, mas pausada.
                    // Desta forma garantimos a ordem de execução das verificações quando todos os serviços estiverem ativos.
                    // Caso contrário, se um serviço A estivesse desabilitado e o B ativo, quando o A fosse habilitado,
                    // todas as triggers agendadas de A só iniciariam após a execução das triggers de B que já estavam agendadas.
                    // Na prática, como estamos tratando de requisições, é preferível que todas as verificações para uma requisição X
                    // estejam finalizadas do que ter todas as verificações do tipo Y realizadas para todas as requisições.
                    switch (EVerificacaoRequisicaoTipoHelper.Parse(verificacoesNaoExecutada.VerificacaoTipoId))
                    {
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_AJUIZAMENTO: AgendarExecucao<VerificacaoAjuizamentoJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_COMPLEMENTAR_IGUAL: AgendarExecucao<VerificacaoComplementarIgualJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_CPF_CNPJ: AgendarExecucao<VerificacaoCnpjCpfJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_INCONTROVERSO_MENOR_IGUAL: AgendarExecucao<VerificacaoIncontroversoMenorIgualJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_NOME_PARTES: AgendarExecucao<VerificacaoNomeParteJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_ORGAO_PSS: AgendarExecucao<VerificacaoOrgaoPssJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_ADVOGADO: AgendarExecucao<VerificacaoPeritoAdvogadoJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PERITO_CNPJ: AgendarExecucao<VerificacaoPeritoCnpjJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_SUCUMBENCIAL: AgendarExecucao<VerificacaoSucumbencialJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_SUPLEMENTAR_IGUAL: AgendarExecucao<VerificacaoSuplementarIgualJob>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_11:
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_12: break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_21: AgendarExecucao<VerificacaoPrevencaoTipo21Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22: AgendarExecucao<VerificacaoPrevencaoTipo22Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_23: AgendarExecucao<VerificacaoPrevencaoTipo23Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_24: AgendarExecucao<VerificacaoPrevencaoTipo24Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_31: AgendarExecucao<VerificacaoPrevencaoTipo31Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_32: AgendarExecucao<VerificacaoPrevencaoTipo32Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_34: AgendarExecucao<VerificacaoPrevencaoTipo34Job>(triggerName, dados, configuracaoAtiva); break;
                        case EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_35: AgendarExecucao<VerificacaoPrevencaoTipo35Job>(triggerName, dados, configuracaoAtiva); break;
                        default: throw new ArgumentException($"Valor inválido para VerificacaoTipoId: {verificacoesNaoExecutada.VerificacaoTipoId}");
                    }
                }
                await uow.CompleteAsync();
            }
        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "EnfileiraVerificacoesPeriodicoJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar EnfileiraVerificacoesPeriodicoJob.");
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }

    private bool IsConfiguracaoVerificacaoAtiva(int verificacaoTipoId)
    {
        EVerificacaoRequisicaoTipo verificacaoRequisicaoTipo = EVerificacaoRequisicaoTipoHelper.Parse(verificacaoTipoId);
        return _settingManager.GetGlobalBool(VerificacaoRequisicoesSettings.ObterNomeConfiguracaoPorTipoVerificacao(verificacaoRequisicaoTipo));
    }

    private void ConfigurarJobs()
    {
        AdicionarJob<VerificacaoCnpjCpfJob>();
        AdicionarJob<VerificacaoAjuizamentoJob>();
        AdicionarJob<VerificacaoComplementarIgualJob>();
        AdicionarJob<VerificacaoIncontroversoMenorIgualJob>();
        AdicionarJob<VerificacaoNomeParteJob>();
        AdicionarJob<VerificacaoOrgaoPssJob>();
        AdicionarJob<VerificacaoPeritoAdvogadoJob>();
        AdicionarJob<VerificacaoPeritoCnpjJob>();
        AdicionarJob<VerificacaoPrevencaoTipo21Job>();
        AdicionarJob<VerificacaoPrevencaoTipo22Job>();
        AdicionarJob<VerificacaoPrevencaoTipo23Job>();
        AdicionarJob<VerificacaoPrevencaoTipo24Job>();
        AdicionarJob<VerificacaoPrevencaoTipo31Job>();
        AdicionarJob<VerificacaoPrevencaoTipo32Job>();
        AdicionarJob<VerificacaoPrevencaoTipo34Job>();
        AdicionarJob<VerificacaoPrevencaoTipo35Job>();
        AdicionarJob<VerificacaoSucumbencialJob>();
        AdicionarJob<VerificacaoSuplementarIgualJob>();
    }
}