using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoAjuizamentos;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs
{
    [ExcludeFromCodeCoverage]
    public class VerificacaoAjuizamentoJob : BaseQuartzBackgroundJob, IVerificacaoAjuizamentoJob
    {
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IVerificacaoAjuizamentoManager _verificacaoAjuizamentoManager;

        public string? NumeroProtocoloRequisicao { private get; set; }

        public VerificacaoAjuizamentoJob(
            IGetLoggerService getLoggerService,
            IUnitOfWorkManager unitOfWorkManager,
            IVerificacaoAjuizamentoManager verificacaoAjuizamentoManager) : base(getLoggerService)
        {
            _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
            _verificacaoAjuizamentoManager = verificacaoAjuizamentoManager ?? throw new ArgumentNullException(nameof(verificacaoAjuizamentoManager));
        }

        public override async Task Execute(IJobExecutionContext context)
        {
            try
            {
                context.CancellationToken.ThrowIfCancellationRequested();

                if (NumeroProtocoloRequisicao.IsNullOrEmpty())
                {
                    Logger.LogError("Erro ao executar VerificacaoAjuizamentoJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                    return;
                }

                using (var uow = _unitOfWorkManager.Begin(true, true))
                {
                    await _verificacaoAjuizamentoManager.ProcessarVerificacaoAsync(NumeroProtocoloRequisicao);
                    await uow.CompleteAsync();
                }
            }
            catch (OperationCanceledException ex)
            {
                Logger.LogWarning(ex, "VerificacaoAjuizamentoJob foi interrompido.");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Erro ao executar VerificacaoAjuizamentoJob para requisição nº {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
            }
            finally
            {
                if (_unitOfWorkManager?.Current != null)
                    await _unitOfWorkManager.Current.CompleteAsync();
            }
        }
    }
}
