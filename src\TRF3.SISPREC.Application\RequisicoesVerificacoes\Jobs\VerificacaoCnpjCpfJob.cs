using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacoesCnpjCpf;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoCnpjCpfJob : BaseQuartzBackgroundJob, IVerificacaoCnpjCpfJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoCnpjCpfManager _controleCnpjCpfManager;

    public string? NumeroProtocoloRequisicao { private get; set; }
    public long RequisicaoVerificacaoId { private get; set; }

    public VerificacaoCnpjCpfJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoCnpjCpfManager controleCnpjCpfManager) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager;
        _controleCnpjCpfManager = controleCnpjCpfManager;
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoCnpjCpfJob. RequisicaoVerificacaoId inválido: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
                return;
            }
            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoCnpjCpfJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _controleCnpjCpfManager.ValidaRequisicaoControleCNPJeCPF(RequisicaoVerificacaoId, NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }

        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoCnpjCpfJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoCnpjCpfJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}