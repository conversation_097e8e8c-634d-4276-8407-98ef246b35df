using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoComplementarIgual;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoComplementarIgualJob : BaseQuartzBackgroundJob, IVerificacaoComplementarIgualJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoComplementarIgualService _verificacaoComplementarIgualService;

    public string? NumeroProtocoloRequisicao { private get; set; }
    public long RequisicaoVerificacaoId { private get; set; }

    public VerificacaoComplementarIgualJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoComplementarIgualService verificacaoComplementarIgualService) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoComplementarIgualService = verificacaoComplementarIgualService ?? throw new ArgumentNullException(nameof(verificacaoComplementarIgualService));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoComplementarIgualJob. RequisicaoVerificacaoId inválido: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
                return;
            }
            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoComplementarIgualJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoComplementarIgualService.VerificarComplementarAsync(RequisicaoVerificacaoId, NumeroProtocoloRequisicao);
            await uow.CompleteAsync();
        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoComplementarIgualJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoComplementarIgualJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
