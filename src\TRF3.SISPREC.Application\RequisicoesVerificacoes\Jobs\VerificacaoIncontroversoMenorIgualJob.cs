using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoIncontroversoMenorIgual;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoIncontroversoMenorIgualJob : BaseQuartzBackgroundJob, IVerificacaoIncontroversoMenorIgualJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoIncontroversoMenorIgualService _verificacaoIncontroversoMenorIgualService;

    public string? NumeroProtocoloRequisicao { private get; set; }
    public long RequisicaoVerificacaoId { private get; set; }

    public VerificacaoIncontroversoMenorIgualJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoIncontroversoMenorIgualService verificacaoIncontroversoMenorIgualService) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoIncontroversoMenorIgualService = verificacaoIncontroversoMenorIgualService ?? throw new ArgumentNullException(nameof(verificacaoIncontroversoMenorIgualService));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoIncontroversoMenorIgualJob. RequisicaoVerificacaoId inválido: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
                return;
            }
            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoIncontroversoMenorIgualJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoIncontroversoMenorIgualService.VerificarIncontroversoAsync(RequisicaoVerificacaoId, NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoIncontroversoMenorIgualJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoIncontroversoMenorIgualJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
