using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.DivergenciaNomePartes;
using TRF3.SISPREC.Infraestrutura;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoNomeParteJob : BaseQuartzBackgroundJob, IVerificacaoNomeParteJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IDivergenciaNomeParteManager _divergenciaNomeParteManager;

    public string? NumeroProtocoloRequisicao { private get; set; }

    public VerificacaoNomeParteJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IDivergenciaNomeParteManager divergenciaNomeParteManager) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _divergenciaNomeParteManager = divergenciaNomeParteManager ?? throw new ArgumentNullException(nameof(divergenciaNomeParteManager));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoNomeParteJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _divergenciaNomeParteManager.ProcessarVerificacaoAsync(NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }

        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoNomeParteJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoNomeParteJob para requisição nº {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
