using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoOrgaoPSS;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoOrgaoPssJob : BaseQuartzBackgroundJob, IVerificacaoOrgaoPssJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoOrgaoPssManager _verificacaoOrgaoPssManager;

    public string? NumeroProtocoloRequisicao { private get; set; }

    public VerificacaoOrgaoPssJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoOrgaoPssManager verificacaoOrgaoPssManager) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoOrgaoPssManager = verificacaoOrgaoPssManager ?? throw new ArgumentNullException(nameof(verificacaoOrgaoPssManager));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoOrgaoPssJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _verificacaoOrgaoPssManager.ProcessarVerificacaoAsync(NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }
        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoOrgaoPssJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoOrgaoPssJob para requisição nº {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
