using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoPeritosAdvogados;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoPeritoAdvogadoJob : BaseQuartzBackgroundJob, IVerificacaoPeritoAdvogadoJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoPeritoAdvogadoService _verificacaoPeritoAdvogadoService;

    public string? NumeroProtocoloRequisicao { private get; set; }
    public long RequisicaoVerificacaoId { private get; set; }

    public VerificacaoPeritoAdvogadoJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoPeritoAdvogadoService verificacaoPeritoAdvogadoService) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoPeritoAdvogadoService = verificacaoPeritoAdvogadoService ?? throw new ArgumentNullException(nameof(verificacaoPeritoAdvogadoService));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoPeritoAdvogadoJob. RequisicaoVerificacaoId inválido: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
                return;
            }
            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoPeritoAdvogadoJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoPeritoAdvogadoService.VerificarPeritoAdvogadoAsync(RequisicaoVerificacaoId, NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoPeritoAdvogadoJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoPeritoAdvogadoJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
