using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoPeritoCnpj;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoPeritoCnpjJob : BaseQuartzBackgroundJob, IVerificacaoPeritoCnpjJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoPeritoCnpjService _verificacaoPeritoCnpjService;

    public string? NumeroProtocoloRequisicao { private get; set; }
    public long RequisicaoVerificacaoId { private get; set; }

    public VerificacaoPeritoCnpjJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoPeritoCnpjService verificacaoPeritoCnpjService) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoPeritoCnpjService = verificacaoPeritoCnpjService ?? throw new ArgumentNullException(nameof(verificacaoPeritoCnpjService));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (RequisicaoVerificacaoId <= 0)
            {
                Logger.LogError("Erro ao executar VerificacaoPeritoCnpjJob. RequisicaoVerificacaoId inválido: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
                return;
            }
            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoPeritoCnpjJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using IUnitOfWork uow = _unitOfWorkManager.Begin(false, true);
            await _verificacaoPeritoCnpjService.VerificarPeritoCnpjAsync(RequisicaoVerificacaoId, NumeroProtocoloRequisicao);
            await uow.CompleteAsync();

        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoPeritoCnpjJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoPeritoCnpjJob: RequisicaoVerificacaoId: {RequisicaoVerificacaoId}.", RequisicaoVerificacaoId);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
