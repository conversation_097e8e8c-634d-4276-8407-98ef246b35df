using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoPrevencoes;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public abstract class VerificacaoPrevencaoJob : BaseQuartzBackgroundJob, IVerificacaoPrevencaoJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoPrevencaoManager _verificacaoPrevencaoManager;

    public string? NumeroProtocoloRequisicao { private get; set; }

    public string NomeClasse => GetType().Name;
    public abstract Enums.EVerificacaoRequisicaoTipo TipoPrevencao { get; }

    public VerificacaoPrevencaoJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoPrevencaoManager verificacaoPrevencaoManager) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoPrevencaoManager = verificacaoPrevencaoManager ?? throw new ArgumentNullException(nameof(verificacaoPrevencaoManager));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar {NomeClasse}. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", [NomeClasse, NumeroProtocoloRequisicao!]);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _verificacaoPrevencaoManager.ProcessarVerificacaoAsync(NumeroProtocoloRequisicao, TipoPrevencao);
                await uow.CompleteAsync();
            }
        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "{NomeClasse} foi interrompido.", NomeClasse);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar {NomeClasse} para requisição nº {NumeroProtocoloRequisicao}.", [NomeClasse, NumeroProtocoloRequisicao!]);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
