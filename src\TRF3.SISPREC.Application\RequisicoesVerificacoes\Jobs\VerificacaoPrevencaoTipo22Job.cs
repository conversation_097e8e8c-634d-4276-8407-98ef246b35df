using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoPrevencoes;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoPrevencaoTipo22Job : VerificacaoPrevencaoJob
{
    public override Enums.EVerificacaoRequisicaoTipo TipoPrevencao => Enums.EVerificacaoRequisicaoTipo.VERIFICACAO_PREVENCAO_TIPO_22;

    public VerificacaoPrevencaoTipo22Job(IGetLoggerService getLoggerService, IUnitOfWorkManager unitOfWorkManager, IVerificacaoPrevencaoManager verificacaoPrevencaoManager) : base(getLoggerService, unitOfWorkManager, verificacaoPrevencaoManager)
    {
    }
}
