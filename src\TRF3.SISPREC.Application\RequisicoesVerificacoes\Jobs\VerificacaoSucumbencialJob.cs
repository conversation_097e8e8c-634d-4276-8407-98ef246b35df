using Microsoft.Extensions.Logging;
using Quartz;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.VerificacaoSucumbencial;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.RequisicoesVerificacoes.Jobs;

[ExcludeFromCodeCoverage]
public class VerificacaoSucumbencialJob : BaseQuartzBackgroundJob, IVerificacaoSucumbencialJob
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IVerificacaoSucumbencialManager _verificacaoSucumbencialManager;

    public string? NumeroProtocoloRequisicao { private get; set; }

    public VerificacaoSucumbencialJob(
        IGetLoggerService getLoggerService,
        IUnitOfWorkManager unitOfWorkManager,
        IVerificacaoSucumbencialManager verificacaoSucumbencialManager) : base(getLoggerService)
    {
        _unitOfWorkManager = unitOfWorkManager ?? throw new ArgumentNullException(nameof(unitOfWorkManager));
        _verificacaoSucumbencialManager = verificacaoSucumbencialManager ?? throw new ArgumentNullException(nameof(verificacaoSucumbencialManager));
    }

    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            context.CancellationToken.ThrowIfCancellationRequested();

            if (NumeroProtocoloRequisicao.IsNullOrEmpty())
            {
                Logger.LogError("Erro ao executar VerificacaoSucumbencialJob. NumeroProtocoloRequisicao inválido: {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
                return;
            }

            using (var uow = _unitOfWorkManager.Begin(false, true))
            {
                await _verificacaoSucumbencialManager.ProcessarVerificacaoAsync(NumeroProtocoloRequisicao);
                await uow.CompleteAsync();
            }

        }
        catch (OperationCanceledException ex)
        {
            Logger.LogWarning(ex, "VerificacaoSucumbencialJob foi interrompido.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao executar VerificacaoSucumbencialJob para requisição nº {NumeroProtocoloRequisicao}.", NumeroProtocoloRequisicao);
        }
        finally
        {
            if (_unitOfWorkManager?.Current != null)
                await _unitOfWorkManager.Current.CompleteAsync();
        }
    }
}
