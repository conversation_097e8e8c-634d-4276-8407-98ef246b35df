using AutoMapper;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AcaoTipos.Dtos;
using TRF3.SISPREC.AcoesJustificativa;
using TRF3.SISPREC.AcoesJustificativa.Dtos;
using TRF3.SISPREC.AdvogadosJudiciais;
using TRF3.SISPREC.AdvogadosJudiciais.Dtos;
using TRF3.SISPREC.Agencias;
using TRF3.SISPREC.Agencias.Dtos;
using TRF3.SISPREC.Assuntos;
using TRF3.SISPREC.Assuntos.Dtos;
using TRF3.SISPREC.Bancos;
using TRF3.SISPREC.Bancos.Dtos;
using TRF3.SISPREC.BeneficiarioIdentificacaoTipos;
using TRF3.SISPREC.BeneficiarioIdentificacaoTipos.Dtos;
using TRF3.SISPREC.BeneficiarioSucessaoTipos;
using TRF3.SISPREC.BeneficiarioSucessaoTipos.Dtos;
using TRF3.SISPREC.BeneficiarioTipos;
using TRF3.SISPREC.BeneficiarioTipos.Dtos;
using TRF3.SISPREC.CodigosReceitaFederal;
using TRF3.SISPREC.CodigosReceitaFederal.Dtos;
using TRF3.SISPREC.ConsultaComplementares.Dtos;
using TRF3.SISPREC.Controles.Dtos;
using TRF3.SISPREC.DespesaClassificacoes;
using TRF3.SISPREC.DespesaClassificacoes.Dtos;
using TRF3.SISPREC.DespesaNaturezas;
using TRF3.SISPREC.DespesaNaturezas.Dtos;
using TRF3.SISPREC.DespesaTipos;
using TRF3.SISPREC.DespesaTipos.Dtos;
using TRF3.SISPREC.Enderecos.Dtos;
using TRF3.SISPREC.FaseTipos;
using TRF3.SISPREC.FaseTipos.Dtos;
using TRF3.SISPREC.IndiceAtualizacaoMonetarias;
using TRF3.SISPREC.IndiceAtualizacaoMonetarias.Dtos;
using TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos;
using TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.Dtos;
using TRF3.SISPREC.MdbReaders.Models;
using TRF3.SISPREC.MovimentoTipos;
using TRF3.SISPREC.MovimentoTipos.Dtos;
using TRF3.SISPREC.Municipios;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.OcorrenciaMotivos.Dtos;
using TRF3.SISPREC.OrdemPagamento107aTipos;
using TRF3.SISPREC.OrdemPagamento107aTipos.Dtos;
using TRF3.SISPREC.Pessoas;
using TRF3.SISPREC.Pessoas.Dtos;
using TRF3.SISPREC.Pessoas.Dtos.EnderecosPessoas;
using TRF3.SISPREC.Pessoas.EnderecosPessoas;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.Propostas.Dtos;
using TRF3.SISPREC.RequisicaoRequerenteCompensados;
using TRF3.SISPREC.RequisicaoRequerenteCompensados.Dtos;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesOcorrencias.Dtos;
using TRF3.SISPREC.RequisicoesPartesRequerentesIR;
using TRF3.SISPREC.RequisicoesPartesRequerentesIR.Dtos;
using TRF3.SISPREC.RequisicoesPartesRequerentesPSS;
using TRF3.SISPREC.RequisicoesPartesRequerentesPSS.Dtos;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesProcessosOrigens.Dtos;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesPropostas.Dtos;
using TRF3.SISPREC.RequisicoesPropostas.EntidadesPDF;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using TRF3.SISPREC.SentencaTipos;
using TRF3.SISPREC.SentencaTipos.Dtos;
using TRF3.SISPREC.ServidorCondicaoTipos;
using TRF3.SISPREC.ServidorCondicaoTipos.Dtos;
using TRF3.SISPREC.SincronizacaoProgressos;
using TRF3.SISPREC.SincronizacaoProgressos.Dtos;
using TRF3.SISPREC.SincronizacoesDominios;
using TRF3.SISPREC.SincronizacoesDominios.Dtos;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos.Dtos;
using TRF3.SISPREC.TiposProcedimentos;
using TRF3.SISPREC.TiposProcedimentos.Dtos;
using TRF3.SISPREC.UnidadeJudicialTipoNaturezas;
using TRF3.SISPREC.UnidadeJudicialTipoNaturezas.Dtos;
using TRF3.SISPREC.UnidadeJudicialTipos;
using TRF3.SISPREC.UnidadeJudicialTipos.Dtos;
using TRF3.SISPREC.Unidades;
using TRF3.SISPREC.Unidades.Dtos;
using TRF3.SISPREC.UnidadesGestoras;
using TRF3.SISPREC.UnidadesGestoras.Dtos;
using TRF3.SISPREC.UnidadesJudiciais;
using TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais;
using TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais.Dtos;
using TRF3.SISPREC.UnidadesJudiciais.Dtos;
using TRF3.SISPREC.ValorTipos;
using TRF3.SISPREC.ValorTipos.Dtos;
using TRF3.SISPREC.ViewAuditoriaEntidades;
using TRF3.SISPREC.ViewAuditoriaEntidades.Dtos;
using TRF3.SISPREC.ViewAuditoriaPropriedades;
using TRF3.SISPREC.ModelosDocumentos;
using TRF3.SISPREC.ModelosDocumentos.Dtos;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos;
using TRF3.SISPREC.RequisicoesPlanosOrcamentos.Dtos;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.RequisicaoEstornos.Dtos;
using TRF3.SISPREC.ViewAuditoriaPropriedades.Dtos;
using TRF3.SISPREC.IndicadorEconomicos;
using TRF3.SISPREC.IndicadorEconomicoTipos.Dtos;
using TRF3.SISPREC.IndicadorEconomicos.Dtos;
using TRF3.SISPREC.UnidadesEquivalentes;
using TRF3.SISPREC.UnidadeEquivalentes.Dtos;
using TRF3.SISPREC.IndicadorEconomicoTipos;
using TRF3.SISPREC.Peritos;
using TRF3.SISPREC.Peritos.Dtos;
using TRF3.SISPREC.UnidadesOrcamentarias.Dtos;
using TRF3.SISPREC.ControleImportacaoRequisicoes;
using TRF3.SISPREC.ControleImportacoes.Dtos;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using TRF3.SISPREC.JustificativaComparacoes;
using TRF3.SISPREC.JustificativaComparacoes.Dtos;
using TRF3.SISPREC.UnidadesOrcamentarias;
using TRF3.SISPREC.AnaliseCpfCnpj;
using TRF3.SISPREC.JustificativaDocumentos;
using TRF3.SISPREC.JustificativaDocumentos.Dtos;
using TRF3.SISPREC.AnaliseCpfCnpj.Dtos;
using TRF3.SISPREC.RequisicaoObservacoes;
using TRF3.SISPREC.ConferirObservacoes.Dtos;
using TRF3.SISPREC.AnaliseTelas;
using TRF3.SISPREC.AnaliseTelas.Dtos;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.AnaliseReinclusoes;
using TRF3.SISPREC.AnaliseReinclusoes.Dto;
using TRF3.SISPREC.AnaliseNomesPartes.Dtos;
using TRF3.SISPREC.AnaliseNomesPartes;
using TRF3.SISPREC.Setores.Dtos;
using TRF3.SISPREC.Setores;
using TRF3.SISPREC.MotivosExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.MotivosExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.BlocosSisprec.Dtos;
using TRF3.SISPREC.BlocosSisprec;
using TRF3.SISPREC.ViewProcessos;


namespace TRF3.SISPREC;


[ExcludeFromCodeCoverage]
public class SISPRECApplicationAutoMapperProfile : Profile
{
    public SISPRECApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */
        CultureInfo culturePtBr = new CultureInfo("pt-BR");
        CreateMap<IndiceAtualizacaoMonetaria, IndiceAtualizacaoMonetariaDto>();
        CreateMap<CreateUpdateIndiceAtualizacaoMonetariaDto, IndiceAtualizacaoMonetaria>(MemberList.Source);
        CreateMap<IndiceAtualizacaoMonetariaTipo, IndiceAtualizacaoMonetariaTipoDto>();
        CreateMap<SincronizacaoDominio, SincronizacaoDominioDto>();
        CreateMap<DetalheSincronizacaoDominioDto, SincronizacaoDominio>(MemberList.Source);
        CreateMap<SincronizacaoDominio, SincronizacaoDominioDto>();
        CreateMap<SincronizacaoProgresso, SincronizacaoProgressoDto>();
        CreateMap<ValorTipo, ValorTipoDto>();
        CreateMap<UnidadeJudicial, UnidadeJudicialDto>();
        CreateMap<UnidadeGestora, UnidadeGestoraDto>();
        CreateMap<UnidadeJudicialTipo, UnidadeJudicialTipoDto>();
        CreateMap<SentencaTipo, SentencaTipoDto>();
        CreateMap<MovimentoTipo, MovimentoTipoDto>();
        CreateMap<DespesaTipo, DespesaTipoDto>().ReverseMap();
        CreateMap<DespesaNatureza, DespesaNaturezaDto>().ReverseMap();
        CreateMap<BeneficiarioTipo, BeneficiarioTipoDto>();
        CreateMap<BeneficiarioIdentificacaoTipo, BeneficiarioIdentificacaoTipoDto>();
        CreateMap<BeneficiarioSucessaoTipo, BeneficiarioSucessaoTipoDto>();
        CreateMap<OrdemPagamento107aTipo, OrdemPagamento107aTipoDto>();
        CreateMap<FaseTipo, FaseTipoDto>();
        CreateMap<VerificarColunas, VerificarColunasDto>();
        CreateMap<VerificarTabelas, VerificarTabelasDto>();
        CreateMap<UnidadeJudicialTipoNatureza, UnidadeJudicialTipoNaturezaDto>();
        CreateMap<AcaoJustificativa, AcaoJustificativaDto>()
            .ForMember(dest => dest.AcaoTipo, opt => opt.MapFrom(src => src.AcaoTipo));
        CreateMap<CreateUpdateAcaoJustificativaDto, AcaoJustificativa>(MemberList.Source);
        CreateMap<ViewAuditoriaEntidade, ViewAuditoriaEntidadeDto>();
        CreateMap<ViewAuditoriaPropriedade, ViewAuditoriaPropriedadeDto>();
        CreateMap<IndiceAtualizacaoMonetariaTipo, IndiceAtualizacaoMonetariaTipoDto>();
        CreateMap<CreateUpdateIndiceAtualizacaoMonetariaTipoDto, IndiceAtualizacaoMonetariaTipo>(MemberList.Source);
        CreateMap<DespesaClassificacao, DespesaClassificacaoDto>()
            .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.ObterDescricao()));
        CreateMap<CreateUpdateDespesaClassificacaoDto, DespesaClassificacao>(MemberList.Source);
        CreateMap<Municipio, MunicipioDto>();
        CreateMap<CreateUpdateIndiceAtualizacaoMonetariaTipoDto, IndiceAtualizacaoMonetariaTipo>(MemberList.Source);
        CreateMap<IndiceAtualizacaoMonetariaDto, IndiceAtualizacaoMonetaria>();
        CreateMap<ServidorCondicaoTipo, ServidorCondicaoTipoDto>();
        CreateMap<CreateUpdateUnidadeJudicialDto, UnidadeJudicial>(MemberList.Source);
        CreateMap<ContaUnidadeJudicialDto, ContaUnidadeJudicial>(MemberList.Source).ReverseMap();
        CreateMap<UpdateContaUnidadeJudicialDto, ContaUnidadeJudicial>(MemberList.Source).ReverseMap();

        #region UnidadeOrcamentaria

        CreateMap<UnidadeOrcamentaria, UnidadeOrcamentariaDto>()
            .ForMember(dest => dest.UnidadeDto, opt => opt.MapFrom(src => src.Unidade))
            .ForMember(dest => dest.UnidadeSuperior, opt => opt.MapFrom(src => src.UnidadeSuperior));


        CreateMap<CreateUpdateUnidadeOrcamentariaDto, UnidadeOrcamentaria>(MemberList.Source)
            .ForMember(dest => dest.Unidade, opt => opt.MapFrom(src => src.UnidadeDto));

        #endregion

        #region Assunto

        CreateMap<AssuntoAuxiliar, AssuntoAuxiliarDto>().ReverseMap();
        CreateMap<AssuntoDespesa, AssuntoDespesaDto>()
            .ForMember(dest => dest.DescricaoDespesaClassificacao, opt => opt.MapFrom(src => src.DespesaClassificacao.ObterDescricao()))
            .ReverseMap();
        CreateMap<Assunto, AssuntoDto>()
            .ForMember(dest => dest.AssuntoAuxiliarDto, opt => opt.MapFrom(src => src.AssuntoAuxiliar))
            .ForMember(dest => dest.AssuntoDespesaDtos, opt => opt.MapFrom(src => src.AssuntoDespesas))
            .ReverseMap();

        CreateMap<CreateUpdateAssuntoDto, Assunto>(MemberList.Source)
            .ForMember(dest => dest.AssuntoAuxiliar, opt => opt.MapFrom(src => src.AssuntoAuxiliarDto))
            .ForMember(dest => dest.AssuntoDespesas, opt => opt.MapFrom(src => src.AssuntoDespesaDtos))
            .ReverseMap();
        #endregion

        #region UnidadeJudicial

        CreateMap<UnidadeJudicialOrigem, UnidadeJudicialOrigemDto>()
            //.ForMember(dest => dest.UnidadeJudicialDto, opt => opt.MapFrom(src => src.UnidadeJudicial))
            .ReverseMap();

        CreateMap<UnidadeJudicial, UnidadeJudicialDto>()
            .ForMember(dest => dest.UnidadeJudicialOrigem, opt => opt.MapFrom(src => src.UnidadeJudicialOrigem))
            .ReverseMap();

        CreateMap<CreateUpdateUnidadeJudicialDto, UnidadeJudicial>(MemberList.Source)
            .ForMember(dest => dest.UnidadeJudicialOrigem, opt => opt.MapFrom(src => src.UnidadeJudicialOrigem))
            .ReverseMap();

        CreateMap<CreateContaUnidadeJudicialDto, ContaUnidadeJudicial>().ReverseMap();

        #endregion

        #region Unidade 

        CreateMap<Unidade, UnidadeDto>();
        CreateMap<UnidadeDto, Unidade>();

        CreateMap<Banco, BancoDto>();
        CreateMap<CreateBancoDto, Banco>(MemberList.Source);
        CreateMap<UpdateBancoDto, Banco>(MemberList.Source);

        CreateMap<Agencia, AgenciaDto>();
        CreateMap<CreateAgenciaDto, Agencia>(MemberList.Source);
        CreateMap<UpdateAgenciaDto, Agencia>(MemberList.Source);
        #endregion

        #region entidades relacionadas à requisição

        CreateMap<RequisicaoProtocolo, RequisicaoProtocoloDto>()
            .ForMember(dest => dest.AssuntoDto, opt => opt.MapFrom(src => src.Assunto))
            .ForMember(dest => dest.SituacaoRequisicaoProtocoloDto, opt => opt.MapFrom(src => src.SituacaoRequisicaoProtocolo))
            .ForMember(dest => dest.UnidadeJudicialDto, opt => opt.MapFrom(src => src.UnidadeJudicial))
            .ReverseMap();
        CreateMap<CreateUpdateRequisicaoProtocoloDto, RequisicaoProtocolo>(MemberList.Source);
        CreateMap<Proposta, PropostaDto>();
        CreateMap<CreateUpdatePropostaDto, Proposta>(MemberList.Source);
        CreateMap<RequisicaoProposta, RequisicaoPropostaDto>(MemberList.Source).ReverseMap();
        CreateMap<DetalhesRequisicaoPrincipal, DetalhesRequisicaoPrincipalDto>(MemberList.Source).ReverseMap();
        CreateMap<DetalhesRequisicaoIrPss, DetalhesRequisicaoIrPssDto>(MemberList.Source).ReverseMap();
        CreateMap<DetalhesRequisicaoRequeridoCompensacao, DetalhesRequisicaoRequeridoCompensacaoDto>(MemberList.Source).ReverseMap();
        CreateMap<DetalhesRequisicaoRequerenteHonorarios, DetalhesRequisicaoRequerenteHonorariosDto>(MemberList.Source).ReverseMap();
        CreateMap<DetalhesRequisicaoRequerenteHonorariosListagem, DetalhesRequisicaoRequerenteHonorariosListagemDto>(MemberList.Source).ReverseMap();
        CreateMap<DetalhesRequisicaoAutorExpediente, DetalhesRequisicaoAutorExpedienteDto>(MemberList.Source).ReverseMap();
        CreateMap<CreateUpdateRequisicaoPropostaDto, RequisicaoProposta>(MemberList.Source).ReverseMap();
        CreateMap<SituacaoRequisicaoProtocolo, SituacaoRequisicaoProtocoloDto>();
        CreateMap<RequisicaoProcessoOrigem, RequisicaoProcessoOrigemDto>();
        CreateMap<CreateUpdateRequisicaoProcessoOrigemDto, RequisicaoProcessoOrigem>(MemberList.Source);
        CreateMap<CreateUpdatePessoaDto, Pessoa>(MemberList.Source);
        CreateMap<CreateUpdateEnderecoPessoaDto, EnderecoPessoa>(MemberList.Source);
        CreateMap<AdvogadoJudicial, AdvogadoJudicialDto>();
        CreateMap<CreateUpdateAdvogadoJudicialDto, AdvogadoJudicial>(MemberList.Source);
        CreateMap<TipoProcedimento, TipoProcedimentoDto>();

        CreateMap<Pessoa, PessoaDto>()
            .ForMember(dest => dest.Endereco, opt => opt.MapFrom(src => src.Endereco));

        CreateMap<EnderecoPessoaDto, MunicipioDto>()
            .ForMember(dest => dest.MunicipioId, opt => opt.MapFrom(src => src.MunicipioId));

        CreateMap<EnderecoPessoa, EnderecoPessoaDto>();
        CreateMap<EnderecoPessoaDto, EnderecoPessoa>();
        CreateMap<EnderecoPessoaDto, MunicipioDto>();
        CreateMap<MunicipioDto, Municipio>();

        #region Maps para geração do PDF dos detalhes da requisição

        CreateMap<RequisicaoPdf, RequisicaoPdfDto>().ReverseMap();
        CreateMap<MagistradoPdf, MagistradoPdfDto>().ReverseMap();
        CreateMap<RequeridoPdf, RequeridoPdfDto>().ReverseMap();
        CreateMap<OriginariosPdf, OriginariosPdfDto>().ReverseMap();
        CreateMap<ParteAutoraPdf, ParteAutoraPdfDto>().ReverseMap();
        CreateMap<RequerentePdf, RequerentePdfDto>().ReverseMap();
        CreateMap<RequerentesEcontratuaisPdf, RequerentesEcontratuaisPdfDto>().ReverseMap();
        CreateMap<AdvogadoPdf, AdvogadoPdfDto>().ReverseMap();
        CreateMap<ImpostoDeRendaPdf, ImpostoDeRendaPdfDto>().ReverseMap();
        CreateMap<ExpedientePdf, ExpedientePdfDto>().ReverseMap();
        CreateMap<ParcelasPdf, ParcelasPdfDto>().ReverseMap();
        CreateMap<OcorrenciasPdf, OcorrenciasPdfDto>().ReverseMap();
        CreateMap<RequisicaoReferenciaPdf, RequisicaoReferenciaDto>().ReverseMap();
        CreateMap<RequisicaoReinclusaoPdf, RequisicaoReinclusaoDto>().ReverseMap();
        CreateMap<ConsultaCpfPdf, ConsultaCpfDto>().ReverseMap();

        #endregion

        #endregion

        CreateMap<RequisicaoOcorrencia, RequisicaoOcorrenciaDto>()
            .ForMember(dest => dest.TipoAcao, opt => opt.MapFrom(src => src.AcaoTipo))
            .ReverseMap();

        CreateMap<RequisicaoParteRequerenteIr, RequisicaoParteRequerenteIrDto>();
        CreateMap<CreateUpdateRequisicaoParteRequerenteIrDto, RequisicaoParteRequerenteIr>(MemberList.Source);

        CreateMap<RequisicaoParteRequerentePss, RequisicaoParteRequerentePssDto>();
        CreateMap<CreateUpdateRequisicaoParteRequerentePssDto, RequisicaoParteRequerentePss>(MemberList.Source);

        CreateMap<AcaoTipo, AcaoTipoDto>()
            .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.Descricao.GetEnumDescription()))
            .ReverseMap();

        #region CodigoReceitaFederal

        CreateMap<CodigoReceitaFederal, CodigoReceitaFederalDto>();
        CreateMap<CreateUpdateCodigoReceitaFederalDto, CodigoReceitaFederal>(MemberList.Source);

        #endregion

        #region RequisicaoParteRequerenteCompensado

        CreateMap<RequisicaoRequerenteCompensado, RequisicaoParteRequerenteCompensadoDto>()
            .ForMember(dest => dest.CodigoReceitaFederal, opt => opt.MapFrom(src => src.CodigoReceitaFederal))
            .ReverseMap();

        CreateMap<CreateUpdateRequisicaoParteRequerenteCompensadoDto, RequisicaoRequerenteCompensado>(MemberList.Source);

        #endregion

        CreateMap<MotivoExpedienteAdministrativo, MotivoExpedienteAdministrativoDto>();

        CreateMap<OcorrenciaMotivo, OcorrenciaMotivoDto>().ReverseMap();
        CreateMap<CreateUpdateOcorrenciaMotivoDto, OcorrenciaMotivo>(MemberList.Source);

        CreateMap<ConsultaComplementar, ConsultaComplementaresDto>()
            .ForMember(dest => dest.DataContaLiquidacao, opt => opt.MapFrom(src => src.DataContaLiquidacao.ToString("dd/MM/yyyy")))
            .ForMember(dest => dest.DataConta, opt => opt.MapFrom(src => src.DataConta.HasValue ? src.DataConta.Value.ToString("dd/MM/yyyy") : null))
            .ForMember(dest => dest.ValorConta, opt => opt.MapFrom(src =>
                src.ValorConta.HasValue
                ? src.ValorConta.Value.ToString("N2", culturePtBr)
                : null))
            .ForMember(dest => dest.ValorRequisicao, opt => opt.MapFrom(src =>
                src.ValorRequisicao.HasValue
                ? src.ValorRequisicao.Value.ToString("N2", culturePtBr)
                : null));

        CreateMap<ModeloDocumento, ModeloDocumentoDto>();
        CreateMap<CreateUpdateModeloDocumentoDto, ModeloDocumento>(MemberList.Source);
        CreateMap<RequisicaoPlanoOrcamento, RequisicaoPlanoOrcamentoDto>();
        CreateMap<CreateUpdateRequisicaoPlanoOrcamentoDto, RequisicaoPlanoOrcamento>(MemberList.Source);
        CreateMap<CreateRequisicaoPlanoOrcamentoDto, RequisicaoPlanoOrcamento>(MemberList.Source);
        CreateMap<UpdateRequisicaoPlanoOrcamentoDto, RequisicaoPlanoOrcamento>(MemberList.Source);
        CreateMap<RequisicaoEstorno, RequisicaoEstornoDto>();
        CreateMap<CreateUpdateRequisicaoEstornoDto, RequisicaoEstorno>(MemberList.Source);
        CreateMap<IndicadorEconomico, IndicadorEconomicoDto>().ReverseMap();
        CreateMap<IndicadorEconomicoTipo, IndicadorEconomicoTipoDto>().ReverseMap();
        CreateMap<IndicadorEconomico, IndicadorEconomicoDto>().ReverseMap();

        CreateMap<UnidadeEquivalente, UnidadeEquivalenteDto>().ReverseMap();
        CreateMap<CreateUpdateUnidadeEquivalenteDto, UnidadeEquivalente>(MemberList.Source);
        CreateMap<IndicadorEconomicoTipo, IndicadorEconomicoTipoDto>();
        CreateMap<IndicadorEconomico, IndicadorEconomicoDto>();
        CreateMap<CreateUpdateIndicadorEconomicoTipoDto, IndicadorEconomicoTipo>(MemberList.Source);

        CreateMap<CreateUpdateMotivoExpedienteAdministrativoDto, MotivoExpedienteAdministrativo>();

        //CreateMap<ControleImportacaoRequisicao, ControleImportacaoRequisicaoDto>()
        //    .ForMember(dest => dest.DataErro, opt => opt.MapFrom(src => src.ControleImportacaoRequisicoesErro.DataConta.HasValue ? src.DataConta.Value.ToString("dd/MM/yyyy") : null));

        CreateMap<ControleImportacaoRequisicao, ControleImportacaoRequisicaoDto>()
           .ForMember(dest => dest.ControleImportacaoRequisicaoId, opt => opt.MapFrom(src => src.ControleImportacaoRequisicoesErro.FirstOrDefault().ControleImportacaoRequisicaoErroId))
           .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.ControleImportacaoRequisicoesErro.FirstOrDefault().Descricao))
           .ForMember(dest => dest.DataErro, opt => opt.MapFrom(src => src.ControleImportacaoRequisicoesErro.FirstOrDefault().DataErro));

        CreateMap<Perito, PeritoDto>();
        CreateMap<CreateUpdatePeritoDto, Perito>(MemberList.Source);
        CreateMap<RequisicaoJustificativa, RequisicaoJustificativaDto>();
        CreateMap<CreateUpdateRequisicaoJustificativaDto, RequisicaoJustificativa>(MemberList.Source);
        CreateMap<JustificativaComparacao, JustificativaComparacaoDto>();
        CreateMap<CreateUpdateJustificativaComparacaoDto, JustificativaComparacao>(MemberList.Source);

        CreateMap<RequisicaoAnaliseCpfCnpj, RequisicaoAnaliseCpfCnpjDto>();
        CreateMap<DadosRequisicaoCpfCnpj, DadosRequisicaoCpfCnpjDto>();

        CreateMap<RequisicaoComparadaDto, JustificativaComparacao>(MemberList.Source).ReverseMap();
        CreateMap<RequisicaoJustificativaDto, RequisicaoJustificativa>(MemberList.Source).ReverseMap();
        CreateMap<JustificativaDocumento, JustificativaDocumentoDto>();
        CreateMap<CreateUpdateJustificativaDocumentoDto, JustificativaDocumento>(MemberList.Source);


        CreateMap<ConferirObservacao, ConferirObservacaoDto>();
        CreateMap<DadosRequisicaoCpfCnpj, DadosRequisicaoCpfCnpjDto>();
        CreateMap<AnaliseTela, AnaliseTelaDto>()
            .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.Descricao.GetEnumDescription()));

        CreateMap<EstornoRequisicao, EstornoRequisicaoDto>();

        CreateMap<AnaliseNomesParte, AnaliseNomesPartePdfDto>();

        CreateMap<Setor, SetorDto>().ReverseMap();
        CreateMap<CreateUpdateSetorDto, Setor>().ReverseMap();

        #region ExpedienteAdministrativo

        CreateMap<ExpedienteAdministrativo, ExpedienteAdministrativoDto>()
            .ForMember(dest => dest.StatusExpedienteAdminstrativo, opt => opt.MapFrom(src => src.StatusExpedienteAdminstrativo.GetEnumDescription()))
            .ForMember(dest => dest.TipoExpedienteAdministrativo, opt => opt.MapFrom(src => src.TipoExpedienteAdministrativo.GetEnumDescription()))
            .ReverseMap();

        CreateMap<ExpedienteAdministrativo, ExpedienteAdministrativoSemBlocoDto>()
            .ForMember(dest => dest.StatusExpedienteAdminstrativo, opt => opt.MapFrom(src => src.StatusExpedienteAdminstrativo.GetEnumDescription()))
            .ForMember(dest => dest.TipoExpedienteAdministrativo, opt => opt.MapFrom(src => src.TipoExpedienteAdministrativo.GetEnumDescription()))
            .ReverseMap();

        CreateMap<CreateUpdateExpedienteAdminstrativoDto, ExpedienteAdministrativo>()
            .ReverseMap();

        #endregion

        CreateMap<BlocoSisprec, BlocoSisprecDto>().ReverseMap();
        CreateMap<CreateUpdateModeloBlocoSisprecDto, BlocoSisprec>().ReverseMap();
        CreateMap<BlocoSisprec, BlocoSisprecDto>()
            .ForMember(dest => dest.StatusBloco, opt => opt.MapFrom(src => src.StatusBloco.GetEnumDescription()));

        CreateMap<ExpedienteAdministrativo, ExpedienteAdministrativoDto>().ReverseMap();
    }
}
