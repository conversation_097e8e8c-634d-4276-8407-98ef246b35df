using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.SentencaTipos.Dtos;

namespace TRF3.SISPREC.SentencaTipos;

public class SentencaTipoAppService : BaseReadOnlyAppService<SentencaTipo, SentencaTipoDto, SentencaTipoKey, SentencaTipoGetListInput>, ISentencaTipoAppService
{
    private readonly ISentencaTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.SentencaTipo.Visualizar;

    public SentencaTipoAppService(ISentencaTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<SentencaTipo> GetEntityByIdAsync(SentencaTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Senten_Tipo == id.Seq_Senten_Tipo
            ));
    }

    protected override IQueryable<SentencaTipo> ApplyDefaultSorting(IQueryable<SentencaTipo> query)
    {
        return query.OrderBy(e => e.Seq_Senten_Tipo);
    }

    protected override async Task<IQueryable<SentencaTipo>> CreateFilteredQueryAsync(SentencaTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}