using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.ServidorCondicaoTipos.Dtos;

namespace TRF3.SISPREC.ServidorCondicaoTipos;

public class ServidorCondicaoTipoAppService : BaseReadOnlyAppService<ServidorCondicaoTipo, ServidorCondicaoTipoDto, int, ServidorCondicaoTipoGetListInput>, IServidorCondicaoTipoAppService
{
    private readonly IServidorCondicaoTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.ServidorCondicaoTipo.Visualizar;

    public ServidorCondicaoTipoAppService(IServidorCondicaoTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<ServidorCondicaoTipo> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Servid_Condic_Tipo == id
            ));
    }

    protected override IQueryable<ServidorCondicaoTipo> ApplyDefaultSorting(IQueryable<ServidorCondicaoTipo> query)
    {
        return query.OrderBy(e => e.Seq_Servid_Condic_Tipo);
    }

    protected override async Task<IQueryable<ServidorCondicaoTipo>> CreateFilteredQueryAsync(ServidorCondicaoTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.Seq_Servid_Condic_Tipo > 0, x => x.Seq_Servid_Condic_Tipo == input.Seq_Servid_Condic_Tipo)
            .WhereIf(input.Codigo != null, x => x.Codigo == input.Codigo)
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            .WhereIf(input.DataUtilizacaoFim != null, x => x.DataUtilizacaoFim == input.DataUtilizacaoFim)
            ;
    }
}