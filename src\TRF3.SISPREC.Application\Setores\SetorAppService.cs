using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Setores.Dtos;
using TRF3.SISPREC.Permissoes;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Setores
{
    public class SetorAppService : BaseCrudAppService<Setor,
                                                           SetorDto,
                                                           int,
                                                           SetorGetListInput,
                                                           CreateUpdateSetorDto,
                                                           CreateUpdateSetorDto>, ISetorAppService
    {
        #region Policy Permission


        /* Permissoes são automaticamente verificadas pelos respectivos métodos das classes base de AppService.
         * Importante chamar os métodos base para verificar as permissões automaticamente, 
         * ou, caso necessário não chamar o método base, colocar a anotação [Authorize(Policy = NOME_PERMISSAO)]
         */

        protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.Setor.Visualizar;
        protected override string? GravarPolicyName { get; set; } = SISPRECPermissoes.Setor.Gravar;

        #endregion

        #region Read-Only Fields

        private readonly ISetorRepository _repository;
        private readonly ISetorManager _SetorManager;

        #endregion

        #region Constructors

        public SetorAppService(ISetorRepository repository,
                                    ISetorManager manager) : base(repository, manager)
        {
            _repository = repository ?? throw new ArgumentException(null, nameof(repository));
            _SetorManager = manager ?? throw new ArgumentException(null, nameof(manager));
        }

        #endregion

        #region ISetorAppService Members

        public override async Task<SetorDto> GetAsync(int id)
        {
            var entity = await (await _repository.GetQueryableAsync())
                .Where(x => x.SetorId == id)
                .FirstOrDefaultAsync();

            if (entity == null) throw new UserFriendlyException("Setor não encontrado");

            return ObjectMapper.Map<Setor, SetorDto>(entity);
        }

        protected override IQueryable<Setor> ApplyDefaultSorting(IQueryable<Setor> query)
        {
            return query.OrderBy(e => e.Sigla);
        }

        protected override async Task<Setor> GetEntityByIdAsync(int id)
        {
            return await _repository.FindAsync(m => m.SetorId == id) ?? new Setor();
        }

        protected override async Task<IQueryable<Setor>> CreateFilteredQueryAsync(SetorGetListInput input)
        {
            return (await base.CreateFilteredQueryAsync(input))
                .WhereIf(input.SetorId != null, x => x.SetorId == input.SetorId)
                .WhereIf(!string.IsNullOrEmpty(input.Nome), x => x.Nome.Contains(input.Nome!))
                .WhereIf(!string.IsNullOrEmpty(input.Sigla), x => x.Sigla.Contains(input.Sigla!))
                .WhereIf(input.Ativo != null, x => x.Ativo == (input.Ativo!.Value == ESimNao.SIM));
        }

        public override async Task<SetorDto> CreateAsync(CreateUpdateSetorDto input)
        {
            await CheckCreatePolicyAsync();

            var existeSigla = await ValidaSigla(input.Sigla);
            if (existeSigla)
                throw new UserFriendlyException("Já existe um setor cadastrado com esta sigla!");

            var setor = ObjectMapper.Map<CreateUpdateSetorDto, Setor>(input);

            await _SetorManager.InserirAsync(setor);

            return ObjectMapper.Map<Setor, SetorDto>(setor);
        }

        public override async Task<SetorDto> UpdateAsync(int id, CreateUpdateSetorDto input)
        {
            await CheckUpdatePolicyAsync();

            var setor = await _repository.GetAsync(x => x.SetorId == id) ?? throw new UserFriendlyException("Setor não encontrado!");

            await ValidarInputAsync(input, setor.Sigla);

            setor.Nome = input.Nome;
            setor.Sigla = input.Sigla;
            setor.Ativo = input.Ativo;

            await _SetorManager.AlterarAsync(setor);
            return ObjectMapper.Map<Setor, SetorDto>(setor);
        }
        protected override Task DeleteByIdAsync(int id)
        {
            return _SetorManager.ExcluirAsync(e => e.SetorId == id);
        }

        #endregion

        private async Task<bool> ValidaSigla(string sigla)
        {
            return await _repository.AnyAsync(x => x.Sigla == sigla);
        }

        private async Task ValidarInputAsync(CreateUpdateSetorDto input, string siglaAtual)
        {
            if (!string.Equals(input.Sigla, siglaAtual, StringComparison.OrdinalIgnoreCase))
            {
                var siglaExistente = await ValidaSigla(input.Sigla);
                if (siglaExistente)
                    throw new UserFriendlyException("Já existe um setor cadastrado com esta sigla!");
            }
        }
    }
}