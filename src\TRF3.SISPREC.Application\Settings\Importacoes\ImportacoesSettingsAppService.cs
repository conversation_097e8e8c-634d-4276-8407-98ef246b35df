using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using TRF3.SISPREC.ImportacaoRequisicoes.Jobs;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.Propostas;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC.Settings.Importacoes;

[Authorize(Roles = SISPRECPermissoes.Perfil.AdminTI)]
public class ImportacoesSettingsAppService : SISPRECBaseSettingsAppService, IImportacoesSettingsAppService
{
    public ImportacoesSettingsAppService(ISettingManager settingManager, IBackgroundJobsService backgroundJobsService) : base(settingManager, backgroundJobsService)
    {
    }


    [HttpPost]
    public async Task DesabilitarImportacaoRequisicoes()
    {
        await BackgroundJobsService.PausarJobsAsync(ImportacaoRequisicoesSettings.EnfileiraImportacaoControleRequisicaoJobGroupName);
        await BackgroundJobsService.PausarJobsAsync(ImportacaoRequisicoesSettings.ImportacaoRequisicoesPeriodicoJobGroupName);
        await BackgroundJobsService.PausarJobsAsync(ImportacaoRequisicoesSettings.ImportarControleRequisicoesJobGroupName);
        await SettingManager.SetGlobalBoolAsync(ImportacaoRequisicoesSettings.ImportacaoAtiva, false);
    }

    [HttpPost]
    public async Task HabilitarImportacaoRequisicoes()
    {
        var enfileiraConsultaControlePeriodicoJob = LazyServiceProvider.GetService<IEnfileiraImportacaoControleRequisicaoPeriodicoJob>()!;
        enfileiraConsultaControlePeriodicoJob.Agendar();

        var enfileiraConsultaRequisicoesfPeriodicoJob = LazyServiceProvider.GetService<IImportacaoRequisicoesPeriodicoJob>()!;
        enfileiraConsultaRequisicoesfPeriodicoJob.Agendar();

        await BackgroundJobsService.RetomarJobsAsync(ImportacaoRequisicoesSettings.EnfileiraImportacaoControleRequisicaoJobGroupName);
        await BackgroundJobsService.RetomarJobsAsync(ImportacaoRequisicoesSettings.ImportacaoRequisicoesPeriodicoJobGroupName);
        await BackgroundJobsService.RetomarJobsAsync(ImportacaoRequisicoesSettings.ImportarControleRequisicoesJobGroupName);
        await SettingManager.SetGlobalBoolAsync(ImportacaoRequisicoesSettings.ImportacaoAtiva, true);
    }

    [HttpPost]
    public async Task HabilitarImportacaoRequisicaoManual()
    {
        await BackgroundJobsService.RetomarJobsAsync(ImportacaoRequisicoesSettings.EnfileiraImportacaoRequisicoesManualJobGroupName);
        await BackgroundJobsService.RetomarJobsAsync(ImportacaoRequisicoesSettings.ImportarRequisicoesManualJobGroupName);
    }


    [HttpPost]
    public async Task DesabilitarImportacaoRequisicaoManual()
    {
        await BackgroundJobsService.PausarJobsAsync(ImportacaoRequisicoesSettings.EnfileiraImportacaoRequisicoesManualJobGroupName);
        await BackgroundJobsService.PausarJobsAsync(ImportacaoRequisicoesSettings.ImportarRequisicoesManualJobGroupName);
    }

    [HttpPost]
    public async Task DesabilitarImportacaoPropostas()
    {
        await BackgroundJobsService.PausarJobsAsync(ImportacaoPropostaSettings.ImportarPropostaPeriodicoJobName);
        await SettingManager.SetGlobalBoolAsync(ImportacaoPropostaSettings.ImportacaoAtiva, false);
    }

    [HttpPost]
    public async Task HabilitarImportacaoPropostas()
    {
        var importarPropostaPeriodicoJob = LazyServiceProvider.GetService<IImportarPropostaPeriodicoJob>()!;
        importarPropostaPeriodicoJob.Agendar();

        await BackgroundJobsService.RetomarJobsAsync(ImportacaoPropostaSettings.ImportarPropostaPeriodicoJobName);
        await SettingManager.SetGlobalBoolAsync(ImportacaoPropostaSettings.ImportacaoAtiva, true);
    }
}
