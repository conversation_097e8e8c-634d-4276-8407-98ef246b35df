using TRF3.SISPREC.Infraestrutura;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC.Settings;

public abstract class SISPRECBaseSettingsAppService : BaseAppService
{
    protected readonly ISettingManager SettingManager;
    protected readonly IBackgroundJobsService BackgroundJobsService;

    public SISPRECBaseSettingsAppService(ISettingManager settingManager, IBackgroundJobsService backgroundJobsService)
    {
        SettingManager = settingManager;
        BackgroundJobsService = backgroundJobsService;
    }

    public bool IsConfiguracaoAtiva(string configKeyName)
    {
        return SettingManager.GetGlobalBool(configKeyName);
    }
}
