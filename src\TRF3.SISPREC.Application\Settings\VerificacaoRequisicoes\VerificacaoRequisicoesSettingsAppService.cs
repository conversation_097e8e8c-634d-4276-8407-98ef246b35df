using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Infraestrutura;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.RequisicoesVerificacoes.Jobs;
using Volo.Abp.SettingManagement;

namespace TRF3.SISPREC.Settings.VerificacaoRequisicoes;

[Authorize(Roles = SISPRECPermissoes.Perfil.AdminTI)]
public class VerificacaoRequisicoesSettingsAppService(ISettingManager settingManager, IBackgroundJobsService backgroundJobsService) : SISPRECBaseSettingsAppService(settingManager, backgroundJobsService), IVerificacaoRequisicoesSettingsAppService
{
    public async Task AlterarVerificacaoSuplementarIgualJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoSuplementarIgualJob>(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva);
        else
            await DesabilitarJob<VerificacaoSuplementarIgualJob>(VerificacaoRequisicoesSettings.VerificacaoSuplementarIgualAtiva);
    }

    public async Task AlterarVerificacaoComplementarIgualJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoComplementarIgualJob>(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva);
        else
            await DesabilitarJob<VerificacaoComplementarIgualJob>(VerificacaoRequisicoesSettings.VerificacaoComplementarIgualAtiva);
    }

    public async Task AlterarVerificacaoIncontroversoMenorIgualJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoIncontroversoMenorIgualJob>(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva);
        else
            await DesabilitarJob<VerificacaoIncontroversoMenorIgualJob>(VerificacaoRequisicoesSettings.VerificacaoIncontroversoMenorIgualAtiva);
    }

    public async Task AlterarVerificacaoCpfCnpjJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoCnpjCpfJob>(VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva);
        else
            await DesabilitarJob<VerificacaoCnpjCpfJob>(VerificacaoRequisicoesSettings.VerificacaoCpfCnpjAtiva);
    }

    public async Task AlterarVerificacaoAjuizamentoJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoAjuizamentoJob>(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva);
        else
            await DesabilitarJob<VerificacaoAjuizamentoJob>(VerificacaoRequisicoesSettings.VerificacaoAjuizamentoAtiva);
    }

    public async Task AlterarVerificacaoPeritoAdvogadoJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPeritoAdvogadoJob>(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva);
        else
            await DesabilitarJob<VerificacaoPeritoAdvogadoJob>(VerificacaoRequisicoesSettings.VerificacaoPeritoAdvogadoAtiva);
    }

    public async Task AlterarVerificacaoPrevencaoTipo21Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo21Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo21Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo21Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo22Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo22Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo22Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo22Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo23Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo23Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo23Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo23Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo24Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo24Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo24Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo24Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo32Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo32Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo32Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo32Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo35Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo35Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo35Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo35Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo35Ativa);
    }

    public async Task AlterarVerificacaoPeritoCnpjJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPeritoCnpjJob>(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva);
        else
            await DesabilitarJob<VerificacaoPeritoCnpjJob>(VerificacaoRequisicoesSettings.VerificacaoPeritoCnpjAtiva);
    }

    public async Task AlterarVerificacaoPrevencaoTipo31Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo31Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo31Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo31Ativa);
    }

    public async Task AlterarVerificacaoPrevencaoTipo34Job(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoPrevencaoTipo34Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa);
        else
            await DesabilitarJob<VerificacaoPrevencaoTipo34Job>(VerificacaoRequisicoesSettings.VerificacaoPrevencaoTipo34Ativa);
    }

    public async Task AlterarVerificacaoSucumbencial(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoSucumbencialJob>(VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva);
        else
            await DesabilitarJob<VerificacaoSucumbencialJob>(VerificacaoRequisicoesSettings.VerificacaoSucumbencialAtiva);
    }

    public async Task AlterarVerificacaoOrgaoPssJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoOrgaoPssJob>(VerificacaoRequisicoesSettings.VerificacaoOrgaoPssAtiva);
        else
            await DesabilitarJob<VerificacaoOrgaoPssJob>(VerificacaoRequisicoesSettings.VerificacaoOrgaoPssAtiva);
    }

    public async Task AlterarVerificacaoNomeParteJob(bool ativo)
    {
        if (ativo)
            await HabilitarJob<VerificacaoNomeParteJob>(VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva);
        else
            await DesabilitarJob<VerificacaoNomeParteJob>(VerificacaoRequisicoesSettings.VerificacaoNomePartesAtiva);
    }

    private async Task DesabilitarJob<T>(string nomeConfiguracao) where T : IJob
    {
        string groupName = typeof(EnfileiraVerificacoesPeriodicoJob).Name;
        string jobName = typeof(T).Name;

        // Pausa o job de verificação de requisição.
        await BackgroundJobsService.PausarJobAsync(jobName, groupName);
        await SettingManager.SetGlobalBoolAsync(nomeConfiguracao, false);

        // Verifica se deve pausar o job de enfileiramento (se todos os jobs de verificação estão desabilitados).
        if (DeveDesabilitarEnfileiraVerificacoesPeriodicoJob(nomeConfiguracao))
        {
            // Interrompe a execução atual.
            await BackgroundJobsService.InterromperJobAsync(typeof(EnfileiraVerificacoesPeriodicoJob));
            // Pausa o job para não disparar novas execuções.
            await BackgroundJobsService.PausarJobAsync(groupName, groupName);
        }
    }

    private async Task HabilitarJob<T>(string nomeConfiguracao) where T : IJob
    {
        string groupName = typeof(EnfileiraVerificacoesPeriodicoJob).Name;
        string jobName = typeof(T).Name;

        // Verifica se precisa retomar o job de enfileiramento.
        if (!await BackgroundJobsService.IsJobPeriodicoHabilitadoAsync(typeof(EnfileiraVerificacoesPeriodicoJob).Name))
        {
            var enfileiraVerificacoesPeriodicoJob = LazyServiceProvider.GetService<IEnfileiraVerificacoesPeriodicoJob>()!;
            enfileiraVerificacoesPeriodicoJob.Agendar();
            await BackgroundJobsService.RetomarJobAsync(groupName, groupName);
        }

        // Retoma o job de verificação de requisição.
        await SettingManager.SetGlobalBoolAsync(nomeConfiguracao, true);
        await BackgroundJobsService.RetomarJobAsync(jobName, groupName);
    }

    private bool DeveDesabilitarEnfileiraVerificacoesPeriodicoJob(string nomeConfiguracao)
    {
        bool retorno = true;
        string configuracao;
        EVerificacaoRequisicaoTipo[] verificacaoTipos = Enum.GetValues<EVerificacaoRequisicaoTipo>();

        foreach (var tipo in verificacaoTipos)
        {
            configuracao = VerificacaoRequisicoesSettings.ObterNomeConfiguracaoPorTipoVerificacao(tipo);
            // Se a nomeConfiguracao = configuracao, não preciso verificar se está ativa, pois é a que estou desabilitando.
            retorno = retorno && (nomeConfiguracao.Equals(configuracao) || !IsConfiguracaoAtiva(configuracao));
        }
        return retorno;
    }
}