using TRF3.SISPREC.Apoio;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.SincronizacaoProgressos.Dtos;
using TRF3.SISPREC.SincronizacoesDominios.Dtos;
using Volo.Abp;

namespace TRF3.SISPREC.SincronizacoesDominios;


public class SincronizacaoDominioAppService : BaseReadOnlyAppService<SincronizacaoDominio, SincronizacaoDominioDto, SincronizacaoDominioKey, SincronizacaoDominioGetListInput>,
    ISincronizacaoDominioAppService
{

    private readonly ISincronizacaoDominioRepository _repository;
    private readonly JobSincronizaDominioAgora _jobSincronizacao;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.TransmissaoCJF.Visualizar;

    public SincronizacaoDominioAppService(ISincronizacaoDominioRepository repository, JobSincronizaDominioAgora jobSincronizacao) : base(repository)
    {
        _repository = repository;
        _jobSincronizacao = jobSincronizacao;
    }

    protected override async Task<SincronizacaoDominio> GetEntityByIdAsync(SincronizacaoDominioKey sincronizacaoKey)
    {
        return (await _repository.WithDetailsAsync()).Where(i => i.Id == sincronizacaoKey.id).FirstOrDefault();
    }

    protected override IQueryable<SincronizacaoDominio> ApplyDefaultSorting(IQueryable<SincronizacaoDominio> query)
    {
        return query.OrderByDescending(e => e.DataInclusao);
    }

    protected override async Task<IQueryable<SincronizacaoDominio>> CreateFilteredQueryAsync(SincronizacaoDominioGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.Id != null, x => x.Id == input.Id)
            .WhereIf(input.DataInclusaoInicio != null, x => x.DataInclusao.CompareTo(input.DataInclusaoInicio) <= 0)
            .WhereIf(input.DataInclusaoFim != null, x => x.DataInclusao.CompareTo(input.DataInclusaoFim) >= 0)
            .WhereIf(input.DataConclusaoInicio != null, x => x.DataConclusao != null && ((DateTime)x.DataConclusao).CompareTo(input.DataConclusaoInicio) <= 0)
            .WhereIf(input.DataConclusaoFim != null, x => x.DataConclusao != null && ((DateTime)x.DataConclusao).CompareTo(input.DataConclusaoFim) >= 0)
            .WhereIf(!input.Mensagem.IsNullOrWhiteSpace(), x => x.Mensagem.Contains(input.Mensagem))
            .WhereIf(input.Sistema != null, x => x.Sistema == input.Sistema)
            ;
    }

    public override async Task<SincronizacaoDominioDto> GetAsync(SincronizacaoDominioKey id)
    {
        var query = await base.GetAsync(id);
        query.SincronizacoesProgressos = query.SincronizacoesProgressos
            .GroupBy(x => x.Entidade)
            .Select(x => new SincronizacaoProgressoDto()
            {
                Entidade = StringHelper.AdicionarEspacoEntreMaiuscula(x.Key),
                Alterado = x.Sum(x => x.Alterado),
                SemAlteracao = x.Sum(x => x.SemAlteracao),
                Incluido = x.Sum(x => x.Incluido),
                Excluido = x.Sum(x => x.Excluido),
                Total = x.Sum(x => x.Alterado)
                + x.Sum(x => x.SemAlteracao)
                + x.Sum(x => x.Incluido)
                + x.Sum(x => x.Excluido),
            }).ToList();

        return query;
    }

    public async Task PostDispararSincronizacaoAsync()
    {
        var sincroinizacao = _repository.BuscaUltimoIdValido();
        if (sincroinizacao != null && sincroinizacao.DataConclusao == null)
        {
            throw new UserFriendlyException("Não é possível iniciar uma nova sincronização de domínio se  existe uma sincronização não finalizada");
        }

        await _jobSincronizacao.DispararJob();
    }
}
