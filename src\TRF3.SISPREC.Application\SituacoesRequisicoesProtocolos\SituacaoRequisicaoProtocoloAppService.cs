using TRF3.SISPREC.SituacoesRequisicoesProtocolos.Dtos;

namespace TRF3.SISPREC.SituacoesRequisicoesProtocolos;
public class SituacaoRequisicaoProtocoloAppService : BaseReadOnlyAppService<SituacaoRequisicaoProtocolo, SituacaoRequisicaoProtocoloDto, int, SituacaoRequisicaoProtocoloGetListInput>,
    ISituacaoRequisicaoProtocoloAppService
{
    private readonly ISituacaoRequisicaoProtocoloRepository _repository;

    public SituacaoRequisicaoProtocoloAppService(ISituacaoRequisicaoProtocoloRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<SituacaoRequisicaoProtocolo> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.SituacaoRequisicaoProtocoloId == id
            ));
    }

    protected override IQueryable<SituacaoRequisicaoProtocolo> ApplyDefaultSorting(IQueryable<SituacaoRequisicaoProtocolo> query)
    {
        return query.OrderBy(e => e.SituacaoRequisicaoProtocoloId);
    }

    protected override async Task<IQueryable<SituacaoRequisicaoProtocolo>> CreateFilteredQueryAsync(SituacaoRequisicaoProtocoloGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.SituacaoRequisicaoProtocoloId > 0, x => x.SituacaoRequisicaoProtocoloId == input.SituacaoRequisicaoProtocoloId)
            .WhereIf(!input.DescricaoSituacao.IsNullOrWhiteSpace(), x => x.DescricaoSituacao.Contains(input.DescricaoSituacao))
            ;
    }
}
