using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.TiposProcedimentos.Dtos;

namespace TRF3.SISPREC.TiposProcedimentos;
public class TipoProcedimentoAppService : BaseReadOnlyAppService<TipoProcedimento, TipoProcedimentoDto, string, TipoProcedimentoGetListInput>,
    ITipoProcedimentoAppService
{
    private readonly ITipoProcedimentoRepository _repository;

    public TipoProcedimentoAppService(ITipoProcedimentoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<TipoProcedimento> GetEntityByIdAsync(string id)
    {
        return await _repository.GetAsync(e => e.TipoProcedimentoId == id);
    }

    protected override IQueryable<TipoProcedimento> ApplyDefaultSorting(IQueryable<TipoProcedimento> query)
    {
        return query.OrderBy(e => e.TipoProcedimentoId);
    }

    protected override async Task<IQueryable<TipoProcedimento>> CreateFilteredQueryAsync(TipoProcedimentoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.MovimentoTipo)
            .Include(x => x.BeneficiarioIdentificacaoTipo)
            .WhereIf(!input.TipoProcedimentoId.IsNullOrWhiteSpace(), x => x.TipoProcedimentoId.Contains(input.TipoProcedimentoId))
            .WhereIf(!input.DescricaoTipoProcedimento.IsNullOrWhiteSpace(), x => x.DescricaoTipoProcedimento.Contains(input.DescricaoTipoProcedimento))
            .WhereIf(input.MovimeTipoId > 0, x => x.MovimeTipoId == input.MovimeTipoId)
            .WhereIf(input.BeneficiarioIdentificacaoTipoId > 0, x => x.BeneficiarioIdentificacaoTipoId == input.BeneficiarioIdentificacaoTipoId)
            ;
    }
}
