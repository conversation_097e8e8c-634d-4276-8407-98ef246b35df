using TRF3.SISPREC.Enums;
using TRF3.SISPREC.UnidadeEquivalentes.Dtos;
using TRF3.SISPREC.UnidadesEquivalentes;

namespace TRF3.SISPREC.UnidadeEquivalentes;

public class UnidadeEquivalenteAppService : BaseAtivaDesativaAppService<UnidadeEquivalente,
                                                                          UnidadeEquivalenteDto,
                                                                          UnidadeEquivalenteKey,
                                                                          UnidadeEquivalenteGetListInput,
                                                                          CreateUpdateUnidadeEquivalenteDto,
                                                                          CreateUpdateUnidadeEquivalenteDto>, IUnidadeEquivalenteAppService
{
    private readonly IUnidadeEquivalenteRepository _unidadeEquivalenteRepository;

    public UnidadeEquivalenteAppService(IUnidadeEquivalenteRepository repository, IUnidadeEquivalenteManager manager) : base(repository, manager)
    {
        _unidadeEquivalenteRepository = repository;
    }

    protected override async Task<UnidadeEquivalente> GetEntityByIdAsync(UnidadeEquivalenteKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _unidadeEquivalenteRepository.WithDetailsAsync()).Where(e =>
                e.CodSiafi == id.CodSiafi && e.CodSiafiEquivalente == id.CodSiafiEquivalente
            ));
    }

    protected override async Task<IQueryable<UnidadeEquivalente>> CreateFilteredQueryAsync(UnidadeEquivalenteGetListInput input)
    {
        return (await _unidadeEquivalenteRepository.WithDetailsAsync())
            .WhereIf(input.CodigoSiafi != null, x => x.CodSiafi.Contains(input.CodigoSiafi) || x.CodSiafiEquivalente.Contains(input.CodigoSiafi))
            .WhereIf(input.NomeUnidade != null, x => x.NomeUnidade.Contains(input.NomeUnidade) || x.NomeUnidadeEquivalente.Contains(input.NomeUnidade))
            .WhereIf(input.Ativo != null, x => x.Ativo == (input.Ativo == ESimNao.SIM));
    }

    public override async Task<UnidadeEquivalenteDto> GetAsync(UnidadeEquivalenteKey id)
    {
        var unidadeEquivalente = await _unidadeEquivalenteRepository.ObterUnidadeEquivalente(id.CodSiafi, id.CodSiafiEquivalente);

        return MapToGetOutputDto(unidadeEquivalente);
    }

    public override async Task AtivarDesativarAsync(UnidadeEquivalenteKey id)
    {
        //Necessário obter a unidade equivalente antes para que seja possível cobrir equivalencia cruzada
        //Ex: Unidade 98000 x Unidade Equivalente 43204 e Unidade 43204 x Unidade Equivalente 98000
        var unidadeEquivalente = await _unidadeEquivalenteRepository.GetAsync(x => (
            x.CodSiafi == id.CodSiafi && x.CodSiafiEquivalente == id.CodSiafiEquivalente) || (x.CodSiafiEquivalente == id.CodSiafi && x.CodSiafi == id.CodSiafiEquivalente)
        );

        id.CodSiafi = unidadeEquivalente.CodSiafi;
        id.CodSiafiEquivalente = unidadeEquivalente.CodSiafiEquivalente;

        await base.AtivarDesativarAsync(id);
    }
}
