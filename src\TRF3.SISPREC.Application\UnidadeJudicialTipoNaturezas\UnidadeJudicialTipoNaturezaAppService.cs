using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.UnidadeJudicialTipoNaturezas.Dtos;

namespace TRF3.SISPREC.UnidadeJudicialTipoNaturezas;

public class UnidadeJudicialTipoNaturezaAppService : BaseReadOnlyAppService<UnidadeJudicialTipoNatureza, UnidadeJudicialTipoNaturezaDto, int, UnidadeJudicialTipoNaturezaGetListInput>, IUnidadeJudicialTipoNaturezaAppService
{
    private readonly IUnidadeJudicialTipoNaturezaRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.UnidadeJudicialTipoNatureza.Visualizar;

    public UnidadeJudicialTipoNaturezaAppService(IUnidadeJudicialTipoNaturezaRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<UnidadeJudicialTipoNatureza> GetEntityByIdAsync(int pk)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.UnidadeJudicialTipoNaturezaId == pk
            ));
    }

    protected override IQueryable<UnidadeJudicialTipoNatureza> ApplyDefaultSorting(IQueryable<UnidadeJudicialTipoNatureza> query)
    {
        return query.OrderBy(e => e.UnidadeJudicialTipoNaturezaId);
    }

    protected override async Task<IQueryable<UnidadeJudicialTipoNatureza>> CreateFilteredQueryAsync(UnidadeJudicialTipoNaturezaGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.UnidadeJudicialTipoNaturezaId > 0, x => x.UnidadeJudicialTipoNaturezaId == input.UnidadeJudicialTipoNaturezaId)
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Natureza.IsNullOrWhiteSpace(), x => x.Natureza.Contains(input.Natureza))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            .WhereIf(input.SeqCJF != null, x => x.SeqCJF == input.SeqCJF)
            .WhereIf(input.DataUtilizacaoFim != null, x => x.DataUtilizacaoFim == input.DataUtilizacaoFim)
            ;
    }
}