using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.UnidadeJudicialTipos.Dtos;

namespace TRF3.SISPREC.UnidadeJudicialTipos;

public class UnidadeJudicialTipoAppService : BaseReadOnlyAppService<UnidadeJudicialTipo, UnidadeJudicialTipoDto, UnidadeJudicialTipoKey, UnidadeJudicialTipoGetListInput>, IUnidadeJudicialTipoAppService
{
    private readonly IUnidadeJudicialTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.UnidadeJudicialTipo.Visualizar;

    public UnidadeJudicialTipoAppService(IUnidadeJudicialTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<UnidadeJudicialTipo> GetEntityByIdAsync(UnidadeJudicialTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Unidad_Judici_Tipo == id.Seq_Unidad_Judici_Tipo
            ));
    }

    protected override IQueryable<UnidadeJudicialTipo> ApplyDefaultSorting(IQueryable<UnidadeJudicialTipo> query)
    {
        return query.OrderBy(e => e.Seq_Unidad_Judici_Tipo);
    }

    protected override async Task<IQueryable<UnidadeJudicialTipo>> CreateFilteredQueryAsync(UnidadeJudicialTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            ;
    }
}