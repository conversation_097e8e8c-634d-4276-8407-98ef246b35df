using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.UnidadesGestoras.Dtos;

namespace TRF3.SISPREC.UnidadesGestoras;

public class UnidadeGestoraAppService : BaseReadOnlyAppService<UnidadeGestora, UnidadeGestoraDto, UnidadeGestoraKey, UnidadeGestoraGetListInput>, IUnidadeGestoraAppService
{
    private readonly IUnidadeGestoraRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.UnidadeGestora.Visualizar;

    public UnidadeGestoraAppService(IUnidadeGestoraRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<UnidadeGestora> GetEntityByIdAsync(UnidadeGestoraKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Id == id.Id
            ));
    }

    protected override IQueryable<UnidadeGestora> ApplyDefaultSorting(IQueryable<UnidadeGestora> query)
    {
        return query.OrderBy(e => e.Id);
    }

    protected override async Task<IQueryable<UnidadeGestora>> CreateFilteredQueryAsync(UnidadeGestoraGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.UG != null, x => x.UG.ToString().Contains(input.UG))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}