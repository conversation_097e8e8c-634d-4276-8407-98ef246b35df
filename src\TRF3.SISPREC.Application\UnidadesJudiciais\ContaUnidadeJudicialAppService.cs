using System.Linq.Dynamic.Core;
using TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais;
using TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.UnidadesJudiciais
{
    public class ContaUnidadeJudicialAppService : BaseCrudAppService<ContaUnidadeJudicial, ContaUnidadeJudicialDto, int, ContaUnidadeJudicialGetListInput, CreateContaUnidadeJudicialDto, UpdateContaUnidadeJudicialDto>
        , IContaUnidadeJudicialAppService
    {

        private readonly IContaUnidadeJudicialRepository _repository;

        public ContaUnidadeJudicialAppService(IContaUnidadeJudicialRepository repository, IContaUnidadeJudicialManager manager) : base(repository, manager)
        {
            _repository = repository;
        }

        protected override async Task<ContaUnidadeJudicial> GetEntityByIdAsync(int id)
        {
            return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.ContaUnidadeJudicialId == id
            ));
        }

        protected override IQueryable<ContaUnidadeJudicial> ApplyDefaultSorting(IQueryable<ContaUnidadeJudicial> query)
        {
            return query.OrderBy(e => e.ContaUnidadeJudicialId);
        }

        public async Task<ContaUnidadeJudicialDto> ObterContaUnidadeJudicial(int unidadeJudicialId)
        {
            var unidadeJudicial = await _repository.GetAsync(x => x.UnidadeJudicialId == unidadeJudicialId);

            if (unidadeJudicial == null)
                throw new EntityNotFoundException();

            return ObjectMapper.Map<ContaUnidadeJudicial, ContaUnidadeJudicialDto>(unidadeJudicial);
        }

        public async Task<PagedResultDto<ContaUnidadeJudicialDto>> GetContasUnidadeJudicialPorUnidadeJudicial(int unidadeJudicialId)
        {
            var queryable = await _repository.GetQueryableAsync();

            var contas = await AsyncExecuter.ToListAsync(queryable.Where(x => x.UnidadeJudicialId == unidadeJudicialId));

            var contasDto = ObjectMapper.Map<List<ContaUnidadeJudicial>, List<ContaUnidadeJudicialDto>>(contas);
            var totalCount = contas.Count;

            return new PagedResultDto<ContaUnidadeJudicialDto>
            {
                TotalCount = totalCount,
                Items = contasDto
            };
        }
    }
}
