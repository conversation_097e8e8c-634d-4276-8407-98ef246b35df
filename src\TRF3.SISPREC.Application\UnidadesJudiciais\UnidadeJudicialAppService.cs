using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.UnidadesJudiciais.Dtos;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.UnidadesJudiciais;

public class UnidadeJudicialAppService : BaseSincronizavelAppService<UnidadeJudicial, UnidadeJudicialDto, int, UnidadeJudicialGetListInput, CreateUpdateUnidadeJudicialDto, CreateUpdateUnidadeJudicialDto>, IUnidadeJudicialAppService
{
    private readonly IUnidadeJudicialRepository _repository;
    private readonly IUnidadeJudicialOrigemRepository _origemRepository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.UnidadeJudicial.Visualizar;
    protected override string? GravarPolicyName { get; set; } = SISPRECPermissoes.UnidadeJudicial.Gravar;

    public UnidadeJudicialAppService(IUnidadeJudicialRepository repository,
                                     IUnidadeJudicialManager manager,
                                     IUnidadeJudicialOrigemRepository origemRepository) : base(repository, manager)
    {
        _repository = repository ?? throw new ArgumentException(nameof(repository));
        _origemRepository = origemRepository ?? throw new ArgumentException(nameof(origemRepository));
    }

    public override async Task<UnidadeJudicialDto> CreateAsync(CreateUpdateUnidadeJudicialDto input)
    {
        //Alterar quando implementar entidade auxiliar
        return await base.CreateAsync(input);
    }

    public override async Task<UnidadeJudicialDto> UpdateAsync(int id, CreateUpdateUnidadeJudicialDto input)
    {
        UnidadeJudicial entidade = await GetEntityByIdAsync(id);

        if (await base.Manager.RegistroFoiSincronizadoCjf(entidade))
            return await SalvarUnidadeJudicialAuxiliarAsync(id, input.UnidadeJudicialOrigem);

        return await base.UpdateAsync(id, input);
    }

    protected override async Task<UnidadeJudicial> GetEntityByIdAsync(int id)
    {
        UnidadeJudicial? unidadeJudicial = (await _repository!.GetQueryableAsync())
                            .Include(x => x.UnidadeJudicialOrigem)
                            .Include(x => x.ContasUnidadeJudicial)
                            .Where(e => e.Seq_Unidad_Judici == id)
                            .FirstOrDefault();

        if (unidadeJudicial == null)
            throw new EntityNotFoundException();

        return unidadeJudicial;
    }

    protected override IQueryable<UnidadeJudicial> ApplyDefaultSorting(IQueryable<UnidadeJudicial> query)
    {
        return query.OrderBy(e => e.Seq_Unidad_Judici);
    }

    protected override async Task<IQueryable<UnidadeJudicial>> CreateFilteredQueryAsync(UnidadeJudicialGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.CodigoSiafi.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            .WhereIf(input.DataUtilizacaoFim != null, x => x.DataUtilizacaoFim == input.DataUtilizacaoFim)
            .WhereIf(input.Ativo != null, x => x.Ativo == input.Ativo)
            .WhereIf(input.FoiSincronizadoCjf != null, x => x.FoiSincronizadoCjf == input.FoiSincronizadoCjf)
            ;
    }

    public async Task<UnidadeJudicialDto> SalvarUnidadeJudicialAuxiliarAsync(int id, UnidadeJudicialOrigemDto input)
    {
        await CheckUpdatePolicyAsync();

        UnidadeJudicial unidadeJudicial;
        using (_repository.DisableTracking())
        {
            unidadeJudicial = await _repository.GetAsync(a => a.Seq_Unidad_Judici == id);
        }

        var unidadeJudicialOrigem = await _origemRepository.FindAsync(a => a.UnidadeJudicialOrigemId == id);

        if (unidadeJudicialOrigem is null)
        {
            unidadeJudicialOrigem = ObjectMapper.Map<UnidadeJudicialOrigemDto, UnidadeJudicialOrigem>(input);
            unidadeJudicialOrigem.UnidadeJudicialOrigemId = id;
            unidadeJudicialOrigem = await _origemRepository.InsertAsync(unidadeJudicialOrigem);
        }
        else
        {
            unidadeJudicialOrigem = ObjectMapper.Map<UnidadeJudicialOrigemDto, UnidadeJudicialOrigem>(input, unidadeJudicialOrigem);
            unidadeJudicialOrigem.UnidadeJudicialOrigemId = id;
            unidadeJudicialOrigem = await _origemRepository.UpdateAsync(unidadeJudicialOrigem);
        }

        unidadeJudicial.UnidadeJudicialOrigem = unidadeJudicialOrigem;
        return await MapToGetOutputDtoAsync(unidadeJudicial);
    }

    [RemoteService(false)]
    public override Task DeleteAsync(int id)
    {
        throw new InvalidOperationException("Não é possível excluir uma Unidade.");
    }

    public async Task<List<UnidadeJudicialDto>> GetTodasAsDescricoesUnidadesJudiciais(string descricao)
    {
        await CheckGetListPolicyAsync();

        var query = (await _repository.GetQueryableAsync())
            .WhereIf(!descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(descricao));

        var totalCount = await AsyncExecuter.CountAsync(query);

        var entities = await AsyncExecuter.ToListAsync(query.OrderBy(x => x.Descricao));

        return await MapToGetListOutputDtosAsync(entities);
    }
}