using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.UnidadesOrcamentarias.Dtos;
using Volo.Abp;
using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Unidades;

namespace TRF3.SISPREC.UnidadesOrcamentarias;

public class UnidadeOrcamentariaAppService : BaseAtivaDesativaAppService<UnidadeOrcamentaria,
                                                           UnidadeOrcamentariaDto,
                                                           int,
                                                           UnidadeOrcamentariaGetListInput,
                                                           CreateUpdateUnidadeOrcamentariaDto,
                                                           CreateUpdateUnidadeOrcamentariaDto>, IUnidadeOrcamentariaAppService
{
    #region Read-Only Fields

    private readonly IUnidadeOrcamentariaRepository _repository;
    private readonly IUnidadeOrcamentariaManager _unidademanager;

    #endregion

    #region Permissions 

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.UnidadeOrcamentaria.Visualizar;
    protected override string? GravarPolicyName { get; set; } = SISPRECPermissoes.UnidadeOrcamentaria.Gravar;

    #endregion

    #region Constructors

    public UnidadeOrcamentariaAppService(IUnidadeOrcamentariaRepository repository,
                                         IUnidadeOrcamentariaManager manager) : base(repository, manager)
    {
        _repository = repository;
        _unidademanager = manager;
    }
    #endregion

    #region IUnidadeOrcamentariaAppService Members

    public override async Task<UnidadeOrcamentariaDto> GetAsync(int id)
    {
        var entity = await (await _repository.GetQueryableAsync())
           .Include(x => x.Unidade)
           .Include(x => x.UnidadeSuperior)
           .ThenInclude(x => x.Unidade)
           .Where(x => x.UnidadeOrcamentariaId == id)
           .FirstOrDefaultAsync();

        return ObjectMapper.Map<UnidadeOrcamentaria, UnidadeOrcamentariaDto>(entity);
    }

    public override async Task<UnidadeOrcamentariaDto> CreateAsync(CreateUpdateUnidadeOrcamentariaDto input)
    {
        //Alterar quando implementar entidade auxiliar 
        var unidade = ObjectMapper.Map<CreateUpdateUnidadeOrcamentariaDto, UnidadeOrcamentaria>(input);

        await _unidademanager.InserirAsync(unidade);

        return ObjectMapper.Map<UnidadeOrcamentaria, UnidadeOrcamentariaDto>(unidade);
    }

    protected override async Task<UnidadeOrcamentaria> GetEntityByIdAsync(int id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Include(x => x.Unidade).Where(e =>
                e.UnidadeOrcamentariaId == id
            ));
    }

    protected override IQueryable<UnidadeOrcamentaria> ApplyDefaultSorting(IQueryable<UnidadeOrcamentaria> query)
    {
        return query.OrderBy(e => e.UnidadeOrcamentariaId);
    }

    protected override async Task<IQueryable<UnidadeOrcamentaria>> CreateFilteredQueryAsync(UnidadeOrcamentariaGetListInput input)
    {
        return (await _repository.GetQueryableAsync())
                .Include(x => x.Unidade)
                .WhereIf(input.CodigoSiafi != null, x => x.CodigoSiafi.Contains(input.CodigoSiafi))
                .WhereIf(input.Nome != null, x => x.Unidade.Nome.Contains(input.Nome))
                .Select(x => new UnidadeOrcamentaria()
                {
                    UnidadeOrcamentariaId = x.UnidadeOrcamentariaId,
                    Abreviatura = x.Abreviatura,
                    CodigoSiafi = x.CodigoSiafi,
                    DataUtilizacaoFim = x.DataUtilizacaoFim,
                    DataUtilizacaoInicio = x.DataUtilizacaoInicio,
                    SequencialCJF = x.SequencialCJF,
                    Ativo = x.Ativo,
                    Unidade = new Unidade()
                    {
                        Nome = x.Unidade.Nome
                    }
                })
            ;
    }

    public async Task<List<UnidadeOrcamentariaDto>> GetListarTodasUnidades()
    {
        await CheckGetListPolicyAsync();

        var query = await _repository.GetQueryableAsync();
        var totalCount = await AsyncExecuter.CountAsync(query);

        var entityDtos = new List<UnidadeOrcamentariaDto>();

        if (totalCount > 0)
            entityDtos = await MapToGetListOutputDtosAsync(await AsyncExecuter.ToListAsync(query));

        return entityDtos;
    }

    public async Task<List<UnidadeOrcamentariaDto>> GetTodosOsNomesUnidadesOrcamentarias(string nome)
    {
        await CheckGetListPolicyAsync();

        var query = (await _repository.GetQueryableAsync())
            .Include(x => x.Unidade)
            .WhereIf(!nome.IsNullOrWhiteSpace(), x => x.Unidade.Nome.Contains(nome));

        var entities = await AsyncExecuter.ToListAsync(query.OrderBy(x => x.Unidade.Nome));

        return await MapToGetListOutputDtosAsync(entities);
    }

    public async Task<List<UnidadeOrcamentariaDto>> GetTodosOsNomesUnidadesOrcamentariasSemDataUtilizacaoFim(string nome)
    {
        await CheckGetListPolicyAsync();

        var query = (await _repository.GetQueryableAsync())
            .Include(x => x.Unidade)
            .Where(x => x.DataUtilizacaoFim == null)
            .WhereIf(!nome.IsNullOrWhiteSpace(), x => x.Unidade.Nome.Contains(nome));

        var entities = await AsyncExecuter.ToListAsync(query.OrderBy(x => x.Unidade.Nome));

        return await MapToGetListOutputDtosAsync(entities);
    }

    public async Task<List<UnidadeOrcamentariaDto>> GetTodosOsCodigoSiafiUnidadesOrcamentarias(string codigoSiafi)
    {
        await CheckGetListPolicyAsync();

        var query = (await _repository.GetQueryableAsync())
            .Where(x => x.DataUtilizacaoFim == null)
            .WhereIf(!codigoSiafi.IsNullOrWhiteSpace(), x => x.CodigoSiafi.Contains(codigoSiafi));

        var entities = await AsyncExecuter.ToListAsync(query.OrderBy(x => x.CodigoSiafi));

        return await MapToGetListOutputDtosAsync(entities);
    }

    public async Task<UnidadeOrcamentariaDto> GetUnidadePeloCodigoSiafi(string codigoSiafi)
    {
        return ObjectMapper.Map<UnidadeOrcamentaria, UnidadeOrcamentariaDto>
            (await _repository.GetAsync(x => x.CodigoSiafi == codigoSiafi && x.DataUtilizacaoFim == null));
    }

    public async Task<string> GetNomeUnidadePeloCodigoSiafi(string codigoSiafi)
    {
        return await _repository.ObterNomeUnidadePeloCodigoSiafi(codigoSiafi);
    }

    [RemoteService(false)]
    public override Task DeleteAsync(int id)
    {
        throw new InvalidOperationException("Não é possível excluir uma Unidade.");
    }

    #endregion
}