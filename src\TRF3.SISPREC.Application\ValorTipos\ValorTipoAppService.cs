using TRF3.SISPREC.Permissoes;
using TRF3.SISPREC.ValorTipos.Dtos;

namespace TRF3.SISPREC.ValorTipos;

public class ValorTipoAppService : BaseReadOnlyAppService<ValorTipo, ValorTipoDto, ValorTipoKey, ValorTipoGetListInput>, IValorTipoAppService
{
    private readonly IValorTipoRepository _repository;

    protected override string? VisualizarPolicyName { get; set; } = SISPRECPermissoes.ValorTipo.Visualizar;

    public ValorTipoAppService(IValorTipoRepository repository) : base(repository)
    {
        _repository = repository;
    }

    protected override async Task<ValorTipo> GetEntityByIdAsync(ValorTipoKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.Seq_Valor_Tipo == id.Seq_Valor_Tipo
            ));
    }

    protected override IQueryable<ValorTipo> ApplyDefaultSorting(IQueryable<ValorTipo> query)
    {
        return query.OrderBy(e => e.Seq_Valor_Tipo);
    }

    protected override async Task<IQueryable<ValorTipo>> CreateFilteredQueryAsync(ValorTipoGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(!input.Codigo.IsNullOrWhiteSpace(), x => x.Codigo.Contains(input.Codigo))
            .WhereIf(!input.Descricao.IsNullOrWhiteSpace(), x => x.Descricao.Contains(input.Descricao))
            ;
    }
}