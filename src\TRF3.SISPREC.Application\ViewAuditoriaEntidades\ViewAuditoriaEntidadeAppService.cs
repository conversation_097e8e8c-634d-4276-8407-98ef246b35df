using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.ViewAuditoriaEntidades.Dtos;
using TRF3.SISPREC.ViewAuditoriaPropriedades;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.ViewAuditoriaEntidades;
public class ViewAuditoriaEntidadeAppService : BaseReadOnlyAppService<ViewAuditoriaEntidade, ViewAuditoriaEntidadeDto, Guid, ViewAuditoriaEntidadeGetListInput>, IViewAuditoriaEntidadeAppService
{

    private readonly IViewAuditoriaEntidadeRepository _repository;
    private readonly IViewAuditoriaPropriedadeRepository _auditoriaPropriedadesRepository;

    public ViewAuditoriaEntidadeAppService(IViewAuditoriaEntidadeRepository repository, IViewAuditoriaPropriedadeRepository auditoriaPropriedadesRepository) : base(repository)
    {
        _repository = repository;
        _auditoriaPropriedadesRepository = auditoriaPropriedadesRepository;
    }
    protected override IQueryable<ViewAuditoriaEntidade> ApplyDefaultSorting(IQueryable<ViewAuditoriaEntidade> query)
    {
        return query.OrderByDescending(e => e.AuditoriaEntidadeId);
    }

    protected override async Task<ViewAuditoriaEntidade> GetEntityByIdAsync(Guid id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.AuditoriaEntidadeId == id
            ));
    }

    protected override async Task<IQueryable<ViewAuditoriaEntidade>> CreateFilteredQueryAsync(ViewAuditoriaEntidadeGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input)).Include(x => x.AuditoriaPropriedades)
            .WhereIf(input.AuditoriaEntidadeId != null, x => x.AuditoriaEntidadeId == input.AuditoriaEntidadeId)
            .WhereIf(!input.TipoAlteracao.IsNullOrWhiteSpace(), x => x.TipoAlteracao.Contains(input.TipoAlteracao))
            .WhereIf(!input.ValorEntidadeId.IsNullOrWhiteSpace(), x => x.ValorEntidadeId.Contains(input.ValorEntidadeId))
            .WhereIf(!input.NomeCompletoEntidade.IsNullOrWhiteSpace(), x => x.NomeCompletoEntidade.Contains(input.NomeCompletoEntidade))
            .WhereIf(!input.AlteracaoLogPropriedadesExtras.IsNullOrWhiteSpace(), x => x.AlteracaoLogPropriedadesExtras.Contains(input.AlteracaoLogPropriedadesExtras))
            .WhereIf(!input.Usuario.IsNullOrWhiteSpace(), x => x.Usuario.Contains(input.Usuario))
            .WhereIf(input.DuracaoExecucao > 0, x => x.DuracaoExecucao == input.DuracaoExecucao)
            .WhereIf(!input.EnderecoIp.IsNullOrWhiteSpace(), x => x.EnderecoIp.Contains(input.EnderecoIp))
            .WhereIf(!input.MetodoHttp.IsNullOrWhiteSpace(), x => x.MetodoHttp.Contains(input.MetodoHttp))
            .WhereIf(input.StatusHttp != null, x => x.StatusHttp == input.StatusHttp)
            .WhereIf(!input.Url.IsNullOrWhiteSpace(), x => x.Url.Contains(input.Url))
            .WhereIf(!input.Excecoes.IsNullOrWhiteSpace(), x => x.Excecoes.Contains(input.Excecoes))
            .WhereIf(!input.AuditLogPropriedadesExtras.IsNullOrWhiteSpace(), x => x.AuditLogPropriedadesExtras.Contains(input.AuditLogPropriedadesExtras))
            .WhereIf(input.DataInicio != null && input.DataInicio != default, x => x.HorarioExecucao >= input.DataInicio)
            .WhereIf(input.DataFim != null && input.DataInicio != default, x => x.HorarioExecucao <= input.DataFim)

            ;
    }

    public async Task<IEnumerable<ViewAuditoriaEntidadeDto>> ListarPorNomeCompletoEntidadeEValorEntidadeId(string nomeCompletoEntidade, string valorEntidadeId)
    {
        var filtro = new ViewAuditoriaEntidadeGetListInput
        {
            NomeCompletoEntidade = nomeCompletoEntidade,
            ValorEntidadeId = valorEntidadeId
        };

        IQueryable<ViewAuditoriaEntidade> query = await CreateFilteredQueryAsync(filtro);

        return ObjectMapper.Map<List<ViewAuditoriaEntidade>, List<ViewAuditoriaEntidadeDto>>(query.ToList());
    }

    [HttpGet]
    public async Task<PagedResultDto<AuditoriaPropriedade>> ListarAuditoriasPropriedades(ViewAuditoriaEntidadeGetListInput filtro)
    {
        await CheckGetListPolicyAsync();

        var query = (await _auditoriaPropriedadesRepository.ListarAuditoriasPropriedades())
                            .WhereIf(!string.IsNullOrEmpty(filtro.NomeCompletoEntidade), x => x.NomeCompletoEntidade == filtro.NomeCompletoEntidade!)
                            .WhereIf(!string.IsNullOrEmpty(filtro.ValorEntidadeId), x => x.ValorEntidadeId == filtro.ValorEntidadeId!)
                            .WhereIf(filtro.DataInicio != null, x => x.HorarioExecucao.Date >= filtro.DataInicio.ToUniversalTime().Date)
                            .WhereIf(filtro.DataFim != null, x => x.HorarioExecucao.Date <= filtro.DataFim.ToUniversalTime().Date)
                            .WhereIf(!string.IsNullOrWhiteSpace(filtro.Usuario), x => x.Usuario.Contains(filtro.Usuario))
                            .OrderByIf<AuditoriaPropriedade, IQueryable<AuditoriaPropriedade>>(!string.IsNullOrWhiteSpace(filtro.Sorting), filtro.Sorting)
                            ;


        var totalCount = await AsyncExecuter.CountAsync(query);

        var registrosAuditoria = new List<AuditoriaPropriedade>();

        if (totalCount > 0)
        {
            query = query.PageBy(filtro);
            registrosAuditoria = await AsyncExecuter.ToListAsync(query);
        }

        return new PagedResultDto<AuditoriaPropriedade>(
            totalCount,
            registrosAuditoria
        );
    }
}
