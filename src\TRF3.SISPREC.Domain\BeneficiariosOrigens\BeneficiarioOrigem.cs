using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.PagoBeneficiariosOrigens;
using TRF3.SISPREC.PrecatoriosOrigens;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.BeneficiariosOrigens
{
    /// <summary>
    /// <summary>
    /// Representação de um beneficiário importado do mdb.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public abstract class BeneficiarioOrigem : Entity
    {
        #region Chave Primária Composta

        /// <summary>
        /// Id do controle de processamento.
        /// </summary>
        public int ControleProcessamentoId { get; set; }

        /// <summary>
        /// Número do processo.
        /// </summary>
        public string NumeroProcesso { get; set; } = null!;

        /// <summary>
        /// Código do beneficiário (CPF/CNPJ)
        /// </summary>
        public string CodigoBeneficiario { get; set; } = null!;

        public override object[] GetKeys()
        {
            return new object[] { ControleProcessamentoId, NumeroProcesso, CodigoBeneficiario };
        }

        #endregion

        #region Virtual

        /// <summary>
        /// Precatório origem ao qual esse beneficiário pertence.
        /// </summary>
        /// <remarks>
        /// Não é possível utilizar override devido a mudança de tipos na classe filha, obrigatória para o EF.
        /// </remarks>
        public virtual PrecatorioOrigem PrecatorioOrigem
        {
            get =>
                //Retorna desse método abstrado para que seja retornado o precatório da classe filha, pois a implementação desse método obrigatóriamente será na classe filha.
                GetPrecatorioOrigem();
            set =>
                //Atribui o valor ao precatório da classe filha.
                SetPrecatorioOrigem(value);
        }

        /// <summary>
        /// Retorna o precatório origem da classe.<br/>
        /// Deve retornar o precatório origem da classe filha, e não da pai.
        /// </summary>
        /// <returns>O precatório origem da classe filha.</returns>
        protected abstract PrecatorioOrigem GetPrecatorioOrigem();

        /// <summary>
        /// Seta o precatório origem da classe. <br/>
        /// Faz a configuração no precatório da classe filha, e não da pai.
        /// </summary>
        protected abstract void SetPrecatorioOrigem(PrecatorioOrigem precatorio);

        /// <summary>
        /// Se preenchido, indica que houve pagamento do beneficiário.
        /// </summary>
        public virtual PagoBeneficiarioOrigem? PagamentoBeneficiario
        {
            get => GetPagamentoBeneficiario();
            set => SetPagamentoBeneficiario(value);
        }

        /// <summary>
        /// Seta o pagamento na classe filha.
        /// </summary>
        /// <param name="pagoBeneficiarioOrigem">Pagamento a ser setado.</param>
        protected abstract void SetPagamentoBeneficiario(PagoBeneficiarioOrigem? pagoBeneficiarioOrigem);

        /// <summary>
        /// Retorna o pagamento da classe filha.
        /// </summary>
        /// <returns></returns>
        protected abstract PagoBeneficiarioOrigem? GetPagamentoBeneficiario();

        #endregion

        #region Propriedades de Controle

        /// <summary>
        /// Data de extração.
        /// </summary>
        public DateTime DataExtracao { get; set; }

        /// <summary>
        /// Data de importação.
        /// </summary>
        public DateTime? DataImportacao { get; set; }

        #endregion


        #region Propriedades


        #region Campos referentes aos critério de igualdade dos beneficiários (AnoProposta | CodigoUORCadastrador | UOLotacao | NumeroProcesso )
        public int AnoProposta { get; set; }
        public int CodigoUORCadastradora { get; set; }
        public int? UOLotacao { get; set; }

        #endregion

        #region Dados básicos do beneficiário (CodigoBeneficiario | NomeBeneficiario | TipoBeneficiario )

        public required string NomeBeneficiario { get; set; }
        public EBeneficiarioTipo? TipoBeneficiario { get; set; }
        #endregion

        #region Campos referentes ao dados básicos do beneficiário (documento e identificadores do beneficiário)

        public string? NomeSocialBeneficiario { get; set; }
        public bool? IndicadorPortadorDeficiencia { get; set; }
        public bool? IndicadorIsencaoIRRF { get; set; }
        public EBeneficiarioIdentificacaoTipo? IdentificadorPV { get; set; }
        public ECondicaoServidor? CondicaoServidor { get; set; }

        #endregion


        #region Campos que só serão preenchidos caso se trate de um beneficiário sucessor do processo
        public string? SucessaoTipo { get; set; }
        public DateTime? DataSucessao { get; set; }
        public string? CodigoBeneficiarioAntecessor { get; set; } = null!;
        public string? NomeBeneficiarioAntecessor { get; set; } = null;
        public decimal? ValorIndividualExpedicao { get; set; }
        public string? IndicadorOrdemPagamento107AParcela01 { get; set; }
        public string? IndicadorOrdemPagamento107AParcela02 { get; set; }
        public string? IndicadorOrdemPagamento107AParcela03 { get; set; }

        #endregion



        #region Tipo de movimento
        public ETipoMovimento? TipoMovimento { get; set; }

        #endregion

        #region Dados da conta bancária
        public int? CodigoBanco { get; set; }
        public int? CodigoAgencia { get; set; }
        public string? NumeroConta { get; set; }
        #endregion

        #region Datas de controle - usuários de inclusão e alteração
        public DateTime? DataInclusao { get; set; }
        public string? UsuarioInclusao { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public string? UsuarioAlteracao { get; set; }
        #endregion

        #region 03 - ValorPSSParcelaX (1 a 10) => PSSC - Beneficiário - PSS
        public decimal? ValorPSSParcela1 { get; set; }
        public decimal? ValorPSSParcela2 { get; set; }
        public decimal? ValorPSSParcela3 { get; set; }
        public decimal? ValorPSSParcela4 { get; set; }
        public decimal? ValorPSSParcela5 { get; set; }
        public decimal? ValorPSSParcela6 { get; set; }
        public decimal? ValorPSSParcela7 { get; set; }
        public decimal? ValorPSSParcela8 { get; set; }
        public decimal? ValorPSSParcela9 { get; set; }
        public decimal? ValorPSSParcela10 { get; set; }
        #endregion

        #region 04 - ValorCompensacaoPSSParcelaX (1 a 10) => CPSS - Beneficiário: Complemento PSS
        public decimal? ValorCompensacaoPSSParcela1 { get; set; }
        public decimal? ValorCompensacaoPSSParcela2 { get; set; }
        public decimal? ValorCompensacaoPSSParcela3 { get; set; }
        public decimal? ValorCompensacaoPSSParcela4 { get; set; }
        public decimal? ValorCompensacaoPSSParcela5 { get; set; }
        public decimal? ValorCompensacaoPSSParcela6 { get; set; }
        public decimal? ValorCompensacaoPSSParcela7 { get; set; }
        public decimal? ValorCompensacaoPSSParcela8 { get; set; }
        public decimal? ValorCompensacaoPSSParcela9 { get; set; }
        public decimal? ValorCompensacaoPSSParcela10 { get; set; }
        #endregion

        #region 08 - ValorParcelaX (1 a 10) => PCPL - Processo e Beneficiário: Valor de Parcela
        public decimal? ValorParcela1 { get; set; }
        public decimal? ValorParcela2 { get; set; }
        public decimal? ValorParcela3 { get; set; }
        public decimal? ValorParcela4 { get; set; }
        public decimal? ValorParcela5 { get; set; }
        public decimal? ValorParcela6 { get; set; }
        public decimal? ValorParcela7 { get; set; }
        public decimal? ValorParcela8 { get; set; }
        public decimal? ValorParcela9 { get; set; }
        public decimal? ValorParcela10 { get; set; }
        #endregion

        #region 09 / 10 / 11 / 12 / 13 / 14 / 15 => ATSL / VIU / VAEX / VEXA / BDIV / BINV / BPSR / 
        // 09 - ATSL - SELIC - INDEFINIDO
        // todo: definir propriedades aqui

        // 10 - VIU - Beneficiário: Valor Individual Último Cálculo
        public decimal? ValorIndividualUltimoCalculo { get; set; }

        // 12 - VEXA - Beneficiário: Parte do Valor Relativo aos Anos Anteriores
        public decimal? ValorExerciciosAnteriores { get; set; }

        // 13 - BDIV - Beneficiário: Deduções Individuais (art. 4º e 5º da IN 1127-RFB)
        public decimal? ValorDeducoesIndividuais { get; set; }

        // 14 - BINV - Beneficiário: Valor Individual do Precatório para o Beneficiário
        public decimal ValorIndividual { get; set; }

        public int? NumeroMesesExerciciosAnteriores { get; set; }

        // 15 - BPSR - Beneficiário: Valor do PSS Retido no Pagamento
        // todo: definir propriedades aqui
        #endregion

        #region 16 - ValorPrincipalTRFX (1 a 10) => BPTR - Beneficiário: Valor Principal TRF
        public decimal? ValorPrincipalTRF1 { get; set; }
        public decimal? ValorPrincipalTRF2 { get; set; }
        public decimal? ValorPrincipalTRF3 { get; set; }
        public decimal? ValorPrincipalTRF4 { get; set; }
        public decimal? ValorPrincipalTRF5 { get; set; }
        public decimal? ValorPrincipalTRF6 { get; set; }
        public decimal? ValorPrincipalTRF7 { get; set; }
        public decimal? ValorPrincipalTRF8 { get; set; }
        public decimal? ValorPrincipalTRF9 { get; set; }
        public decimal? ValorPrincipalTRF10 { get; set; }
        #endregion

        #region 17 - ValorJurosSelicX (1 a 10) => BSLC - Beneficiário: Valor Juros SELIC
        public decimal? ValorJurosSelic1 { get; set; }
        public decimal? ValorJurosSelic2 { get; set; }
        public decimal? ValorJurosSelic3 { get; set; }
        public decimal? ValorJurosSelic4 { get; set; }
        public decimal? ValorJurosSelic5 { get; set; }
        public decimal? ValorJurosSelic6 { get; set; }
        public decimal? ValorJurosSelic7 { get; set; }
        public decimal? ValorJurosSelic8 { get; set; }
        public decimal? ValorJurosSelic9 { get; set; }
        public decimal? ValorJurosSelic10 { get; set; }
        #endregion

        #region 18 / 19 - DEDU / PGT
        // 18 - DEDU - beneficiário: deduções
        // todo: definir propriedades aqui

        // 19 - PGT - beneficiário: pagamento
        // todo: definir propriedades aqui
        #endregion

        #endregion
    }

}
