using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Web.Menus;

[ExcludeFromCodeCoverage]
public static class SISPRECMenus
{
    private const string Prefix = "SISPREC";
    public const string Home = Prefix + ".Home";
    public const string CadastrosBasicos = Prefix + ".CadastrosBasicos";
    public const string TransmissaoCJF = Prefix + ".TransmissaoCJF";
    public const string Fases = TransmissaoCJF + ".Fases";
    public const string Controles = TransmissaoCJF + ".Controles";
    public const string Processamento = TransmissaoCJF + ".Processamento";
    public const string ServicosTransmissaoCJF = TransmissaoCJF + ".Servicos";
    public const string CadastrosBasicosCJF = CadastrosBasicos + ".Cadastros";
    public const string SincronizacaoCJF = TransmissaoCJF + ".Sincronizacao";

    //Entidades de domínio (Sincronização)
    public const string Assunto = CadastrosBasicosCJF + ".Assunto";
    public const string DespesaClassificacao = CadastrosBasicosCJF + ".DespesaClassificacao";
    public const string BeneficiarioTipo = CadastrosBasicosCJF + ".BeneficiarioTipo";
    public const string BeneficiarioIdentificacaoTipo = CadastrosBasicosCJF + ".BeneficiarioIdentificacaoTipo";
    public const string AntecessorBeneficiario = CadastrosBasicosCJF + ".AntecessorBeneficiario";
    public const string OrdemPagamento107aTipo = CadastrosBasicosCJF + ".OrdemPagamento107aTipo";
    public const string IndiceAtualizacaoMonetaria = CadastrosBasicosCJF + ".IndiceAtualizacaoMonetaria";
    public const string UnidadeGestora = CadastrosBasicosCJF + ".UnidadeGestora";
    public const string MovimentoTipo = CadastrosBasicosCJF + ".MovimentoTipo";
    public const string SentencaTipo = CadastrosBasicosCJF + ".SentencaTipo";
    public const string BeneficiarioSucessaoTipo = CadastrosBasicosCJF + ".BeneficiarioSucessaoTipo";
    public const string DespesaTipo = CadastrosBasicosCJF + ".DespesaTipo";
    public const string UnidadeJudicial = CadastrosBasicosCJF + ".UnidadeJudicial";
    public const string UnidadeJudicialTipo = CadastrosBasicosCJF + ".UnidadeJudicialTipo";
    public const string DespesaNatureza = CadastrosBasicosCJF + ".DespesaNatureza";
    public const string ValorTipo = CadastrosBasicosCJF + ".ValorTipo";
    public const string UnidadeOrcamentaria = CadastrosBasicosCJF + ".UnidadeOrcamentaria";
    public const string ServidorCondicaoTipo = CadastrosBasicosCJF + ".ServidorCondicaoTipo";
    public const string FaseTipo = CadastrosBasicosCJF + ".FaseTipo";

    //Demais itens
    public const string AcaoOriginaria = Prefix + ".AcaoOriginaria";
    public const string IndiceAtualizacaoMonetariaTipo = Prefix + ".IndiceAtualizacaoMonetariaTipo";
    public const string LogGeral = Prefix + ".LogGeral";
    public const string LogDetalhe = Prefix + ".LogDetalhe";
    public const string Beneficiario = Prefix + ".Beneficiario";
    public const string Parcela = Prefix + ".Parcela";
    public const string Parte = Prefix + ".Parte";
    public const string ControleProcessamentoProcesso = Prefix + ".ControleProcessamentoProcesso";
    public const string ContaBancaria = Prefix + ".ContaBancaria";
    public const string ControleProcessamentoArquivo = Prefix + ".ControleProcessamentoArquivo";
    public const string ProcessoAnterior = Prefix + ".ProcessoAnterior";
    public const string Processo = Prefix + ".Processo";
    public const string ControleProcessamento = Prefix + ".ControleProcessamento";
    public const string ContaBancariaBeneficiario = Prefix + ".ContaBancariaBeneficiario";
    public const string Plano = Prefix + ".Plano";
    public const string BeneficiarioOrigemPCT = Prefix + ".BeneficiarioOrigemPCT";
    public const string BeneficiarioOrigemRPV = Prefix + ".BeneficiarioOrigemRPV";

    public const string ViewFase = Prefix + ".ViewFase";
    public const string ViewControle = Prefix + ".ViewControle";
    public const string ViewProcesso = Prefix + ".ViewProcesso";
    public const string UnidadeJudicialTipoNatureza = Prefix + ".UnidadeJudicialTipoNatureza";
    public const string AcaoJustificativa = Prefix + ".AcaoJustificativa";
    public const string ViewAuditoriaEntidade = Prefix + ".ViewAuditoriaEntidade";
    public const string Banco = Prefix + ".Banco";
    public const string Agencia = Prefix + ".Agencia";

    //Requisitórios
    public const string Cadastros = Prefix + ".Cadastros";
    public const string Pessoas = Prefix + ".Pessoas";
    public const string Enderecos = Pessoas + ".Enderecos";
    public const string Setor = Prefix + ".Setor";
    public const string AdvogadosJudiciais = Prefix + ".AdvogadosJudiciais";
    public const string Requisicoes = Prefix + ".Requisicoes";
    public const string TiposProcedimentos = Prefix + ".TiposProcedimentos";
    public const string AnalisesRequisitorios = Prefix + ".Analises";
    public const string UnidadesEquivalentes = Prefix + ".UnidadesEquivalentes";

    //Requisições
    public const string ViewRequisicao = Requisicoes + ".Listagem";
    public const string RelatorioPorJuizo = Requisicoes + ".RelatorioPorJuizo";
    public const string RequisicoesProtocolos = Requisicoes + ".RequisicoesProtocolos";
    public const string RequisicoesPartes = Requisicoes + ".RequisicoesPartes";
    public const string PartesAutor = Requisicoes + ".PartesAutor";
    public const string PartesRequerente = Requisicoes + ".PartesRequerente";
    public const string PartesReu = Requisicoes + ".PartesReu";
    public const string SituacaoRequisicao = Requisicoes + ".SituacaoRequisicao";
    public const string ProtocolosIniciais = Requisicoes + ".ProtocolosIniciais";
    public const string RequisicaoProcessoOrigem = Requisicoes + ".RequisicaoProcessoOrigem";

    //Propostas
    public const string PropostasRequisitorios = Cadastros + ".Propostas";
    public const string Propostas = PropostasRequisitorios + ".Propostas";
    public const string RequisicoesProposta = PropostasRequisitorios + ".RequisicoesProposta";

    public const string ExpedienteAdministrativo = Prefix + ".ExpedientesAdministrativos";
    public const string ExpedienteAdministrativoListagem = ExpedienteAdministrativo + ".ExpedientesAdministrativos";
    public const string ExpedienteAdministrativoSemBloco = ExpedienteAdministrativo + ".ExpedientesAdministrativosSemBloco";
    public const string BlocosListagem = ExpedienteAdministrativo + ".Blocos";

    //Gerencial
    public const string Gerencial = Prefix + ".Gerencial";
    public const string ConfirmarLiberacao = Prefix + ".ConfirmarLiberacao";

    //Análises
    public const string Analises = Prefix + ".Analises";
    public const string AnaliseNomesPartes = Prefix + ".AnaliseNomesPartes";
    public const string AnaliseCpfCnpj = AnalisesRequisitorios + ".AnaliseCpfCnpj";
    public const string ConferirObservacao = AnalisesRequisitorios + ".ConferirObservacao";
    public const string ConferirOrgaoPss = AnalisesRequisitorios + ".ConferirOrgaoPss";
    public const string MotivoOcorrencia = Prefix + ".MotivoOcorrencia";
    public const string OcorrenciaMotivo = Prefix + ".OcorrenciaMotivo";
    public const string MotivoExpedienteAdministrativo = Prefix + ".MotivoExpedienteAdministrativo";
    public const string CnpjAlimenticio = Prefix + ".ConsultasCnpjAlimenticio";
    public const string Ajuizamento = Prefix + ".ConsultaAjuizamentos";
    public const string Complementares = Prefix + ".ConsultaComplementares";
    public const string Pendencias = Prefix + ".Pendencias";
    public const string CpfCnpj = Prefix + ".CpfCnpj";
    public const string AnalisePrevencao = Prefix + ".AnalisePrevencoes";
    public const string AnaliseReinclusao = Prefix + ".AnaliseReinclusao";
    public const string ConsultarJustificativa = Prefix + ".ConsultarJustificativa";

    //Settings (Configurações de aplicação)
    public const string Settings = Prefix + ".Settings";
    public const string ImportacoesSettings = Settings + ".Importacoes";
    public const string VerificacaoRequisicoesSettings = Settings + ".RequisicoesVerificacoes";
    public const string ModeloDocumento = Prefix + ".ModeloDocumento";
    public const string IndicadorEconomicoTipo = Prefix + ".IndicadorEconomicoTipo";
    public const string IndicadorEconomico = Prefix + ".IndicadorEconomico";
    public const string Perito = Prefix + ".Perito";
    public const string RequisicaoDocumento = Prefix + ".RequisicaoDocumento";
}
