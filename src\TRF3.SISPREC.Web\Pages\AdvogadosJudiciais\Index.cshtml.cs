using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.AdvogadosJudiciais;

public class IndexModel : SISPRECPageModel
{
    public AdvogadoJudicialFilterInput AdvogadoJudicialFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

public class AdvogadoJudicialFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "ID Advogado")]
    public int? AdvogadoJudicialId { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Nome")]
    public string? Nome { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Código OAB")]
    public string? CodigoOab { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

}
