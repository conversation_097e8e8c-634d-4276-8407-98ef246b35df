$(function () {

    $("#AdvogadoJudicialFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });

    // Adicionar listener específico para selects
    $("#AdvogadoJudicialFilter select").on('change', function () {
        dataTable.ajax.reload();
    });

    //After abp v7.2 use dynamicForm 'column-size' instead of the following settings
    //$('#AdvogadoJudicialCollapse div').addClass('col-sm-3').parent().addClass('row');

    const getFilter = function () {
        const input = {};
        $("#AdvogadoJudicialFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AdvogadoJudicialFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.advogadosJudiciais.advogadoJudicial;
    const detalheModal = new abp.ModalManager(abp.appPath + 'AdvogadosJudiciais/DetalheModal');
    const editModal = new abp.ModalManager({
        viewUrl: abp.appPath + 'AdvogadosJudiciais/EditModal',
        scriptUrl: abp.appPath + 'Pages/AdvogadosJudiciais/js/modalForm.js'
    });


    const dataTable = $('#AdvogadoJudicialTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [[0, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getList, getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",
                                visible: abp.auth.isGranted('AdvogadoJudicial.Visualizar'),
                                action: function (data) {
                                    detalheModal.open({ id: data.record.advogadoJudicialId });
                                }
                            }
                            ,
                            {
                                text: "Alterar",
                                visible: abp.auth.isGranted('AdvogadoJudicial.Gravar'),
                                action: function (data) {
                                    editModal.open({ id: data.record.advogadoJudicialId });
                                }
                            }
                        ]
                }
            },
            {
                title: "ID Advogado",
                data: "advogadoJudicialId"
            },
            {
                title: "Nome",
                data: "nome"
            },
            {
                title: "CPF",
                data: "cpf"
            },
            {
                title: "Código OAB",
                data: "codigoOab"
            },
            {
                title: "Ativo",
                data: "ativo",
                render: function (foiSincronizadoCjf, type, row, meta) {
                    if (foiSincronizadoCjf) {
                        return "Sim"
                    }

                    return "Não"
                }
            }
        ]
    }));

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

});
