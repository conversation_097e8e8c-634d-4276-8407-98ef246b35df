$(function () {

    $("#AgenciaFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });

    // Evento específico para Select2 do município
    $("#AgenciaFilter_MunicipioId").on('change', function () {
        dataTable.ajax.reload();
    });

    //After abp v7.2 use dynamicForm 'column-size' instead of the following settings
    //$('#AgenciaCollapse div').addClass('col-sm-3').parent().addClass('row');

    const getFilter = function () {
        const input = {};
        $("#AgenciaFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AgenciaFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.agencias.agencia;
    const detalheModal = new abp.ModalManager(abp.appPath + 'Agencias/DetalheModal');
    const createModal = new abp.ModalManager(abp.appPath + 'Agencias/CreateModal');
    const editModal = new abp.ModalManager(abp.appPath + 'Agencias/EditModal');

    console.log(service.getList);

    const dataTable = $('#AgenciaTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [[0, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getList, getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",
                                action: function (data) {
                                    detalheModal.open({ agenciaId: data.record.agenciaId, bancoId: data.record.banco.bancoId });
                                }
                            },
                            {
                                text: "Alterar",
                                visible: abp.auth.isGranted('Agencia.Gravar'),
                                action: function (data) {
                                    editModal.open({ agenciaId: data.record.agenciaId, bancoId: data.record.banco.bancoId });
                                }
                            },
                        ]
                }
            },
            {
                title: "Número da Agencia",
                data: "agenciaId"
            },
            {
                title: "Cod. Dígito Verificador",
                data: "codDigitoVerifi"
            },
            {
                title: "Nome da Agência",
                data: "nomeAgencia"
            },
            {
                title: "Banco",
                data: "banco.nomeBanco"
            },
            {
                title: "Municipio",
                data: "municipio.nome"
            },
            {
                title: "Ativo?",
                data: "ativo",
                render: function (ativo, type, row, meta) {
                    if (ativo) {
                        return "SIM"
                    }

                    return "NÃO"
                }
            }
        ]
    }));

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewAgenciaButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});
