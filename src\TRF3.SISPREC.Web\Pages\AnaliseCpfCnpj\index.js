$(function () {
    configuraInputParaNumerico('Ano');
    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    const obterRequisicaoEmAnalise = () => { return requisicoesDataTable.row({ selected: true }).id(); };
    const obterParteEmAnalise = () => { return partesDataTable.row({ selected: true }).id(); };

    const getFilter = function () {
        const input = {};
        $("#AnaliseCpfCnpjFilterInput")
            .serializeArray()
            .forEach(function (data) {
                if (data.value !== '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnaliseCpfCnpjFilterInput./g, ''))] = data.value;
                }
            });
        return input;
    };

    const service = tRF3.sISPREC.analiseCpfCnpj.analiseCpfCnpj;

    const requisicoesDataTable = $('#RequisicoesTable')
        .on('preXhr.dt', function () {
            abp.ui.block({ elm: 'body', busy: false });
        })
        // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
        .on('xhr.dt', function (e, settings, json, xhr) {
            abp.ui.unblock();
            $('#RequisicoesTable_processing').hide();
        })
        // Desbloqueia em caso de erro (ex: timeout).
        .on('error.dt', function () {
            abp.ui.unblock();
            // Força a ocultação do elemento de processamento do DataTables, para não ficar "travado" na tela após mensagem de erro.
            $('#RequisicoesTable_processing').hide();
        })
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroProtocoloRequisicao',
            ajax: abp.libs.datatables.createAjax(service.getRequisicoes, getFilter),
            columnDefs: [
                {
                    title: "Requisição",
                    data: "numeroProtocoloRequisicao",
                    className: "text-start"
                },
                {
                    title: "Data de Protocolo",
                    data: "dataHoraProtocoloRequisicaoFormatado",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarRequisicaoEmAnalise();

                // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="RequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="RequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    const partesDataTable = $('#PartesTable')
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'cpfCnpj',
            ajax: abp.libs.datatables.createAjax(service.getPartes, obterRequisicaoEmAnalise),
            columnDefs: [
                {
                    title: "CPF/CNPJ da parte",
                    data: "cpfCnpj",
                    className: "text-start"
                },
                {
                    title: "Nome da parte",
                    data: "nome",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarParteEmAnalise();

                // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="PartesTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="PartesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    $("#btnPesquisar").on("click", function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        // Recarrega a tabela de requisições.
        requisicoesDataTable.ajax.reload(function () {
            // Seleciona a primeira linha.
            selecionarLinhaTabela(requisicoesDataTable, 0);
        }, false);
    }

    function atualizarRequisicaoEmAnalise() {
        let requisicaoEmAnalise = obterRequisicaoEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);

            limparParteEmAnalise();

            // Recarrega a tabela de partes.
            partesDataTable.ajax.reload(function () {
                // Seleciona a primeira linha.
                selecionarLinhaTabela(partesDataTable, 0);
            }, false);
        }
        else {
            limparRequisicaoEmAnalise();
        }
    }

    function atualizarParteEmAnalise() {
        let parteEmAnalise = obterParteEmAnalise();

        if (parteEmAnalise) {
            $('#ViewModel_CpfCnpjParteAnalisada').val(parteEmAnalise);

            obterDadosRequisicao();
        }
        else {
            limparParteEmAnalise();
        }
    }

    function obterDadosRequisicao() {
        limparDadosRequisicao();

        let requisicaoEmAnalise = obterRequisicaoEmAnalise();
        let parteEmAnalise = obterParteEmAnalise();
        service.getDados(requisicaoEmAnalise, parteEmAnalise)
            .then(dados => {
                if (dados != null) {
                    //Inicialmente remove as classes dos inputs para garantir que elas não conflitem quando os proximos dados forem obtidos
                    $('#ViewModel_SituacaoCadastral').removeClass('inputVerde').removeClass('inputVermelho');

                    $('#ViewModel_NomeSistema').val(dados.nome);
                    $('#ViewModel_NomeConselho').val(dados.nomeCjf);
                    $('#ViewModel_SituacaoCadastral').val(dados.situacaoCadastral);

                    let classe = dados.situacaoCadastral == 'REGULAR' || dados.situacaoCadastral == 'ATIVA' ? 'inputVerde' : 'inputVermelho';
                    if (dados.situacaoCadastral)
                        $('#ViewModel_SituacaoCadastral').addClass(classe);

                    $('#ViewModel_NomeJuizo').val(dados.juizo);
                    $('#ViewModel_Alvara').prop('checked', dados.alvara === true);
                    $('#ViewModel_Observacao').val(dados.observacao);

                    $('#ViewModel_NomeReceita').val('');
                }
            })
            .catch(error => {
                abp.message.error('Falha para obter dados da requisição.');
                console.log(error);
            });
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.botao-navegacao-tabela[data-tabela="RequisicoesTable"]').prop('disabled', true);
        // Limpa o datatable sem disparar Ajax.
        requisicoesDataTable.clear();
        limparParteEmAnalise();
    }

    function limparParteEmAnalise() {
        $('#ViewModel_CpfCnpjParteAnalisada').val('');
        $('.botao-navegacao-tabela[data-tabela="PartesTable"]').prop('disabled', true);
        // Limpa o datatable.
        partesDataTable.clear().draw();
        limparDadosRequisicao();
    }

    function limparDadosRequisicao() {
        $('#ViewModel_NomeSistema').val('');
        $('#ViewModel_NomeConselho').val('');
        $('#ViewModel_NomeReceita').val('');
        $('#ViewModel_SituacaoCadastral').val('');
        $('#ViewModel_NomeJuizo').val('');
        $('#ViewModel_Alvara').val('');
        $('#ViewModel_Observacao').val('');
        $('.inputVerde').removeClass('inputVerde')
        $('.inputVermelho').removeClass('inputVermelho');
    }

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        console.log("AJAX Error detectado:", jqXHR.status);
        console.log("Estado do elemento de processamento:", $('#RequisicoesTable_processing').is(":visible"));

        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        // Força a ocultação do elemento de processamento do DataTables
        $('#RequisicoesTable_processing').hide();

        // Verifica se o elemento permanece visível após a tentativa de ocultá-lo
        setTimeout(() => {
            console.log("Estado do elemento de processamento após 500ms:", $('#RequisicoesTable_processing').is(":visible"));

            // Força novamente a ocultação se ainda estiver visível
            if ($('#RequisicoesTable_processing').is(":visible")) {
                $('#RequisicoesTable_processing').css('display', 'none');
            }
        }, 500);
    });

    $('#btnDetalhesDaRequisicao').on('click', function (event) {
        let numProtocRequis = obterRequisicaoEmAnalise();

        if (numProtocRequis)
            window.open("ViewRequisicoes/DetalhesRequisicao?numProtocRequis=" + numProtocRequis);
        else
            abp.notify.error("Selecione uma requisição para visualizar os detalhes.");
    });

    const button = document.getElementById('ocultarSecaoTopobtn');
    const tooltip = document.getElementById('tooltipText');

    button.addEventListener('click', () => {

        button.disabled = true;

        if (button.innerHTML.trim() === 'ᐱ' && tooltip.innerText === 'Recolher') {
            button.innerHTML = 'ᐯ';
            tooltip.innerText = 'Expandir';
        } else {
            button.style.fontSize = '10px';
            button.innerHTML = 'ᐱ';
            tooltip.innerText = 'Recolher';
        }

        setTimeout(() => {
            button.disabled = false;
        }, 300);

        partesDataTable.columns.adjust().draw();
    });

    $('#openModalButton').on('click', function () { $('#motivoModal').modal('show'); });

    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    $('#ViewModel_NomeReceita').on('input', function () {
        service.compararNomes($('#ViewModel_NomeReceita').val().trim(), $('#ViewModel_NomeSistema').val().trim())
            .then(function (result) {
                if (result) {
                    $('#ViewModel_NomeReceita, #ViewModel_NomeSistema').removeClass('inputVermelho').addClass('inputVerde');
                } else {
                    $('#ViewModel_NomeReceita, #ViewModel_NomeSistema').removeClass('inputVerde').addClass('inputVermelho');
                }
            });
    });

    $('#btnConsultarRfb').on('click', function (event) {
        let numeroRequisicao = obterRequisicaoEmAnalise();
        let cpfCnpj = obterParteEmAnalise();
        service.getDados(numeroRequisicao, cpfCnpj)
            .then(function (result) {
                if (result != null) {
                    if (cpfCnpj.length === 11) {
                        window.open("https://servicos.receita.fazenda.gov.br/servicos/cpf/consultasituacao/ConsultaPublica.asp?cpf=" + cpfCnpj + "&nascimento=" + result.dataNascimentoReceitaFormatado, "_blank");
                    }
                    else if (cpfCnpj.length === 14) {
                        window.open("https://solucoes.receita.fazenda.gov.br/Servicos/cnpjreva/cnpjreva_solicitacao.asp?cnpj=" + cpfCnpj, "_blank");
                    }
                }
            });
    });
});
