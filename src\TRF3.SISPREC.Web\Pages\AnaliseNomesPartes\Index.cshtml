@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.AnaliseNomesPartes.AnaliseNomesParte
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Análise de Nomes de Partes";
    PageLayout.Content.MenuItemName = SISPRECMenus.AnaliseNomesPartes;
}

@section scripts
{
    <abp-script src="/js/componente-utils.js" />
    <abp-script src="/Pages/AnaliseNomesPartes/index.js" />
    <abp-script src="/js/exportacao-excel.js" />
}
@section styles
{
    <abp-style src="/Pages/AnaliseNomesPartes/index.css" />
}

<abp-card>
    <abp-card-body>
        <abp-dynamic-form abp-model="AnaliseNomesParteFilter" id="AnaliseNomesParteFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="AnaliseNomesParteCollapse" show="true">
                <abp-form-content />
            </abp-collapse-body>

            <abp-column class="d-flex justify-content-start">
                <abp-button size="Small" class="mx-0" button-type="Primary" id="btnPesquisar">Pesquisar</abp-button>
                <abp-button block="true" size="Small" id="exporta-excel" class="custom-border mx-1">Exportar Excel</abp-button>
            </abp-column>
            <hr />
        </abp-dynamic-form>

        <abp-table striped-rows="true" id="AnaliseNomesParteTable" class="nowrap" />
                <abp-row class="mt-3">
             <abp-column class="text-start">
                <abp-button id="marcar-todos" text="Marcar/Desmarcar Todos" button-type="Outline_Primary" size="Medium" />
               </abp-column>    
             <abp-column class="text-end">
                <abp-button id="cadastro-justificativa" text="Cadastrar Justificativas" data-tipo-analise="7" button-type="Outline_Primary" size="Medium" />
            </abp-column>    
        </abp-row>
    </abp-card-body>
</abp-card>
