using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.AnaliseNomesPartes.AnaliseNomesParte;

public class IndexModel : SISPRECPageModel
{
    public AnaliseNomesParteFilterInput AnaliseNomesParteFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }

    public class AnaliseNomesParteFilterInput
    {
        [Display(Name = "Procedimento")]
        public ETipoProcedimentoRequisicao TipoProcedimento { get; set; }

        [Display(Name = "Ano")]
        public int AnoPropos { get; set; }

        [Display(Name = "Mês")]
        [SelectItems(nameof(Meses))]
        public int MesPropos { get; set; }

        [Display(Name = "Lida?")]
        [SelectItems(nameof(LidaLookupList))]
        public bool? Lida { get; set; }
        public List<SelectListItem> LidaLookupList { get; set; } = new List<SelectListItem>
        {
            new SelectListItem(string.Empty, null),
            new SelectListItem("Sim", "true"),
            new SelectListItem("Não", "false")
        };

        public AnaliseNomesParteFilterInput()
        {
            var date = DateTime.Now.AddMonths(1);
            AnoPropos = date.Year;
            MesPropos = date.Month;
        }
    }
}