using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.AnalisePendencias.ViewModels;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.AnalisePendencias
{
    [ExcludeFromCodeCoverage]
    public class IndexModel : SISPRECPageModel
    {
        public PendenciasViewModel? ViewModel { get; set; } = new();
        public AnaliseFilterInput? AnalisePendenciasFilterInput { get; set; }
        public async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
