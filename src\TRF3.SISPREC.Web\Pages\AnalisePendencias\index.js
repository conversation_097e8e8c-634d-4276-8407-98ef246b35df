window.listaExportacaoObservacao = new Map();

$(function () {

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    const obterPendenciaEmAnalise = () => { return pendenciasTable.row({ selected: true }).id(); };
    const obterPendenciaPorCpfEmAnalise = () => { return dataTablePorCpf.row({ selected: true }).id(); };
    const obterPendenciaPorOriginarioEmAnalise = () => { return dataTablePorOriginario.row({ selected: true }).id(); };

    const service = tRF3.sISPREC.analisePendencias.analisePendencia;
    const serviceAnalise = tRF3.sISPREC.analises.analises;

    const createModal = new abp.ModalManager(abp.appPath + 'AnalisePrevencoes/CreateModal');

    function atualizarEstadoDosBotoes(ativar) {
        $("#ListaPorCPF, #ListaPorOriginario, #ListaPorEstornadas, #btnSalvarComparada, #cadastro-justificativa"
        ).prop('disabled', !ativar);
    }


    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    $("#btnPesquisar").on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    $("#ListaPorCPF").on('click', function (event) {
        event.preventDefault();
        dataTablePorCpf.ajax.reload(function () {
            selecionarLinhaTabela(dataTablePorCpf, 0);
        }, false);

        showTable("#ListaPorCPF");
    });

    $("#ListaPorOriginario").on('click', function (event) {
        event.preventDefault();
        dataTablePorOriginario.ajax.reload(function () {
            selecionarLinhaTabela(dataTablePorOriginario, 0);
        }, false);

        showTable("#ListaPorOriginario");
    });

    const getFilter = function () {
        const input = {};

        $("#AnalisePendenciasFilterInput")
            .serializeArray()
            .forEach(function (data) {
                if (data.value !== '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnalisePendenciasFilterInput./g, ''))] = data.value;
                }
            });
        return input;
    };

    const pendenciasTable = $('#PendenciasTable')
        .on('preXrh.dt', function () {
            abp.ui.block({ elm: 'body', busy: true });
        })
        .on('xhr.dt', function (e, settings, json, xhr) {
            abp.ui.unblock();

            $('#PendenciasTable_processing').hide();
        })
        // Desbloqueia em caso de erro (ex: timeout).
        .on('error.dt', function () {
            abp.ui.unblock();
            $('#PendenciasTable_processing').hide();
        })
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroProtocoloRequisicaoPendente',
            ajax: abp.libs.datatables.createAjax(service.buscarRequisicoesAnalisesPendentes, getFilter),
            columnDefs: [
                {
                    title: "Requisição",
                    data: "numeroProtocoloRequisicaoPendente",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarRequisicaoEmAnalise();

                // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="PendenciasTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="PendenciasTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    const dataTablePorCpf = $('#ProtocolosPorCpfTable')
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroProtocoloRequisicaoPendente',
            ajax: abp.libs.datatables.createAjax(service.buscarRequisicoesParaComparacaoPorCpf, obterPendenciaEmAnalise),
            columnDefs: [
                {
                    title: "Comparar por CPF",
                    data: "numeroProtocoloRequisicaoPendente",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarRequisicaoPorCpfEmAnalise();

                // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="ProtocolosPorCpfTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="ProtocolosPorCpfTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    const dataTablePorOriginario = $('#ProtocolosPorOriginarioTable')
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroProtocoloRequisicaoPendente',
            ajax: abp.libs.datatables.createAjax(service.buscarRequisicoesParaComparacaoPorOriginario, obterPendenciaEmAnalise),
            columnDefs: [
                {
                    title: "Comparar por Originário",
                    data: "numeroProtocoloRequisicaoPendente",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarRequisicaoPorOriginarioEmAnalise();

                // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="ProtocolosPorOriginarioTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="ProtocolosPorOriginarioTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    const dataTableEstornadas = $('#ProtocolosPorEstornadasTable')
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroPorotocolo',
            data: [
                {
                    numeroPorotocolo: "12345678",
                }
            ],
            columnDefs: [
                {
                    title: "Comparar c. Estornadas",
                    data: "numeroPorotocolo"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="PartesTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="PartesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    const dataTableOcorrencias = $('#OcorrenciasTable')
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            ajax: abp.libs.datatables.createAjax(service.buscaOcorrenciasAnalise, obterPendenciaEmAnalise),
            columnDefs: [
                {
                    title: "Tipo de Ocorrência",
                    data: "descricaoAnaliseTela"
                },
                {
                    title: "Detalhe",
                    data: "descricaoMotivoOcorrencia"
                }
            ]
        }));

    function atualizarRequisicaoEmAnalise() {
        let pendenciaEmAnalise = obterPendenciaEmAnalise();

        if (pendenciaEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(pendenciaEmAnalise);

            dataTablePorCpf.ajax.reload(function () {
                selecionarLinhaTabela(dataTablePorCpf, 0);
            }, false);

            dataTableOcorrencias.ajax.reload();

            carregarDadosTabelas(false, serviceAnalise, 'compararRequisicoes', 'ProtocolosPorCpfTable', obterPendenciaEmAnalise(), 'requisicaoPrincipal', 'requisicaoPrincipalRequerido', 'requisicaoPrincipalRequerente', 'requisicaoPrincipalAssunto', 'requisicaoPrincipalAtualizacaoAtual', 'requisicaoPrincipalAtualizacaoAnterior', 'requisicaoPrincipalObservacao', 'valoresAtualizados', 'valoresAtualizadosAtual', 'valoresAtualizadosSoma');
            atualizarCombos(serviceAnalise, true, 'Principal', obterPendenciaEmAnalise());

            atualizarEstadoDosBotoes(true);

            showTable("#ListaPorCPF");
        }
        else {
            limparRequisicaoEmAnalise();
        }
    }

    function atualizarRequisicaoPorCpfEmAnalise() {
        let pendenciaPorCpfEmAnalise = obterPendenciaPorCpfEmAnalise();

        if (pendenciaPorCpfEmAnalise) {
            $('#ViewModel_NumeroRequisicaoComparada').val(pendenciaPorCpfEmAnalise);

            carregarDadosTabelas(false, serviceAnalise, 'compararRequisicoes', 'ProtocolosPorCpfTable', pendenciaPorCpfEmAnalise, 'requisicaoComparada', 'requisicaoComparadaRequerido', 'requisicaoComparadaRequerente', 'requisicaoComparadaAssunto', 'requisicaoComparadaObservacao', 'valoresAtualizadosAnterior');
            atualizarCombos(serviceAnalise, true, 'Comparado', pendenciaPorCpfEmAnalise)
        }
        else {
            limparPendenciaPorCpfEmAnalise();
        }
    }

    function atualizarRequisicaoPorOriginarioEmAnalise() {
        let pendenciaPorOriginarioEmAnalise = obterPendenciaPorOriginarioEmAnalise();

        if (pendenciaPorOriginarioEmAnalise) {
            $('#ViewModel_NumeroRequisicaoComparada').val(pendenciaPorOriginarioEmAnalise);

            carregarDadosTabelas(false, serviceAnalise, 'compararRequisicoes', 'ProtocolosPorOriginarioTable', pendenciaPorOriginarioEmAnalise, 'requisicaoComparada', 'requisicaoComparadaRequerido', 'requisicaoComparadaRequerente', 'requisicaoComparadaAssunto', 'requisicaoComparadaObservacao', 'valoresAtualizadosAnterior');
            atualizarCombos(serviceAnalise, true, 'Comparado', pendenciaPorOriginarioEmAnalise)
        }
        else {
            limparPendenciaPorOriginarioEmAnalise();
        }
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.botao-navegacao-tabela[data-tabela="PendenciasTable"]').prop('disabled', true);
        // Limpa o datatable sem disparar Ajax.
        pendenciasTable.clear();

        limparPendenciaPorCpfEmAnalise();
        limparPendenciaPorOriginarioEmAnalise();
        dataTableOcorrencias.clear().draw();
        atualizarEstadoDosBotoes(false);
    }

    function limparPendenciaPorCpfEmAnalise() {
        $('#ViewModel_NumeroRequisicaoComparada').val('');
        $('.botao-navegacao-tabela[data-tabela="ProtocolosPorCpfTable"]').prop('disabled', true);
        // Limpa o datatable
        dataTablePorCpf.clear().draw();
        limparDadosComparacao();
    }

    function limparPendenciaPorOriginarioEmAnalise() {
        $('#ViewModel_NumeroRequisicaoComparada').val('');
        $('.botao-navegacao-tabela[data-tabela="ProtocolosPorOriginarioTable"]').prop('disabled', true);
        // Limpa o datatable
        dataTablePorOriginario.clear().draw();
        limparDadosComparacao();
    }

    const button = document.getElementById('ocultarSecaoTopobtn');
    const tooltip = document.getElementById('tooltipText');

    button.addEventListener('click', () => {

        button.disabled = true;

        if (button.innerHTML.trim() === 'ᐱ' && tooltip.innerText === 'Recolher') {
            button.innerHTML = 'ᐯ';
            tooltip.innerText = 'Expandir';
        } else {
            button.style.fontSize = '10px';
            button.innerHTML = 'ᐱ';
            tooltip.innerText = 'Recolher';
        }

        setTimeout(() => {
            button.disabled = false;
        }, 300);
    });

    $(document).ready(function () {
        $('#openModalButton').on('click', function () {
            $('#motivoModal').modal('show');
        });
    });

    const tables = {
        "#ListaPorCPF": "#containerProtocolosPorCpf",
        "#ListaPorOriginario": "#containerProtocolosPorOriginario",
        "#ListaPorEstornadas": "#containerProtocolosPorEstornadas"
    };

    function hideAllTables() {
        Object.values(tables).forEach(containerId => {
            $(containerId).hide();
        });
    }

    function showTable(buttonId) {
        hideAllTables();
        $(tables[buttonId]).show();
    }

    const showTableOnLoad = function () {
        showTable("#ListaPorCPF");
    };

    atualizarEstadoDosBotoes(false);
    showTableOnLoad();

    $('#btnSalvarComparada').click(function (e) {
        e.preventDefault();
        let numeroRequisicao = $('#ViewModel_NumeroRequisicaoComparada').val().trim();
        let observacaoEspelho = listaExportacaoObservacao.get(numeroRequisicao);
        if (numeroRequisicao) {

            createModal.open();
            const intervalId = setTimeout(() => {
                let modal = document.querySelector(".modal"); // Ajuste para a classe/campo do modal
                let inputElement = modal?.querySelector(".custom-textarea"); // Busca dentro do modal

                if (inputElement) {
                    inputElement.value = observacaoEspelho ?? '';
                    inputElement.dispatchEvent(new Event("input")); // Se for um componente reativo
                    clearInterval(intervalId);
                }
            }, 300);
        }
    });

    createModal.onResult(function () {
        let numeroRequisicao = $('#ViewModel_NumeroRequisicaoComparada').val().trim();
        let observacaoEspelho = $('#ViewModel_ObservacaoGeracaoEspelho').val();
        if (observacaoEspelho !== '') {
            listaExportacaoObservacao.set(numeroRequisicao, observacaoEspelho);
        }
    });

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        pendenciasTable.ajax.reload(function () {
            selecionarLinhaTabela(pendenciasTable, 0);
        }, false);
    }
});
