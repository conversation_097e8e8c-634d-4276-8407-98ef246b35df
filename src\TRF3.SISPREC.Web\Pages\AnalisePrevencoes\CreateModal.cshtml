@page
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model TRF3.SISPREC.Web.Pages.AnalisePrevencoes.CreateModalModel
@{
    Layout = null;
}

<form abp-model="ViewModel" data-ajaxForm="true" data-check-form-on-close="false" asp-page="CreateModal">
    <abp-modal>
            <abp-modal-header title="Inserir observação para geração do espelho"></abp-modal-header>
        <abp-modal-body>
            <abp-row>
                <abp-input class="custom-textarea" size="Large" label=" " rows="4" asp-for="@Model.ViewModel.ObservacaoGeracaoEspelho" />
                <abp-input asp-for="@Model.Id" disabled="true" />
                </abp-row> 
            </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </abp-modal>
</form>