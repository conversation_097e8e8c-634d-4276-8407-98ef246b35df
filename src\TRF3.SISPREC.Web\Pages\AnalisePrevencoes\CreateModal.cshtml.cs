using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.AnalisePrevencoes.ViewModels;

namespace TRF3.SISPREC.Web.Pages.AnalisePrevencoes;

[ExcludeFromCodeCoverage]
public class CreateModalModel : SISPRECPageModel
{
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    [BindProperty]
    public CreateObservacaoGeracaoEspelhoViewModel ViewModel { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }

}