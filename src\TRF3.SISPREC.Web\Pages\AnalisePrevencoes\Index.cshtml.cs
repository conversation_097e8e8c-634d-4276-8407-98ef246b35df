using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.AnalisePrevencoes.ViewModels;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.AnalisePrevencoes
{
    [ExcludeFromCodeCoverage]
    public class IndexModel : SISPRECPageModel
    {
        public AnalisePrevencoesViewModel? ViewModel { get; set; } = new();

        [BindProperty]
        public CreateObservacaoGeracaoEspelhoViewModel ViewModelObservacao { get; set; } = new();
        public AnaliseFilterInput? AnalisePrevencoesFilterInput { get; set; }

        public async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
