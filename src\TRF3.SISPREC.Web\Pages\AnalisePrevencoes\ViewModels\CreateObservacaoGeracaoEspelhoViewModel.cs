using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.AnalisePrevencoes.ViewModels;

[ExcludeFromCodeCoverage]
public class CreateObservacaoGeracaoEspelhoViewModel
{
    [Display(Name = "Observação para geração do espelho")]
    [TextArea(Rows = 4)]
    [FormControlSize(AbpFormControlSize.Large)]
    public string? ObservacaoGeracaoEspelho { get; set; }
}
