window.listaExportacaoObservacao = new Map();

$(function () {

    const serviceAnalise = tRF3.sISPREC.analises.analises;

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    const obterRequisicaoEmAnalise = () => { return requisicoesDataTable.row({ selected: true }).id() || '00000'; };
    const obterPrevencaoEmAnalise = () => {
        const rowData = dataTablePrevencoes.row({ selected: true }).data();
        return rowData;
    };

    const createModal = new abp.ModalManager(abp.appPath + 'AnalisePrevencoes/CreateModal');

    $("#btnPesquisar").on('click', function (event) {
        event.preventDefault();

        novaPesquisa();
    });

    const getFilter = function () {
        const input = {};
        $("#AnalisePrevencoesFilterInput")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnalisePrevencoesFilterInput./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.analisePrevencoes.analisePrevencoes;

    const requisicoesDataTable = $('#RequisicoesTable')
        .on('preXhr.dt', function () {
            abp.ui.block({ elm: 'body', busy: false });
        })
        // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
        .on('xhr.dt', function (e, settings, json, xhr) {
            abp.ui.unblock();
            $('#RequisicoesTable_processing').hide();
        })
        // Desbloqueia em caso de erro (ex: timeout).
        .on('error.dt', function () {
            abp.ui.unblock();
            // Força a ocultação do elemento de processamento do DataTables, para não ficar "travado" na tela após mensagem de erro.
            $('#RequisicoesTable_processing').hide();
        })
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroProtocoloRequisicao',
            ajax: abp.libs.datatables.createAjax(service.getRequisicoes, getFilter),
            columnDefs: [
                {
                    title: "Requisição",
                    data: "numeroProtocoloRequisicao",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarRequisicaoEmAnalise();

                // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="RequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="RequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        console.log("AJAX Error detectado:", jqXHR.status);
        console.log("Estado do elemento de processamento:", $('#RequisicoesTable_processing').is(":visible"));

        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        // Força a ocultação do elemento de processamento do DataTables
        $('#RequisicoesTable_processing').hide();

        // Verifica se o elemento permanece visível após a tentativa de ocultá-lo
        setTimeout(() => {
            console.log("Estado do elemento de processamento após 500ms:", $('#RequisicoesTable_processing').is(":visible"));

            // Força novamente a ocultação se ainda estiver visível
            if ($('#RequisicoesTable_processing').is(":visible")) {
                $('#RequisicoesTable_processing').css('display', 'none');
            }
        }, 500);
    });

    const dataTablePrevencoes = $('#PrevencoesTable')
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'requisicaoAnterior',
            ajax: abp.libs.datatables.createAjax(service.getPrevencoes, obterRequisicaoEmAnalise),
            columnDefs: [
                { title: "Requisição Anterior", data: "requisicaoAnterior", className: "text-start" },
                { title: "Originário", data: "numeroProcessoOriginario", className: "text-start" },
                { title: "Detalhe", data: "descricao", className: "text-start" }
            ]
        }))
        .on('select', async function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarPrevencaoEmAnalise();

                // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="PrevencoesTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="PrevencoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
            }
        });


    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    createModal.onResult(function () {
        let numeroRequisicao = $('#ViewModel_Prevencao').val().split(" - ")[0].trim();
        let observacaoEspelho = $('#ViewModel_ObservacaoGeracaoEspelho').val();
        if (observacaoEspelho !== '') {
            window.listaExportacaoObservacao.set(numeroRequisicao, observacaoEspelho);
        }
    });

    $('#btnSalvarEspelho').click(function (e) {
        e.preventDefault();
        let numeroRequisicao = $('#ViewModel_Prevencao').val().split(" - ")[0].trim();
        let observacaoEspelho = window.listaExportacaoObservacao.get(numeroRequisicao);
        if (numeroRequisicao) {

            createModal.open();
            setTimeout(() => {
                let modal = document.querySelector(".modal"); // Ajuste para a classe/campo do modal
                let inputElement = modal?.querySelector(".custom-textarea"); // Busca dentro do modal

                if (inputElement) {
                    inputElement.value = observacaoEspelho ?? '';
                    inputElement.dispatchEvent(new Event("input")); // Se for um componente reativo
                }
            }, 300);
        }
    });

    function atualizarRequisicaoEmAnalise() {
        let requisicaoEmAnalise = obterRequisicaoEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);

            dataTablePrevencoes.ajax.reload(function () {
                selecionarLinhaTabela(dataTablePrevencoes, 0);
            }, false);
        }
        else {
            limparRequisicaoEmAnalise();
        }
    }

    function atualizarPrevencaoEmAnalise() {
        let prevencaoEmAnalise = obterPrevencaoEmAnalise();

        if (prevencaoEmAnalise) {
            $('#ViewModel_Prevencao').val(prevencaoEmAnalise.requisicaoAnterior + ' - ' + prevencaoEmAnalise.numeroProcessoOriginario + ' - ' + prevencaoEmAnalise.codTipoPrevencao);

            carregarDadosRequisicaoComparada(prevencaoEmAnalise.requisicaoAnterior);
            carregarDadosComparacao(obterRequisicaoEmAnalise(), prevencaoEmAnalise.requisicaoAnterior);
        }
        else {
            limparPrevencaoEmAnalise();
        }
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.botao-navegacao-tabela[data-tabela="RequisicoesTable"]').prop('disabled', true);
        // Limpa o datatable sem disparar Ajax.
        requisicoesDataTable.clear();

        limparPrevencaoEmAnalise();
    }

    function limparPrevencaoEmAnalise() {
        $('#ViewModel_Prevencao').val('');
        $('.botao-navegacao-tabela[data-tabela="PrevencoesTable"]').prop('disabled', true);
        // Limpa o datatable sem disparar Ajax.
        dataTablePrevencoes.clear().draw();

        limparDadosComparacao();
    }

    function carregarDadosComparacao(requisicaoPrincipal, requisicaoComparada) {
        carregarDadosTabelas(true, serviceAnalise, 'compararRequisicoes', false, requisicaoPrincipal, 'requisicaoPrincipal', 'requisicaoPrincipalRequerido', 'requisicaoPrincipalRequerente', 'requisicaoPrincipalAssunto', 'requisicaoPrincipalAtualizacaoAtual', 'requisicaoPrincipalAtualizacaoAnterior', 'requisicaoPrincipalObservacao', 'valoresAtualizados');
        atualizarCombos(serviceAnalise, true, 'Principal', requisicaoPrincipal);

        carregarDadosTabelas(true, serviceAnalise, 'compararRequisicoes', false, requisicaoComparada, 'requisicaoComparada', 'requisicaoComparadaRequerido', 'requisicaoComparadaRequerente', 'requisicaoComparadaAssunto', 'requisicaoComparadaObservacao', 'valoresAtualizadosAnterior');
        atualizarCombos(serviceAnalise, true, 'Comparado', requisicaoComparada);
    }

    function carregarDadosRequisicaoComparada(requisicaoComparada) {
        carregarDadosTabelas(true, serviceAnalise, 'compararRequisicoes', false, requisicaoComparada, 'requisicaoComparada', 'requisicaoComparadaRequerido', 'requisicaoComparadaRequerente', 'requisicaoComparadaAssunto', 'requisicaoComparadaObservacao', 'valoresAtualizadosAnterior');
        atualizarCombos(serviceAnalise, true, 'Comparado', requisicaoComparada);
    }


    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        requisicoesDataTable.ajax.reload(function () {
            selecionarLinhaTabela(requisicoesDataTable, 0);
        }, false);
    }
});
