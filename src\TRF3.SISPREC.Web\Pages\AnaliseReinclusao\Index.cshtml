@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.AnaliseReinclusao.IndexModel
@{
    PageLayout.Content.Title = "Análise de Reinclusão";

    ViewData["Meses"] = Model.MesesComItemVazio;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/js/componente-utils.js" />
    <abp-script src="/js/analises-utils.js" />
    <abp-script src="/Pages/AnaliseReinclusao/index.js" />
}

@section styles
{
    <abp-style src="/css/app/analises.css" />
    <abp-style src="/Pages/AnaliseReinclusao/index.css" />
}

<abp-card>
    <abp-card-body>

        <!--BOTÃO RECOLHER/EXPANDIR-->
        <div class="tooltip-container float-end" style="margin:-1.5rem">
            <abp-button class="ocultar-secao-botao" id="ocultarSecaoTopobtn" size="Small" button-type="Default" abp-collapse-id="ocultarSecaoTopo">ᐱ</abp-button>
            <span class="tooltiptext" id="tooltipText">Recolher</span>
        </div>

        <!--SEÇÃO OCULTAR E EXIBIR-->
        <abp-collapse-body id="ocultarSecaoTopo" show="true">
            <form asp-for="AnalisePendenciasFilterInput" id="AnalisePendenciasFilterInput">
                <partial name="Shared/Partials/_AnaliseFiltroForm" model="@Model.AnalisePendenciasFilterInput" />
            </form>

            <!--LISTAS-->
            <abp-row class="lista-dados">
                <abp-column style="max-width: 100%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="RequisicoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
            </abp-row>
        </abp-collapse-body>

        <!--BOTÕES-->
        <abp-row class="d-flex button-container">
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" text="<" disabled="true" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="RequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 140px; margin: -6px;">
                <abp-input asp-for="ReinclusaoViewModel!.NumeroProtocoloRequisicao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto" style="width: 160px; margin-right: -6px;">
                <abp-input asp-for="ReinclusaoViewModel!.NumeroProtocoloRequisicaoOriginal" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" text=">" disabled="true" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="RequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button id="btnSalvarComparada" button-type="Primary" size="Small" text="Salvar requisição comparada"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" id="cadastro-justificativa" text="Cadastrar justificativa" data-tipo-analise="4"></abp-button>
            </abp-column>
        </abp-row>

        <abp-column style="width: 100%;">
            <abp-table id="EstornoTable" small="true" striped-rows="true" border-style="Bordered" style="margin-bottom: 10px; margin-top: 10px;">
                <thead Theme="Dark">
                    <tr>
                        <th scope="Column" style="font-weight: bolder; color: #222; width: 35%;">Beneficiário</th>
                        <th scope="Column" style="font-weight: bolder; color: #222; width: 15%;">CPF/CNPJ</th>
                        <th scope="Column" style="font-weight: bolder; color: #222; width: 20%;">Número da conta corrente</th>
                        <th scope="Column" style="font-weight: bolder; color: #222; width: 15%;">Valor</th>
                        <th scope="Column" style="font-weight: bolder; color: #222; width: 15%;">Data Conta</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </abp-table>
        </abp-column>

        @await Html.PartialAsync("Components/Analises/_SecaoComparacaoView")

    </abp-card-body>
</abp-card>
