using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.AnaliseReinclusao.ViewModels;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.AnaliseReinclusao
{
    [ExcludeFromCodeCoverage]
    public class IndexModel : SISPRECPageModel
    {
        public ReinclusaoViewModel? ReinclusaoViewModel { get; set; } = new();
        public AnaliseFilterInput? AnalisePendenciasFilterInput { get; set; }
        public async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}