using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Web.Pages.AnaliseReinclusao.ViewModels
{
    [ExcludeFromCodeCoverage]
    public class ReinclusaoViewModel
    {
        [Display(Name = "Requisição")]
        public string NumeroProtocoloRequisicao { get; set; }
        [Display(Name = "Requisição original")]
        public string NumeroProtocoloRequisicaoOriginal { get; set; }
        public string Beneficiario { get; set; } = "<PERSON> Silva";

        public string BeneficiarioValor { get; set; } = "R$ 1.500,00";

        public string BeneficiarioData { get; set; } = "10/12/2024";

        public string BeneficiarioNumero { get; set; } = "12345";

        public string BeneficiarioCpfCnpj { get; set; } = "123.456.789-00";

        public string Requerido { get; set; } = "<PERSON> Oliveira";

        public string RequeridoCpfCnpj { get; set; } = "987.654.321-00";

        public string RequeridoParteAutora { get; set; } = "<PERSON> Silva";

        public string RequeridoParteAutoraCpfCnpj { get; set; } = "123.456.789-00";

        public string Requerente { get; set; } = "Carlos Souza";

        public string RequerenteCpfCnpj { get; set; } = "123.789.456-00";

        public string RequerenteAdvogado { get; set; } = "Adv. Pedro Lima";

        public string RequerenteAdvogadoCpfCnpj { get; set; } = "456.123.789-00";

        public string Assunto { get; set; } = "Revisão de valores";

        public string AssuntoProtocolo { get; set; } = "98765";

        public string AssuntoOficioJuizo { get; set; } = "OF-1234";

        public string AssuntoTransConhec { get; set; } = "Processo com transação conhecida";

        public string AssuntoTransEmbargo { get; set; } = "Embargo de declaração";

        public string Originario { get; set; } = "Origem A";

        public string OriginarioAtualiz { get; set; } = "Atualização X";

        [Display(Name = "Estorno Lei")]
        public bool OriginarioEstornoLei { get; set; } = false;
        [Display(Name = "Recomposta")]
        public bool OriginarioRecomposta { get; set; } = false;

        public string Referencias { get; set; } = "Ref. 1234";

        public string Contratual { get; set; } = "Contrato de prestação de serviços";

        public string Semelhante { get; set; } = "Caso semelhante ao processo 1234";

        public string SomaValoresAtualizados { get; set; } = "R$ 2.000,00";

        public string Anterior { get; set; } = "R$ 1.800,00";

        public string Atual { get; set; } = "R$ 2.000,00";

        public string Soma { get; set; } = "R$ 3.800,00";
    }
}
