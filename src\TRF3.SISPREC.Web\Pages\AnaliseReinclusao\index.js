$(function () {

    window.listaExportacaoObservacao = new Map();

    const createModal = new abp.ModalManager(abp.appPath + 'AnalisePrevencoes/CreateModal');
    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'An<PERSON>', 'Mes');

    const obterRequisicaoEmAnalise = () => { return dataTableRequisicoes.row({ selected: true }).id(); };
    const obterRequisicaoOriginalEmAnalise = () => {
        const rowData = dataTableRequisicoes.row({ selected: true }).data();
        return rowData ? rowData.numeroRequisicaoOriginal : null;
    };

    const getFilter = function () {
        const input = {};
        $("#AnalisePendenciasFilterInput")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnalisePendenciasFilterInput./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.analiseReinclusoes.analiseReinclusao;
    const serviceAnalises = tRF3.sISPREC.analises.analises;

    $('#btnPesquisar').on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        dataTableRequisicoes.ajax.reload(function () {
            selecionarLinhaTabela(dataTableRequisicoes, 0);
        }, false);
    }

    const dataTableRequisicoes = $('#RequisicoesTable')
        .on('preXhr.dt', function () {
            abp.ui.block({ elm: 'body', busy: true });
        })
        .on('xhr.dt', function (e, settings, json) {
            abp.ui.unblock();

            if (!json || !json.data || json.data.length == 0) {
                limparRequisicaoEmAnalise();
            }

            $('#RequisicoesTable_processing').hide();
        })
        .on('error.dt', function () {
            abp.ui.unblock();
            $('#RequisicoesTable_processing').hide();
        })
        .DataTable(abp.libs.datatables.normalizeConfiguration({
            processing: true,
            serverSide: true,
            paging: false,
            searching: false,
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            deferLoading: 0,
            select: { style: 'single', info: false },
            rowId: 'numeroProtocoloRequisicao',
            ajax: abp.libs.datatables.createAjax(service.obterAnaliseReinclusao, getFilter),
            columnDefs: [
                {
                    title: "Requisição",
                    data: "numeroProtocoloRequisicao",
                    className: "text-start"
                },
                {
                    title: "Requisição Original",
                    data: "numeroRequisicaoOriginal",
                    className: "text-start"
                }
            ]
        }))
        .on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                atualizarRequisicaoEmAnalise();

                // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                let linhaSelecionada = indexes[0];
                let totalLinhas = dt.rows().count();

                $('[data-tipo-navegacao="anterior"][data-tabela="RequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                $('[data-tipo-navegacao="proximo"][data-tabela="RequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);

                carregarEstorno();
            }
        });

    function atualizarRequisicaoEmAnalise() {
        let requisicaoEmAnalise = obterRequisicaoEmAnalise();
        let requisicaoOriginalEmAnalise = obterRequisicaoOriginalEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ReinclusaoViewModel_NumeroProtocoloRequisicao').val(requisicaoEmAnalise);
            $('#ReinclusaoViewModel_NumeroProtocoloRequisicaoOriginal').val(requisicaoOriginalEmAnalise);
        }
        else {
            limparRequisicaoEmAnalise();
        }
    }
    function limparRequisicaoEmAnalise() {
        $('#ReinclusaoViewModel_NumeroProtocoloRequisicao').val('');
        $('#ReinclusaoViewModel_NumeroProtocoloRequisicaoOriginal').val('');
        $('.botao-navegacao-tabela[data-tabela="RequisicoesTable"]').prop('disabled', true);
        // Limpa o datatable sem disparar Ajax.
        dataTableRequisicoes.clear();
    }


    $("#EstornoTable").DataTable({
        paging: false,          // Remove a paginação
        searching: false,       // Remove a barra de busca
        info: false,            // Remove o texto "Mostrando X de Y registros"
        ordering: false,        // Remove a ordenação do cabeçalho
        language: {
            emptyTable: "Nenhum dado disponível"
        },
        columns: [
            {
                data: "nome", title: "Beneficiário", className: "text-start",
                createdCell: function (td, cellData, rowData, row, col) {
                    td.id = `tdNomeBeneficiario`;
                }
            },
            {
                data: "cpfCnpj", title: "CPF/CNPJ", className: "text-start",
                createdCell: function (td, cellData, rowData, row, col) {
                    td.id = `tdCpfCnpjBeneficiario`;
                }
            },
            {
                data: "conta", title: "Número da conta corrente", className: "text-start",
                createdCell: function (td, cellData, rowData, row, col) {
                    td.id = `tdContaCnpjBeneficiario`;
                }
            },
            {
                data: "valorFormatado", title: "Valor", className: "text-start",
                createdCell: function (td, cellData, rowData, row, col) {
                    td.id = `tdValorFormatadoCnpjBeneficiario`;
                }
            },
            {
                data: "dataContaFormatado", title: "Data Conta", className: "text-start",
                createdCell: function (td, cellData, rowData, row, col) {
                    td.id = `tdDataContaFormatadoCnpjBeneficiario`;
                }
            }
        ]
    });

    // Remover classes adicionadas automaticamente pelo DataTables
    $('#EstornoTable').removeClass('dataTable no-footer');

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        console.log("AJAX Error detectado:", jqXHR.status);
        console.log("Estado do elemento de processamento:", $('#RequisicoesTable_processing').is(":visible"));

        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        // Força a ocultação do elemento de processamento do DataTables
        $('#RequisicoesTable_processing').hide();

        // Verifica se o elemento permanece visível após a tentativa de ocultá-lo
        setTimeout(() => {
            console.log("Estado do elemento de processamento após 500ms:", $('#RequisicoesTable_processing').is(":visible"));

            // Força novamente a ocultação se ainda estiver visível
            if ($('#RequisicoesTable_processing').is(":visible")) {
                $('#RequisicoesTable_processing').css('display', 'none');
            }
        }, 500);
    });

    const button = document.getElementById('ocultarSecaoTopobtn');
    const tooltip = document.getElementById('tooltipText');

    button.addEventListener('click', () => {

        button.disabled = true;

        if (button.innerHTML.trim() === 'ᐱ' && tooltip.innerText === 'Recolher') {
            button.innerHTML = 'ᐯ';
            tooltip.innerText = 'Expandir';
        } else {
            button.style.fontSize = '10px';
            button.innerHTML = 'ᐱ';
            tooltip.innerText = 'Recolher';
        }

        setTimeout(() => {
            button.disabled = false;
        }, 300);
    });

    $(document).ready(function () {
        $('#openModalButton').on('click', function () {
            $('#motivoModal').modal('show'); // Exibe o modal
        });
    });

    $(document).on('cadastroJustificativa:updated', function () { 
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    function carregarEstorno() {
        let estornoTable = $('#EstornoTable').DataTable();

        let numeroProtocoloRequisicao = obterRequisicaoEmAnalise();
        let numeroProtocoloRequisicaoOriginal = obterRequisicaoOriginalEmAnalise();

        if (numeroProtocoloRequisicao && numeroProtocoloRequisicaoOriginal) {
            service.getEstornoRequisicao(numeroProtocoloRequisicao, numeroProtocoloRequisicaoOriginal).done(function (result) {
                let data = [result]
                estornoTable.clear().rows.add(data).draw();
                carregarDadosComparacao(numeroProtocoloRequisicao, numeroProtocoloRequisicaoOriginal);
            }).fail(function () {
                abp.notify.error("Erro ao buscar estorno.");
            });
        }
        else {
            estornoTable.clear().draw();
            limparDadosComparacao();
        }
    }

    function carregarDadosComparacao(numeroProtocoloRequisicao, numeroProtocoloRequisicaoOriginal) {
        carregarDadosTabelas(true, serviceAnalises, 'compararRequisicoes', false, numeroProtocoloRequisicao, 'requisicaoPrincipal', 'requisicaoPrincipalRequerido', 'requisicaoPrincipalRequerente', 'requisicaoPrincipalAssunto', 'requisicaoPrincipalAtualizacaoAtual', 'requisicaoPrincipalAtualizacaoAnterior', 'requisicaoPrincipalObservacao', 'valoresAtualizados', 'valoresAtualizadosAtual');
        atualizarCombos(serviceAnalises, true, 'Principal', numeroProtocoloRequisicao);

        carregarDadosTabelas(true, serviceAnalises, 'compararRequisicoes', false, numeroProtocoloRequisicaoOriginal, 'requisicaoComparada', 'requisicaoComparadaRequerido', 'requisicaoComparadaRequerente', 'requisicaoComparadaAssunto', 'requisicaoComparadaObservacao', 'valoresAtualizadosAnterior');
        atualizarCombos(serviceAnalises, true, 'Comparado', numeroProtocoloRequisicaoOriginal);
    }

    $('#btnSalvarComparada').click(function (e) {        
        e.preventDefault();
        let numeroRequisicao = obterRequisicaoOriginalEmAnalise();       
        let observacaoEspelho = listaExportacaoObservacao.get(numeroRequisicao);
        

        if (numeroRequisicao) {

            createModal.open();
            const intervalId = setTimeout(() => {
                let modal = document.querySelector(".modal"); // Ajuste para a classe/campo do modal
                let inputElement = modal?.querySelector(".custom-textarea"); // Busca dentro do modal

                if (inputElement) {
                    inputElement.value = observacaoEspelho ?? '';
                    inputElement.dispatchEvent(new Event("input")); // Se for um componente reativo
                    clearInterval(intervalId);
                }
            }, 300);
        }
    });

    createModal.onResult(function () {
        let numeroRequisicao = obterRequisicaoOriginalEmAnalise();
        let observacaoEspelho = $('#ViewModel_ObservacaoGeracaoEspelho').val();
        if (observacaoEspelho !== '') {
            listaExportacaoObservacao.set(numeroRequisicao, observacaoEspelho);
        }
    });


});
