@page
@using TRF3.SISPREC.Bancos
@using TRF3.SISPREC.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@using TRF3.SISPREC.Web.Views.Shared.Components;
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;
@model TRF3.SISPREC.Web.Pages.Bancos.Banco.DetalheModalModel
@{
    Layout = null;
}

<form data-check-form-on-close="false">
    <abp-modal scrollable="true" size="Large">
        <abp-modal-header title="Banco"></abp-modal-header>
        <abp-modal-body>
            <abp-tabs>
                <abp-tab title="Detalhe">
                    <abp-input asp-for="ViewModel.BancoId" readonly="true" />
                    <abp-input asp-for="ViewModel.NomeBanco" readonly="true" />
                    <abp-input asp-for="ViewModel.Ativo" readonly="true" />
                </abp-tab>
                <abp-tab title="Histórico">
                    @await Component.InvokeAsync("Historico", new { nomeEntidade = typeof(Banco).FullName!, idEntidade = Model.ViewModel.BancoId.ToString() })
                </abp-tab>
            </abp-tabs>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Close)"></abp-modal-footer>
    </abp-modal>
</form>
