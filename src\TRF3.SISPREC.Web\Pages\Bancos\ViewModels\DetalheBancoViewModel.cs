using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace TRF3.SISPREC.Web.Pages.Bancos.Banco.ViewModels;

[ExcludeFromCodeCoverage]
public class DetalheBancoViewModel
{
    [Display(Name = "Número")]
    public int BancoId { get; set; }

    [Display(Name = "Nome")]
    public string NomeBanco { get; set; }

    [Display(Name = "Ativo")]
    public string Ativo { get; set; }
}
