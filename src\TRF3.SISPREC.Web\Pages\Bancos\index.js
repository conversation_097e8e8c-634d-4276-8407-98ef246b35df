$(function () {

    $("#BancoFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });

    //After abp v7.2 use dynamicForm 'column-size' instead of the following settings
    //$('#BancoCollapse div').addClass('col-sm-3').parent().addClass('row');

    const getFilter = function () {
        const input = {};
        $("#BancoFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/BancoFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.bancos.banco;
    const detalheModal = new abp.ModalManager(abp.appPath + 'Bancos/DetalheModal');
    const createModal = new abp.ModalManager(abp.appPath + 'Bancos/CreateModal');
    const editModal = new abp.ModalManager(abp.appPath + 'Bancos/EditModal');

    const dataTable = $('#BancoTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [[0, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getList,getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",
                                action: function (data) {
                                    detalheModal.open({ id: data.record.bancoId});
                                }
                            },
                            {
                                text: "Ativar/Desativar",
                                visible: abp.auth.isGranted('Banco.Gravar'),
                                action: function (data) {
                                    editModal.open({ id: data.record.bancoId});
                                }
                            }
                        ]
                }
            },
            {
                title: "Número",
                data: "bancoId"
            },
            {
                title: "Nome",
                data: "nomeBanco"
            },
            {
                title: "Ativo",
                data: "ativo",
                render: function (ativo, type, row, meta) {
                    if (ativo) {
                        return "Sim"
                    }

                    return "Não"
                }
            }
        ]
    }));

    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    $('#NewBancoButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});
