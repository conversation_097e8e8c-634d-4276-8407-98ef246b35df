@page
@using Microsoft.AspNetCore.Mvc.Localization
@using TRF3.SISPREC.Enums
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;
@model TRF3.SISPREC.Web.Pages.Components.CadastroJustificativas.CadastroJustificativaModal;

@{
    Layout = null;
}

<abp-style src="/Pages/Components/CadastroJustificativa/cadastroJustificativaModal.css" />
<abp-modal size="ExtraLarge">
    <form abp-model="ViewModel" data-ajaxForm="true" asp-page="CadastroJustificativaModal">
        <abp-modal-header title="Justificativa"></abp-modal-header>
        <abp-modal-body>
            <abp-row>
                <abp-column>
                    <abp-input asp-for="ViewModel.Procedimento" readonly="true" />
                </abp-column>
                <abp-column>
                    <abp-input asp-for="ViewModel.TipoAnalise" readonly="true" />
                    <input type="hidden" asp-for="TipoAnalise" />
                </abp-column>
            </abp-row>
            <abp-column>
                <div class="table-cadastro-justificativa mb-3">
                    <abp-table striped-rows="true">
                        <thead>
                            <tr>
                                <th id="request-number">Nº Requisição</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < Model.ViewModel.RequisicaoList!.Count; i++)
                            {
                                <tr>
                                    <td>
                                        @Model.ViewModel.RequisicaoList[i]
                                        <input type="hidden" asp-for="ViewModel.RequisicaoList![i]" />
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </abp-table>
                </div>
            </abp-column>
            <abp-row>
                <abp-column class="col-2">
                    <abp-select asp-for="ViewModel.Decisao" asp-items="@Model.ViewModel.DecisaoList" />
                </abp-column>
                <abp-column class="col-10">
                    <abp-select asp-for="ViewModel.Motivo" asp-items="@Model.ViewModel.MotivoList" />
                </abp-column>
            </abp-row>
            @if (Model.TipoAnalise == EDescricaoAnaliseTela.CPF_CNPJ)
            {
                <abp-row id="ExtratoRFB">
                    <abp-input asp-for="ViewModel.ExtratoRFB" accept="application/pdf" required-symbol="true" required></abp-input>
                </abp-row>
            }
            <abp-row>
                <abp-column class="6">
                    <abp-input asp-for="ViewModel.ComplementoMotivo" />
                </abp-column>
                <abp-column class="6">
                    <abp-input asp-for="ViewModel.Observacoes" />
                </abp-column>
            </abp-row>

            @if (Model.ViewModel.RequisicaoList!.Count == 1)
            {
                <abp-row>
                    <abp-row>
                        <label class="form-label">Requisições Comparadas</label>
                    </abp-row>
                    <abp-column class="col-3">
                        <input type="text" id="newItemText" placeholder="nº de requisição" class="form-control newItemInput" />
                    </abp-column>
                    <abp-column>
                        <input type="text" id="newItemTextObservacao" placeholder="observações para geração do espelho" class="form-control newItemInput" />
                    </abp-column>
                    <abp-column class="col-2">
                        <div id="controls" style="text-align:right">
                            <button id="addButton" type="button" class="btn btn-primary btn-sm">+</button>
                            <button id="deleteButton" type="button" class="btn btn-primary btn-sm" disabled>-</button>
                        </div>
                    </abp-column>
                </abp-row>
                <abp-row>
                    <abp-column>
                        <br />
                        <abp-table striped-rows="true" id="table-comparadas">
                            <thead>
                                <tr>
                                    <th id="numero-requisicao" class="col-3">Nº Requisição</th>
                                    <th id="observacao">Observações</th>
                                </tr>
                            </thead>
                            <tbody id="requisicoes-comparadas-table">
                            </tbody>
                        </abp-table>
                    </abp-column>
                </abp-row>
                <div id="requisicoesComparadas"></div>
            }
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </form>
</abp-modal>