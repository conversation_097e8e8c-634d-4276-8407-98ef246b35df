using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ConferirObservacao
{
    public class ConferirObservacaoFilterInput
    {
        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Procedimento")]
        public ETipoProcedimentoRequisicao TipoProcedimento { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Ano")]
        public int? Ano { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Mês")]
        [SelectItems(nameof(IndexModel.Meses))]
        public int Mes { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Possui Justificativa?")]
        public ESimNao? PossuiJustificativa { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Requisição")]
        public string? NumeroRequisicao { get; set; }

        [HiddenInput]
        public List<string> OpcoesCheckbox { get; set; } = [];

        [HiddenInput]
        public string? OutrosTermos { get; set; }

        public ConferirObservacaoFilterInput()
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.RPV;

            var date = DateTime.Now.AddMonths(1);
            Ano = date.Year;
            Mes = date.Month;
        }

    }
}
