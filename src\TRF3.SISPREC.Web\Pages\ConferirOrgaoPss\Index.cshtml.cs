using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.ConferirOrgaoPss
{
    public class IndexModel : SISPRECPageModel
    {
        public ConferirOrgaoPssFilterInput ConsultaFilter { get; set; } = new();

        public virtual async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }

        public class ConferirOrgaoPssFilterInput
        {
            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Procedimento")]
            public ETipoProcedimentoRequisicao TipoProcedimento { get; set; }

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Ano")]
            [Required(ErrorMessage = "AnoFieldIsRequired")]
            public int Ano { get; set; }

            [FormControlSize(AbpFormControlSize.Small)]
            [Display(Name = "Mês")]
            [SelectItems(nameof(Meses))]
            public int Mes { get; set; }

            public ConferirOrgaoPssFilterInput()
            {
                TipoProcedimento = ETipoProcedimentoRequisicao.RPV;
                var date = DateTime.Now.AddMonths(1);
                Ano = date.Year;
                Mes = date.Month;
            }
        }
    }
}