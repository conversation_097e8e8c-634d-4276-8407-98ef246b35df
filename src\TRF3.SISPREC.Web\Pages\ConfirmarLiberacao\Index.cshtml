@page
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.ConfirmarLiberacao.IndexModel
@{
    PageLayout.Content.Title = "Confirmar Liberação";
}

@section scripts
{
    <abp-script src="/Pages/ConfirmarLiberacao/index.js" />
    <abp-script src="/js/componente-utils.js" />
}

@section styles
{
    <abp-style src="/Pages/ConfirmarLiberacao/index.css" />
}

<abp-card>
    <abp-card-body>
            <abp-row style="margin-bottom: -30px;">
                <abp-column style="margin-left: -1px;">
                    <abp-dynamic-form abp-model="AnalisePrevencoesFilterInput" id="AnalisePrevencoesFilterInput" required-symbols="false" column-size="_2">
                        <abp-form-content />
                    </abp-dynamic-form>
                </abp-column>
                <abp-column class="col-auto">
                    <abp-button id="btnPesquisar" class="float-end" button-type="Primary" text="Pesquisar" size="Small" style="position: relative; top: 23px;"></abp-button>
                </abp-column>
            </abp-row>
            <br />

            <abp-row  class="d-flex align-items-center gap-1 col">
                <abp-column class="row justify-content-between align-items-center">
                    <abp-column class="d-flex align-items-center gap-1 col-2 col-12 col-sm-2" size="_2" style="max-block-size: max-content;">
                        <abp-button id="btnPrevRequisicao" class="btn btn-primary" text="<" size="Small"></abp-button>
                        <abp-input asp-for="ViewModel!.NumeroDaRequisicao" style="width: 100%;display: ruby;" readonly="true"></abp-input>
                        <abp-button id="btnNextRequisicao" class="btn btn-primary" text=">" size="Small"></abp-button>
                    </abp-column>

                    <abp-column class="col-12 col-sm-2" size="_4">
                        <abp-input asp-for="ViewModel!.TipoProcedimento" column-size="_2" readonly="true"></abp-input>
                    </abp-column>

                    <abp-column class="col-12 col-sm-2" size="_4" >
                        <abp-input asp-for="ViewModel!.Ano" column-size="_2" readonly="true"></abp-input>
                    </abp-column>

                    <abp-column class="col-12 col-sm-2" size="_4">
                        <abp-input asp-for="ViewModel!.Mes" column-size="_2" readonly="true"></abp-input>
                    </abp-column>

                    <abp-column class="col-12 col-sm-2" size="_4" >
                        <abp-input asp-for="ViewModel!.SituacaoRequisicao" column-size="_2" readonly="true"></abp-input>
                    </abp-column>

                    <abp-column class="col-12 col-sm-2" size="_4">
                        <abp-input asp-for="ViewModel!.SituacaoProposta" column-size="_2" readonly="true"></abp-input>
                    </abp-column>

                    </abp-column>
                <abp-column class="col-auto float-end">
                    <abp-button id="btnLiberar" class="float-end" button-type="Primary" text="Liberar" size="Small" style="top: 23px;width: 90px;"></abp-button>
                </abp-column>
            </abp-row>
            <br />

            <abp-row style="display: flex; flex-wrap: nowrap; margin-bottom: 100px; margin-bottom: 10px; margin-top: 10px;">
                <abp-column class="col-auto" style="flex: 1; max-width: 20%;margin-top: -10px;">
                    <abp-row style="max-height: 150px; width: 100%; margin-bottom: 1%; margin-right: 5px; overflow-y: auto;">
                        <abp-column style="width: 100%;">
                            <abp-table striped-rows="true" id="RequisicoesTable" class="nowrap" style="width: 100%;" />
                        </abp-column>
                    </abp-row>
                </abp-column>

                <abp-column class="col-auto" style="flex: 1; max-width: 80%; margin-top: -10px;">
                    <abp-row style="max-height: 150px; width: 100%; margin-bottom: 1%; margin-right: 5px; overflow-y: auto; overflow-x: hidden;">
                        <abp-table striped-rows="true" id="OcorrenciaTable" class="nowrap" style="width: 100%; cursor: pointer;" />
                    </abp-row>
                </abp-column>
            </abp-row>

            <br />

            <abp-row>
                <abp-column class="col-auto" style="flex: 1; max-width: 100%; margin-top: -10px;">
                    <h4>Análises</h4>

                    <abp-row style=" width: 100%; margin-left: auto; margin-bottom: 1%; margin-right: 5px; ">
                        <abp-table striped-rows="true" id="DadosAnalise" class="nowrap" style="width: 100%;" />
                    </abp-row>
                </abp-column>
            </abp-row>
    </abp-card-body>
</abp-card>

