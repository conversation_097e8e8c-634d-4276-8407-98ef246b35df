.tooltip-container {
    position: relative;
    display: inline-block;
    width: 200px;
}

.tooltip-container .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 95%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip-container:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

.ocultar-secao-botao {
    margin-bottom: 0px;
    width: 40px;
    height: 25px;
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
    padding-right: 0px;
    font-size: 10px;
    z-index: 1000;
    top: 70px;
    right: 30px;
}

#compareCpfModal .modal-dialog {
    margin-top: 150px;
}

.custom-textarea {
    height: auto !important;
    min-height: 80px; /* Ajuste conforme necessário */
}


#CreateModal > div > div > div.modal-body > div > div > label {
    display: none;
}


#CreateModal > div > div > div.modal-body {
    height: 85px !important;
}

    
