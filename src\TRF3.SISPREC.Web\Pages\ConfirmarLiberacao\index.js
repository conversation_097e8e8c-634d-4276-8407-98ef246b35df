$(function () {
    let currentIndexRequisicao = null;// Variável para armazenar a requisição atual em análise

    configurarFiltroProcedimentoAnoMes('AnalisePrevencoesFilterInput_TipoProcedimento', 'AnalisePrevencoesFilterInput_Ano', 'AnalisePrevencoesFilterInput_Mes');

    function validarCampo(selector, mensagem) {
        const campo = $(selector);
        const spanErro = campo.next(".erro-mensagem");
        if (!campo.val()) {
            if (spanErro.length === 0) {
                campo.after(`<span class="erro-mensagem" style="color: red;">${mensagem}</span>`);
            }
            return false;
        } else {
            spanErro.remove();
            return true;
        }
    }

    function validarCamposDinamicamente() {
        const anoPreenchido = $('#AnalisePrevencoesFilterInput_Ano').val();
        const mesPreenchido = $('#AnalisePrevencoesFilterInput_Mes').val();
        const tipoProcedimentoPreenchido = $('#AnalisePrevencoesFilterInput_TipoProcedimento').val();
        const numeroProtocoloRequisicao = $('#AnalisePrevencoesFilterInput_NumeroProtocoloRequisicao').val();

        if (tipoProcedimentoPreenchido) {
            $('#AnalisePrevencoesFilterInput_Ano, #AnalisePrevencoesFilterInput_Mes, #AnalisePrevencoesFilterInput_NumeroDaRequisicao')
                .next(".erro-mensagem").remove();
        }

        // Se campos estão vazios, mostrar erro
        if (numeroProtocoloRequisicao) return !numeroProtocoloRequisicao;

        validarCampo("#AnalisePrevencoesFilterInput_Ano", "Campo obrigatório");
        validarCampo("#AnalisePrevencoesFilterInput_Mes", "Campo obrigatório");
        validarCampo("#AnalisePrevencoesFilterInput_TipoProcedimento", "Campo obrigatório");
   
        return !(anoPreenchido && mesPreenchido && tipoProcedimentoPreenchido)  ;

    }

    $("#btnPesquisar").on('click', function (event) {
        event.preventDefault();

        if (!validarCamposDinamicamente()) {
            currentIndexRequisicao = 0;
            requisicoesTable.ajax.reload();
        }
    });

    const getFilter = function () {
        const input = {};
        $("#AnalisePrevencoesFilterInput")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnalisePrevencoesFilterInput./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.confirmarLiberacoes.confirmarLiberacoes;

    const responseCallback = function (result) {

        if (result.totalCount === 0) {
            limparCampos();

            return limparTatables();
        }

        // Se houver resultados, seleciona a primeira requisição
        if (!currentIndexRequisicao && result.items.length > 0) {
            currentIndexRequisicao = 0;

            obterDadosRequisicao(result.items[0].numeroProtocoloRequisicaoPendente);
            $('#ViewModel_NumeroDaRequisicao').val(result.items[0].numeroProtocoloRequisicaoPendente);

        }

        return {
            recordsTotal: result.totalCount,
            recordsFiltered: result.totalCount,
            data: result.items
        };
    };

    const requisicoesTable = $('#RequisicoesTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: false,
        serverSide: true,
        paging: false,// Habilita a paginação
        searching: false,
        autoWidth: false,
        scrollCollapse: true,
        deferLoading: 0,
        order: [[0, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getRequisicoes, getFilter, responseCallback),
        columnDefs: [
            {
                title: "Requisição",
                data: "numeroProtocoloRequisicaoPendente"
            }
        ]
    }));

    // Intercepta o evento antes da requisição
    $('#RequisicoesTable').on('preXhr.dt', function () {
        abp.ui.block({ elm: 'body', busy: true });
    });

    // Intercepta o evento após a requisição (sucesso ou erro)
    $('#RequisicoesTable').on('xhr.dt', function () {
        abp.ui.unblock();
    });

    $('#RequisicoesTable').on('error.dt', function () {
        abp.ui.unblock();
    });

    // Evento de clique na linha da requisição
    requisicoesTable.on('click', 'tr', async function () {
        let rowData = requisicoesTable.row(this).data();
        if (rowData) {
            currentIndexRequisicao = $(this).closest('tr').index();    
            // Exibe o número da requisição em análise

            obterDadosRequisicao(rowData.numeroProtocoloRequisicaoPendente);
            $('#ViewModel_NumeroDaRequisicao').val(rowData.numeroProtocoloRequisicaoPendente);
        }
    });

    // Função genérica para lidar com navegação para frente e para trás
    const updateNavigation = (direction, dataGenericTable, currentItemKey, updateCallback) => {        
        if (currentItemKey === -1) return; // Verifica se o item atual existe
        const rows = dataGenericTable.rows({ order: 'current' }).data();
        let currentIndex = currentItemKey; // Inicializa com -1 (caso não encontre)

        // Navega de acordo com a direção
        if (direction === 'next' && currentIndex < rows.length - 1) {
            currentIndex++;
        } else if (direction === 'prev' && currentIndex > 0) {
            currentIndex--;
        }

        // Atualiza a chave do item e executa o callback de atualização
        if (currentIndex >= 0 && currentIndex < rows.length) {
            const currentItem = rows[currentIndex];
            updateCallback(currentItem, currentIndex); // Chama o callback para atualizar o estado necessário
        }
    };

    // Exemplo de como usar a função para a navegação de "Requisição"
    const updateRequisicaoNavigation = (direction) => {
        updateNavigation(direction, requisicoesTable, currentIndexRequisicao, (currentItem, newIndex) => {
            currentIndexRequisicao = newIndex;
            obterDadosRequisicao(currentItem.numeroProtocoloRequisicaoPendente);
            $('#ViewModel_NumeroDaRequisicao').val(currentItem.numeroProtocoloRequisicaoPendente);
        });
    };

    // Adiciona eventos para navegação para frente e para trás
    $('#btnNextRequisicao').on('click', function () {
        updateRequisicaoNavigation('next');
    });

    $('#btnPrevRequisicao').on('click', function () {
        updateRequisicaoNavigation('prev');
    });

    const filterTables = function () { 
         
        const input = ($('#ViewModel_NumeroDaRequisicao').val().trim() || '000000');

        return input;
    };
     
    const responseDataCallback = function (result) {

        if (result.totalCount === 0) { 
            return limparTatables();
        }

        return {
            recordsTotal: result.totalCount,
            recordsFiltered: result.totalCount,
            data: result.items
        };
    };

    const dataTableOcorrencias = $('#OcorrenciaTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: false,// Habilita a paginação
        searching: false,
        autoWidth: false,
        scrollCollapse: true,
        deferLoading: 0,
        ordering: false,
        ajax: abp.libs.datatables.createAjax(service.getOcorrencias, filterTables, responseDataCallback),
        columnDefs: [

            {
                title: "Tipo Ocorrência", data: "tipoOcorrencia"
            },
            {
                title: "Detalhe", data: "descricao"
            },
            {
                title: "Data Ocorrência",
                data: "dataOCorrencia",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')   
            }
        ]
    }));

    const dataTableDados = $('#DadosAnalise').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: false,// Habilita a paginação
        searching: false,
        autoWidth: false,
        scrollX: true,
        scrollCollapse: true,
        deferLoading: 0,
        ordering: false,
        ajax: abp.libs.datatables.createAjax(service.getJustificastivaAnalises, filterTables, responseDataCallback),
        columnDefs: [
            {
                title: "Tipo Análise",
                data: "descricaoTela"
            },
            {
                title: "Data Análise",
                data: "dataAnalise",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')                
            },
            {
                title: "Usuário",
                data: "usuarioAnalise"
            },
            {
                title: "Decisão",
                data: "tipoAcao"
            },
            {
                title: "Motivo",
                data: "descricaoJustificativa"
            },
            {
                title: "Complemento do Motivo",
                data: "complementoJustificativa"
            }
        ]
    }));

    async function obterDadosRequisicao(numeroProtocoloRequisicao) {
        if (numeroProtocoloRequisicao) {

            let dados = await service.getInformacaoBasica(numeroProtocoloRequisicao);

            if (dados != null) {
                $('#ViewModel_TipoProcedimento').val(dados.tipoProcedimento);
                $('#ViewModel_Ano').val(dados.anoPropos);
                $('#ViewModel_Mes').val(dados.mesPropos);
                $('#ViewModel_SituacaoRequisicao').val(dados.situacaoRequisicao);
                $('#ViewModel_SituacaoProposta').val(dados.situacaoProposta);
            }

            dataTableOcorrencias.ajax.reload();
            dataTableDados.ajax.reload();
        }
    } 

    const limparCampos = function () {

        $('#ViewModel_NumeroDaRequisicao').val('');
        $('#ViewModel_NumeroDaRequisicao').trigger('change');

        $('#ViewModel_TipoProcedimento').val('');
        $('#ViewModel_Ano').val('');
        $('#ViewModel_Mes').val('');
        $('#ViewModel_SituacaoRequisicao').val('');
        $('#ViewModel_SituacaoProposta').val(''); 

        dataTableOcorrencias.clear().draw();
        dataTableDados.clear().draw();
 
    }

    function limparTatables() {
        return {
            recordsTotal: 0,
                recordsFiltered: 0,
                    data: []
        }
    }

});
