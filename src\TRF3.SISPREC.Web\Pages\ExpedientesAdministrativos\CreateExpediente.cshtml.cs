using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using TRF3.SISPREC.ExpedientesAdministrativos;
using TRF3.SISPREC.ExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.MotivosExpedientesAdministrativos;
using TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos.ViewModels;

namespace TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos;

public class CreateExpedienteModel : SISPRECPageModel
{
    [BindProperty]
    public CreateEditExpedientesAdministrativosViewModel ViewModel { get; set; } = new();

    private readonly IMotivoExpedienteAdministrativoAppService _service;
    private readonly IExpedienteAdministrativoAppService _expedienteService;

    public CreateExpedienteModel(IMotivoExpedienteAdministrativoAppService service, IExpedienteAdministrativoAppService expedienteService)
    {
        _service = service;
        _expedienteService = expedienteService;
    }

    public virtual async Task OnGetAsync()
    {
        var motivos = await _service.ListarTodos();

        ViewModel.MotivoLookupList.AddRange(motivos.Select(x => new SelectListItem
        {
            Value = x.ExpedienteAdministrativoMotivoId.ToString(),
            Text = x.DescricaoMotivo
        }));

        await Task.CompletedTask;
    }
    [ExcludeFromCodeCoverage]
    public virtual async Task<IActionResult> OnPostAsync()
    {
        var dto = ObjectMapper.Map<CreateEditExpedientesAdministrativosViewModel, CreateUpdateExpedienteAdminstrativoDto>(ViewModel);
        await _expedienteService.CreateAsync(dto);
        return NoContent();
    }
}