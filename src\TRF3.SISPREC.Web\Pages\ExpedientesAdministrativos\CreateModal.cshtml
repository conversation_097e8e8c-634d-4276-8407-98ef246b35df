@page
@using Microsoft.AspNetCore.Mvc.Localization
@using TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;
@model TRF3.SISPREC.Web.Pages.ExpedientesAdministrativos.CreateModalModel
@{
    Layout = null;
}
<abp-modal size="Small" id="expedienteAdministrativoCreateModal">
    <form abp-model="ViewModel" data-ajaxForm="true" asp-page="CreateModal">
        <abp-modal-header title="Incluir em Bloco Existente"></abp-modal-header>
        <abp-modal-body>
            <abp-row>
                <abp-column class="text-start">
                    <abp-select asp-for="ViewModel.BlocoId" asp-items="@Model.BlocoLista" />
                    @for (int i = 0; i < Model.ViewModel.ExpedienteId!.Count; i++)
                    {
                        <input type="hidden" asp-for="ViewModel.ExpedienteId[i]" />
                    }
                </abp-column>
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </form>
</abp-modal>

