#removeRequisicao.disabled,
#removeRequisicao:disabled {
    background-color: #e9ecef !important;
    color: #6c757d !important;
    border-color: #ced4da !important;
    cursor: not-allowed !important;
    pointer-events: none;
    opacity: 0.65;
}

#listaRequisicoes {
    display: block;
    max-height: 100px;
    overflow-y: auto;
    width: 100%;
}

    #listaRequisicoes tr {
        padding: 10px;
        border-bottom: 1px solid #e0e6ec;
        cursor: pointer;
        transition: background-color 0.3s;
        width: 100%;
    }

        #listaRequisicoes tr:hover {
            background-color: #e8f1f8 !important;
        }

        #listaRequisicoes tr.selected {
            background-color: #3565ff !important;
            color: #ffffff !important;
        }

            #listaRequisicoes tr.selected td {
                background-color: #3565ff !important;
                color: #ffffff !important;
            }

#tabelaRequisicoes {
    width: 100%;
    border-collapse: collapse;
}

    #tabelaRequisicoes thead {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

    #tabelaRequisicoes tbody {
        display: block;
        max-height: 100px;
        overflow-y: auto;
        width: 100%;
    }

        #tabelaRequisicoes tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }