$(function () {
    const selectedItems = new Set();
    const requisicoes = [];

    const $motivoSelect = $('#ViewModel_MotivoId');
    const $motivoComplemento = $('#ViewModel_ObservacaoExpedienteAdministrativo');
    const $newItemInput = $('#newItemText');
    const $requisicaoList = $('#listaRequisicoes');
    const $removeBtn = $('#removeRequisicao');
    const $requisicoesHiddenInput = $('#RequisicoesHiddenInput');

    const motivoService = tRF3.sISPREC.motivosExpedientesAdministrativos.motivoExpedienteAdministrativo;
    const requisicaoService = tRF3.sISPREC.requisicoesProtocolos.requisicaoProtocolo;

    function toggleSelection(row) {
        const itemId = $(row).data('id');
        const isSelected = $(row).hasClass('selected');
        $(row).toggleClass('selected');
        isSelected ? selectedItems.delete(itemId) : selectedItems.add(itemId);
        updateDeleteButtonState();
    }

    function updateHiddenInput() {
        $requisicoesHiddenInput.val(JSON.stringify(requisicoes));
    }

    $motivoSelect.on('change', function () {
        const selectedText = $(this).find('option:selected').text();
        $motivoComplemento.val(selectedText);
    });

    const createModalMotivos = new abp.ModalManager({
        viewUrl: abp.appPath + 'MotivosExpedientesAdministrativos/CreateModal',
        backdrop: 'static',
        keyboard: false
    });

    createModalMotivos.onResult(() => {
        abp.notify.success('Motivo cadastrado com sucesso!');
        motivoService.listarTodos().then(result => {
            $motivoSelect.empty().append(new Option('', '', true, true));
            result.forEach(item =>
                $motivoSelect.append(new Option(item.descricaoMotivo, item.expedienteAdministrativoMotivoId))
            );
        });
    });

    $('#addMotivo').click(e => {
        e.preventDefault();
        createModalMotivos.open();
    });

    $('#addRequisicao').on('click', addNewItem);

    document.getElementById("btnCancelar").addEventListener("click", function (event) {
        event.preventDefault();
        window.history.back();
    });

    function addNewItem() {
        abp.ui.block({ elm: 'body', busy: false });
        const newItemText = $newItemInput.val();

        if (!isRequisicaoValida(newItemText)) return;

        if (requisicaoJaAdicionada(newItemText)) {
            abp.message.warn('Requisição já adicionada.');
            abp.ui.unblock();
            return;
        }

        requisicaoService.getRequisicaoById(newItemText)
            .then(requisicao => {
                $requisicaoList.append(criarNovaLinha(requisicao, newItemText));
                requisicoes.push(newItemText);
                updateHiddenInput();
                $newItemInput.val('');
                abp.ui.unblock();
            })
            .catch(error => {
                abp.message.error('Requisição não encontrada.');
                abp.ui.unblock();
            });
    }

    function isRequisicaoValida(text) {
        if (!text) {
            abp.message.warn('Por favor, inserir um número de requisição.');
            return false;
        }
        if (text.length > 20) {
            abp.message.warn('O nº de requisição deve ter no máximo 20 caracteres.');
            return false;
        }
        return true;
    }

    function requisicaoJaAdicionada(id) {
        return requisicoes.includes(id);
    }

    function criarNovaLinha(requisicao, id) {
        return `
            <tr class="item" data-id="${id}">
                <td style="width: 90px">${id}</td>
                <td style="width: 110px">${requisicao.cpfCnpj || ''}</td>
                <td style="width: 250px">${requisicao.requerente || ''}</td>
                <td style="width: 90px">${requisicao.codSiafi || ''}</td>
                <td style="width: 250px">${requisicao.juizo || ''}</td>
            </tr>`;
    }

    $requisicaoList.on('click', 'tr', function () {
        toggleSelection(this);
    });

    $removeBtn.on('click', deleteSelectedItems);

    function updateDeleteButtonState() {
        $removeBtn.prop('disabled', selectedItems.size === 0);
    }

    function deleteSelectedItems() {
        selectedItems.forEach(id => {
            const idStr = id.toString();
            $(`.item[data-id="${idStr}"]`).remove();
            const index = requisicoes.findIndex(r => r.toString() === idStr);
            if (index !== -1) {
                requisicoes.splice(index, 1);
            }
        });

        updateHiddenInput();
        selectedItems.clear();
        updateDeleteButtonState();
    }

    $('#ExpedienteForm').on('abp-ajax-success', function (event, data, xhr) {
        abp.notify.success("Expediente Administrativo cadastrado com sucesso!", "Sucesso", { okText: "OK" });
        setTimeout(() => {
            window.location.href = abp.appPath + "ExpedientesAdministrativos";
        }, 1000);
    });
});
