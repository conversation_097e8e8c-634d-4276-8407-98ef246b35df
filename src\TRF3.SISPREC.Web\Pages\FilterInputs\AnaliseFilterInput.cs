using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.FilterInputs;

[ExcludeFromCodeCoverage]
public class AnaliseFilterInput()
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Procedimento")]
    public ETipoProcedimentoRequisicao? TipoProcedimento { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ano")]
    public int? Ano { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Mês")]
    [SelectItems(nameof(IndexModel.MesesComItemVazio))]
    public int? Mes { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Data Início")]
    public DateTime? DataInicio { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Data Término")]
    public DateTime? DataTermino { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Requisição")]
    public string? NumeroProtocoloRequisicao { get; set; }
}