using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.TipoIndicadorEconomicos;

public class IndexModel : SISPRECPageModel
{
    public IndicadorEconomicoTipoFilterInput IndicadorEconomicoTipoFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

public class IndicadorEconomicoTipoFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Id")]
    public int? TipoIndicadorEconomicoId { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Código")]
    public string? Codigo { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

}
