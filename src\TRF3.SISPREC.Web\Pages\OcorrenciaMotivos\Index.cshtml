@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.OcorrenciaMotivos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Motivo Ocorrência";
    PageLayout.Content.BreadCrumb.Add("OcorrenciaMotivos");
    PageLayout.Content.MenuItemName = SISPRECMenus.OcorrenciaMotivo;
}

@section scripts
{
    <abp-script src="/js/exportacao-pdf.js" />
    <abp-script src="/Pages/OcorrenciaMotivos/index.js" />
}
@section styles
{
    <abp-style src="/Pages/OcorrenciaMotivos/index.css" />
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="OcorrenciaMotivoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewOcorrenciaMotivoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>            
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-dynamic-form abp-model="OcorrenciaMotivoFilter" id="OcorrenciaMotivoFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="OcorrenciaMotivoCollapse">
                <abp-form-content />  
            </abp-collapse-body>
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="OcorrenciaMotivoTable" class="nowrap" />
    </abp-card-body>
</abp-card>
