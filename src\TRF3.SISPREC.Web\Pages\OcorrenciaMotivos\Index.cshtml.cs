using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.OcorrenciaMotivos;

public class IndexModel : SISPRECPageModel
{
    public OcorrenciaMotivoFilterInput OcorrenciaMotivoFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        OcorrenciaMotivoFilter.AcaoTipoLookupList.AddRange(EnumExtensions.GetEnumSelectList<EDescricaoAcaoTipo>().OrderBy(x => x.Text).ToList());
        OcorrenciaMotivoFilter.AnaliseTelaLookupList.AddRange(EnumExtensions.GetEnumSelectList<EDescricaoAnaliseTela>().OrderBy(x => x.Text).ToList());

        await Task.CompletedTask;
    }
}

public class OcorrenciaMotivoFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Código Motivo Ocorrência")]
    public int? CodigoMotivo { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Descrição Motivo")]
    public string? DescricaoMotivo { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ação Tipo")]
    [SelectItems(nameof(AcaoTipoLookupList))]
    public int? AcaoTipoId { get; set; }
    public List<SelectListItem> AcaoTipoLookupList { get; set; } = new List<SelectListItem>()
    {
        new SelectListItem(string.Empty, null)
    };

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Tipo Análise")]
    [SelectItems(nameof(AnaliseTelaLookupList))]
    public int? AnaliseTelaId { get; set; }
    public List<SelectListItem> AnaliseTelaLookupList { get; set; } = new List<SelectListItem>()
    {
        new SelectListItem(string.Empty, null)
    };

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }
}
