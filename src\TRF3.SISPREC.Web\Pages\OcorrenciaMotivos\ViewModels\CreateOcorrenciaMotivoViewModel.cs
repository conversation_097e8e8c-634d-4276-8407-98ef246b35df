using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Web.Pages.OcorrenciaMotivos.ViewModels;

public class CreateOcorrenciaMotivoViewModel
{
    [Display(Name = "Código do Motivo")]
    [Required]
    public int CodigoMotivo { get; set; }

    [Display(Name = "Ação Tipo")]
    public EDescricaoAcaoTipo AcaoTipoId { get; set; }
    public List<SelectListItem> AcaoTipoLookupList { get; set; } = new();

    [Display(Name = "Tipo Análise")]
    public int? AnaliseTelaId { get; set; }
    public List<SelectListItem> AnaliseTelaLookupList { get; set; } = new();

    [Display(Name = "Descrição Motivo")]
    [Required]
    public string? DescricaoMotivo { get; set; }

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; } = true;

}
