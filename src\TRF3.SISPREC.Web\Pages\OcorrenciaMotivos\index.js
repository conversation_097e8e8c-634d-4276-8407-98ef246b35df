$(function () {

    $("#OcorrenciaMotivoFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });

    //After abp v7.2 use dynamicForm 'column-size' instead of the following settings
    //$('#OcorrenciaMotivoCollapse div').addClass('col-sm-3').parent().addClass('row');

    const getFilter = function () {
        const input = {};
        $("#OcorrenciaMotivoFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/OcorrenciaMotivoFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.ocorrenciaMotivos.ocorrenciaMotivo;
    const detalheModal = new abp.ModalManager(abp.appPath + 'OcorrenciaMotivos/DetalheModal');
    const createModal = new abp.ModalManager(abp.appPath + 'OcorrenciaMotivos/CreateModal');
    const editModal = new abp.ModalManager(abp.appPath + 'OcorrenciaMotivos/EditModal');

    const dataTable = $('#OcorrenciaMotivoTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getList,getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",                                
                                action: function (data) {
                                    detalheModal.open({ id: data.record.ocorrenciaMotivoId });
                                }
                            }
                             ,
                            {
                                text: "Alterar",                                
                                action: function (data) {
                                    editModal.open({ id: data.record.ocorrenciaMotivoId });
                                }
                            },
                            {
                                text: "Excluir",                                
                                confirmMessage: function (data) {
                                    return "Tem certeza de que deseja excluir?"
                                },
                                action: function (data) {
                                    abp.ui.block({ elm: 'body', busy: true });
                                    service.delete(data.record.ocorrenciaMotivoId )
                                        .then(function () {
                                            abp.notify.success('Motivo Ocorrência excluído com sucesso!');
                                        }).always(function () {
                                            dataTable.ajax.reload();
                                            abp.ui.unblock();
                                        });
                                }
                            }
                        ]
                }
            },            
            {
                title: "Código Motivo Ocorrência",
                data: "codigoMotivo"
            },            
            {
                title: "Descrição Motivo",
                data: "descricaoMotivo"
            },
            {
                title: "Ação Tipo",
                data: "acaoTipoId",
                render: function (acaoTipoId, type, row, meta) {
                    if (acaoTipoId == 1) {
                        return "DEVOLUÇÃO"
                    }
                    if (acaoTipoId == 2) {
                        return "PENDENTE"
                    }
                    if (acaoTipoId == 3) {
                        return "CANCELAMENTO"
                    }
                    if (acaoTipoId == 4) {
                        return "INFORMAÇÃO"
                    }

                    return "LIBERAÇÃO"
                }
            },
            {
                title: "Tipo Análise",
                data: "analiseTelaId",
                render: function (analiseTelaId, type, row, meta) {
                    if (analiseTelaId == 1) {
                        return "CPF/CNPJ"
                    }
                    if (analiseTelaId == 2) {
                        return "Pendências"
                    }
                    if (analiseTelaId == 3) {
                        return "Prevenção"
                    }
                    if (analiseTelaId == 4) {
                        return "Reinclusão"
                    }
                    if (analiseTelaId == 5) {
                        return "Campo Observação"
                    }
                    if (analiseTelaId == 6) {
                        return "Órgão PSS"
                    }
                    if (analiseTelaId == 7) {
                        return "Nomes de Partes"
                    }

                    return ""
                }
            },
            {
                title: "Ativo",
                data: "ativo",
                render: function (ativo, type, row, meta) {
                    if (ativo) {
                        return "SIM"
                    }

                    return "NÃO"
                }
            },               
        ]
    }));

    createModal.onResult(function () {
        dataTable.ajax.reload();
        abp.notify.success('Motivo Ocorrência cadastrado com sucesso!');
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
        abp.notify.success('Motivo Ocorrência alterado com sucesso!');
    });

    detalheModal.onOpen(function () {
        detalheModal.getModal().removeAttr('data-check-form-on-close');
    });

    downloadPDF('btnExportarPDF', 'ocorrencia-motivo', service, getFilter);

    $('#NewOcorrenciaMotivoButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});
