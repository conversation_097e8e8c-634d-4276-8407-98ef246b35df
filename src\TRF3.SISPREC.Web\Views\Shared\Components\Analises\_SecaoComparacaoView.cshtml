@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model dynamic

@{
    Layout = null;
}

@section scripts
{
}

<div id="secaoComparacao">
    <!--REQUISICAO-->
    <abp-column style="overflow-x: auto; width: 100%;">
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="margin-bottom: 10px; margin-top: 10px;">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Requisição</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Procedimento</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Proposta</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Mês</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Tipo Requisição</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Natureza</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Honorário</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Tipo (T/C/S/I)</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Situação Proposta</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Situação Requisição</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Valor</th>
                    <th scope="Column" style="font-weight: bolder; color: #222;">Data Conta</th>
                </tr>
            </thead>
            <tbody id="dtRequisicao">
                <tr class="requisicaoPrincipal" style="height: 25px;">
                    <td id="tdRequisicao"></td>
                    <td id="tdProcedimento"></td>
                    <td id="tdProposta"></td>
                    <td id="tdMes"></td>
                    <td id="tdTipoRequisicao"></td>
                    <td id="tdNatureza"></td>
                    <td id="tdHonorario"></td>
                    <td id="tdTipo"></td>
                    <td id="tdSituacaoProposta"></td>
                    <td id="tdSituacaoRequisicao"></td>
                    <td id="tdValor"></td>
                    <td id="tdDataConta"></td>
                </tr>
                <tr class="requisicaoComparada" style="height: 25px;">
                    <td id="tdRequisicao"></td>
                    <td id="tdProcedimento"></td>
                    <td id="tdProposta"></td>
                    <td id="tdMes"></td>
                    <td id="tdTipoRequisicao"></td>
                    <td id="tdNatureza"></td>
                    <td id="tdHonorario"></td>
                    <td id="tdTipo"></td>
                    <td id="tdSituacaoProposta"></td>
                    <td id="tdSituacaoRequisicao"></td>
                    <td id="tdValor"></td>
                    <td id="tdDataConta"></td>
                </tr>
            </tbody>
        </abp-table>
    </abp-column>

    <!--REQUERIDO-->
    <abp-table small="true" striped-rows="true" border-style="Bordered" style="margin-bottom: 10px;">
        <thead Theme="Dark">
            <tr>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 30%;">Requerido</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 20%;">CPF/CNPJ Requerido</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 30%;">Parte Autora</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 20%;">CPF/CNPJ Parte Autora</th>
            </tr>
        </thead>
        <tbody id="dtRequisicaoRequerido">
            <tr class="requisicaoPrincipalRequerido" style="height: 24px;">
                <td id="tdRequerido"></td>
                <td id="tdCpfCnpjRequerido"></td>
                <td id="tdParteAutora"></td>
                <td id="tdCpfCnpjAutor"></td>
            </tr>
            <tr class="requisicaoComparadaRequerido" style="height: 24px;">
                <td id="tdRequerido"></td>
                <td id="tdCpfCnpjRequerido"></td>
                <td id="tdParteAutora"></td>
                <td id="tdCpfCnpjAutor"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--REQUERENTE-->
    <abp-table small="true" striped-rows="true" border-style="Bordered" style="margin-bottom: 10px;">
        <thead Theme="Dark">
            <tr>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 30%;">Requerente</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 20%;">CPF/CNPJ Requerente</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 30%;">Advogado</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 20%;">CPF/CNPJ Advogado</th>
            </tr>
        </thead>
        <tbody id="dtRequisicaoRequerente">
            <tr class="requisicaoPrincipalRequerente" style="height: 24px;">
                <td id="tdRequerente"></td>
                <td id="tdCpfCnpjRequerente"></td>
                <td id="tdAdvogado"></td>
                <td id="tdAdvogadoCpfCnpj"></td>
            </tr>
            <tr class="requisicaoComparadaRequerente" style="height: 24px;">
                <td id="tdRequerente"></td>
                <td id="tdCpfCnpjRequerente"></td>
                <td id="tdAdvogado"></td>
                <td id="tdAdvogadoCpfCnpj"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--ASSUNTO-->
    <abp-table small="true" striped-rows="true" border-style="Bordered" style="margin-bottom: 10px;">
        <thead Theme="Dark">
            <tr>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 55%">Assunto</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 10%">Data Protocolo</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 10%">Ofício do Juízo</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 12%">Trâns. Conhecimento</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 10%">Trâns. Embargos</th>
                <th scope="Column" style="font-weight: bolder; color: #222; width: 6%">Atualização</th>
            </tr>
        </thead>
        <tbody id="dtRequisicaoAssunto">
            <tr class="requisicaoPrincipalAssunto" style="height: 24px;">
                <td id="tdAssunto"></td>
                <td id="tdDataProtocolo"></td>
                <td id="tdOficioJuizo"></td>
                <td id="tdTransacaoConhecida"></td>
                <td id="tdEmbargoDeclaracao"></td>
                <td id="tdCodigoTipoIndicadorEconomico"></td>
            </tr>
            <tr class="requisicaoComparadaAssunto" style="height: 24px;">
                <td id="tdAssunto"></td>
                <td id="tdDataProtocolo"></td>
                <td id="tdOficioJuizo"></td>
                <td id="tdTransacaoConhecida"></td>
                <td id="tdEmbargoDeclaracao"></td>
                <td id="tdCodigoTipoIndicadorEconomico"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--OBSERVCAO-->
    <div style="display: flex; justify-content: space-between; gap: 20px;">
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 4; margin-bottom: 10px">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222; text-align: center;">Observação</th>
                </tr>
            </thead>
            <tbody id="dtRequisicaoObservacao">
                <tr class="requisicaoPrincipalObservacao" style="height: 24px;">
                    <td id="tdObservacao"></td>
                </tr>
                <tr class="requisicaoComparadaObservacao">
                    <td id="tdObservacao"></td>
                </tr>
            </tbody>
        </abp-table>

        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
            <div style="display: flex; align-items: center; margin-top: 0px; margin-left: -15px;">
                <label for="ckEstornoLei">
                    <input type="checkbox" id="ckEstornoLei"> Estorno Lei 13463
                </label>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 5px; margin-left: -52px;">
                <label for="ckRecomposta">
                    <input type="checkbox" id="ckRecomposta"> Recomposta
                </label>
            </div>
        </div>

    </div>

    <!--ORIGINARIO-->
    <div style="display: flex; justify-content: space-between; gap: 20px;">
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 2; margin-bottom: 10px;">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222; text-align: center;">Originário</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <select name="originario" id="comboOriginarioPrincipal" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <select name="originario" id="comboOriginarioComparado" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
            </tbody>
        </abp-table>

        <!--EXPEDIENTE-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 1; margin-bottom: 10px">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222; text-align: center;">Expediente</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <select name="originario" id="comboExpedientePrincipal" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <select name="originario" id="comboExpedienteComparado" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
            </tbody>
        </abp-table>

        <!--SEMELHANTE-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 1">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222; text-align: center;">Semelhante</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <select name="originario" id="comboSemelhantePrincipal" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
            </tbody>
        </abp-table>

    </div>

    <div style="display: flex; justify-content: space-between; gap: 20px;">
        <!--CONTRATUAL-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 2; margin-bottom: 0.0%">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222; text-align: center;">Contratual</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <select name="originario" id="comboContratualPrincipal" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <select name="originario" id="comboContratualComparado" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
            </tbody>
        </abp-table>

        <!--REFERENCIA-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 1; margin-bottom: 0.0%;">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" style="font-weight: bolder; color: #222; text-align: center;">Referências</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <select name="originario" id="comboReferenciaPrincipal" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <select name="originario" id="comboReferenciaComparado" style="width: 100%; color: black; border: none; height: fit-content">
                            <option value=""></option>
                        </select>
                    </td>
                </tr>
            </tbody>
        </abp-table>

        <!--VALORES-->
        <abp-table small="true" striped-rows="false" border-style="Bordered" style="flex: 1; margin-bottom: 0.0%">
            <thead Theme="Dark">
                <tr>
                    <th scope="Column" colspan="2" style="font-weight: bolder; color: #222; text-align: center;">Soma dos valores atualizados</th>
                </tr>
            </thead>
            <tbody id="dtRequisicaoValores">
                <tr class="valoresAtualizadosAtual">
                    <th id="thValorAtual" style="font-weight: bold; color: black; background-color: whitesmoke; width: 40%;">Atual</th>
                    <td id="tdAtualizacaoAtual"></td>
                </tr>
                <tr class="valoresAtualizadosAnterior">
                    <th id="thValorAnterior" style="font-weight: bold; color: black; background-color: whitesmoke;">Anterior</th>
                    <td id="tdAtualizacaoAnterior"></td>
                </tr>
                <tr class="valoresAtualizadosSoma">
                    <th id="thValorSoma" style="font-weight: bold; color: black; background-color: whitesmoke;">Soma</th>
                    <td id="tdSomaDosValoresAtualizados"></td>
                </tr>
            </tbody>
        </abp-table>
    </div>
</div>