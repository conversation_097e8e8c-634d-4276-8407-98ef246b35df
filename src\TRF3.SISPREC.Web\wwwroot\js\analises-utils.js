function carregarDadosTabelas(estilizarPersonalizado, service, metodo, tabelaId, numeroProtocoloRequisicao, ...classesLinhas) {

    const mapeamentoCampos = {
        "tdRequisicao": "numeroProtocoloRequisicao",
        "tdProcedimento": "tipoProcedimento",
        "tdProposta": "anoProposta",
        "tdMes": "mesProposta",
        "tdValor": "valorRequisicao",
        "tdTipoRequisicao": "indicadorInclusaoRequisicao",
        "tdDataConta": "dataContaLiquidacao",
        "tdNatureza": "naturezaCredito",
        "tdHonorario": "tipoHonorario",
        "tdSituacaoProposta": "situacaoRequisicaoProposta",
        "tdRequerido": "nomePessoaRequerido",
        "tdCpfCnpjRequerido": "numeroCnpjCpfPessoaRequerido",
        "tdParteAutora": "nomePessoaAutor",
        "tdCpfCnpjAutor": "numeroCnpjCpfPessoaAutor",
        "tdRequerente": "nomePessoaRequerente",
        "tdCpfCnpjRequerente": "numeroCnpjCpfPessoaRequerente",
        "tdAdvogado": "nomeAdvogado",
        "tdAdvogadoCpfCnpj": "numeroCnpjCpfAdvogado",
        "tdAssunto": "descricaoCJFAssunto",
        "tdDataProtocolo": "dataHoraProtocoloRequisicao",
        "tdOficioJuizo": "numeroOficioRequisitorio",
        "tdTransacaoConhecida": "dataTransitoJulgadoFase",
        "tdEmbargoDeclaracao": "dataTransitoJulgadoEmbargos",
        "tdAtualizacaoAtual": "valorAtualizadoRequisicaoAtual",
        "tdAtualizacaoAnterior": "valorAtualizadoRequisicaoAnterior",
        "tdObservacao": "observacao",
        "tdSituacaoRequisicao": "descricaoSituacao",
        "tdTipo": "tipoRequisicao",
        "tdIndicadorEstorno": "indicadorEstornoRequisicao",
        "tdSomaDosValoresAtualizados": "somaDosValoresAtualizados",
        "tdCodigoTipoIndicadorEconomico": "codigoTipoIndicadorEconomico"
    };

    if (classesLinhas.length === 1 && typeof classesLinhas[0] === 'string') {
        classesLinhas = [classesLinhas[0]];
    }

    const obterDados = (numeroProtocoloRequisicao) => {
        return service[metodo](numeroProtocoloRequisicao)
            .then(data => (data && data.length > 0 ? data : null))
            .catch(error => {
                console.error(`Erro ao buscar dados com o método ${metodo}: `, error);
                return null;
            });
    };

    const preencherLinhas = (dados) => {
        
        $('#tdSomaDosValoresAtualizados').text('');        

        if (dados && dados.length > 0) {

            classesLinhas.forEach(classeLinha => {
                const linha = $(`.${classeLinha}`).first(); // Seleciona a primeira linha da classe

                Object.keys(mapeamentoCampos).forEach(id => {
                    linha.find(`#${id}`).text('');
                });

                dados.forEach(item => {
                    Object.entries(mapeamentoCampos).forEach(([id, campo]) => {                        
                        linha.find(`#${id}`).text(item[campo] || '');
                    });
                });
            });
            
            const valorAtual = $('#tdAtualizacaoAtual').text();
            const valorAnterior = $('#tdAtualizacaoAnterior').text();

            verificaValoresEmBranco(valorAtual, valorAnterior);

            calcularSomaValoresAtualizados(valorAtual, valorAnterior);

            if (dados.some(d => d.indicadorEstornoRequisicao === 'ATIVA')) {
                $('#ckEstornoLei').prop('checked', true);
            } else {
                $('#ckEstornoLei').prop('checked', false);
            }


        } else {

            if (tabelaId != false) {
                $(`#${tabelaId} tbody`).html('<tr class="dataTables_empty"><td colspan="3" valign="top">Nenhum dado disponível</td></tr>');
            }
        }
    };
    
    obterDados(numeroProtocoloRequisicao)
        .then(data => preencherLinhas(data))
        .then(() => {
            setTimeout(() => {
                executarComparacao('[id^="td"], select[id^="combo"]', 'igual', estilizarPersonalizado);
            }, 500);
        });
}

function atualizarCombos(service, habilitado, linhaComparacao, numeroProtocoloRequisicao) {

    if (habilitado === true) {
        carregarComboComDados(service, 'buscaOriginariosAnalise', `comboOriginario${linhaComparacao}`, numeroProtocoloRequisicao);
        carregarComboComDados(service, 'buscaExpedientesAnalise', `comboExpediente${linhaComparacao}`, numeroProtocoloRequisicao);
        carregarComboComDados(service, 'buscaContratuaisAnalise', `comboContratual${linhaComparacao}`, numeroProtocoloRequisicao);
        carregarComboComDados(service, 'buscaSemelhantesAnalise', `comboSemelhante${linhaComparacao}`, numeroProtocoloRequisicao);
        carregarComboComDados(service, 'buscaReferenciasAnalise', `comboReferencia${linhaComparacao}`, numeroProtocoloRequisicao);
    }
}

function carregarComboComDados(service, metodo, selectId, numeroProtocoloRequisicao) {
    const obterDados = (numeroProtocoloRequisicao) => {
        return service[metodo](numeroProtocoloRequisicao)
            .then(data => (Array.isArray(data) && data.length > 0 ? data : null))
            .catch(error => {
                console.error(`Erro ao buscar dados com o método ${metodo}: `, error);
                return null;
            });
    };

    const preencherCombo = (dados) => {
        const select = $(`#${selectId}`);
        select.empty();

        if (dados) {
            dados.forEach((item, index) => {                
                const option = `<option value="${index}">${item}</option>`;
                select.append(option);
            });
        }
    };

    obterDados(numeroProtocoloRequisicao).then(data => preencherCombo(data));
}

function limparDadosComparacao() {
    $("tbody[id^='dt']").each(function () {
        $(this).find("td").text("").css("background-color", "");
    });

    $("#secaoComparacao select").each(function () {
        $(this).val("");
    });

    $('#ckEstornoLei').prop('checked', false);
    $('#ckRecomposta').prop('checked', false);

    $('#tdAtualizacaoAtual').text('');
    $('#tdAtualizacaoAnterior').text('');
    $('#tdSomaDosValoresAtualizados').text('');
}

function calcularSomaValoresAtualizados(valorAtual, valorAnterior) {    

    $('#tdSomaDosValoresAtualizados').text('');
    
    const valorAtualStr = (valorAtual ?? '0,00').toString();
    const valorAnteriorStr = (valorAnterior ?? '0,00').toString();
    
    const valorAtualLimpo = valorAtualStr.replace(/\./g, '').replace(',', '.');
    const valorAnteriorLimpo = valorAnteriorStr.replace(/\./g, '').replace(',', '.');

    const valorAtualConvertido = parseFloat(valorAtualLimpo) || 0;
    const valorAnteriorConvertido = parseFloat(valorAnteriorLimpo) || 0;    

    const soma = valorAtualConvertido + valorAnteriorConvertido;

    const valorFormatado = (Math.round(soma * 100) / 100).toFixed(2);

    const valorComPontoEMilhares = formatarValorMonetario(valorFormatado);

    $('#tdSomaDosValoresAtualizados').text(valorComPontoEMilhares);
}

function verificaValoresEmBranco(valorAtual, valorAnterior) {
    if (!valorAtual) {
        $('#tdAtualizacaoAtual').text('0,00');
    }

    if (!valorAnterior) {
        $('#tdAtualizacaoAnterior').text('0,00');
    }
}