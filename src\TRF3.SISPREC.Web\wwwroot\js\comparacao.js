
/**
 * Executa a comparação de valores entre elementos HTML e adiciona uma classe se os valores forem iguais.
 * 
 * @param {string} seletor - Seletor para buscar os elementos na DOM (ex: '[id^="td"], select[id^="combo"]').
 * @param {string} classeAdicionar - Classe CSS a ser adicionada quando os valores forem iguais (ex: "igual").
 * @param {string} estilizarPersonalizado - Habilitar estilos e regras personalizadas para cores de campos e linhas.
 */
function executarComparacao(seletor, classeAdicionar, estilizarPersonalizado = false) {
    const serviceComparar = tRF3.sISPREC.analiseCpfCnpj.analiseCpfCnpj;
    // Remove a classe de todos os elementos antes de iniciar a comparação
    document.querySelectorAll(`.${classeAdicionar}`).forEach(el => el.classList.remove(classeAdicionar));

    // Captura os elementos com base no seletor fornecido
    const valores = [...document.querySelectorAll(seletor)].map(el => {
        const linha = el.closest('tr');
        const isPrincipal = linha?.className.includes('Principal');
        const isComparada = linha?.className.includes('Comparada');

        return {
            id: el.id,
            valor: el.tagName === "OPTION" ? el.textContent.trim() : el.textContent.trim() || el.value,
            elemento: el,
            isPrincipal,
            isComparada
        };
    });

    const idMap = new Map(); // Mapa para armazenar os valores já encontrados    
    valores.forEach(item => {
        if (!item.valor) return;

        if (idMap.has(item.id)) {
            const existente = idMap.get(item.id);
            abp.ui.block({ elm: 'body', busy: true });
            serviceComparar.compararNomes(existente.valor, item.valor)
                .then(iguais => {
                    if (!estilizarPersonalizado) {
                        if (iguais) {
                            item.elemento.classList.add(classeAdicionar);
                        }
                    } else {

                        aplicarEstilosPersonalizados(item, existente, iguais);
                    }
                })
                .catch(() => {
                    abp.message.error('Ocorreu um erro ao tentar comparar os dados.');
                })
                .always(() => {
                    abp.ui.unblock({ elm: 'body' });
                });
        } else {
            idMap.set(item.id, item); // Armazena o primeiro valor do ID
        }
    });
}

function aplicarEstilosPersonalizados(item, existente, iguais) {
    const principal = item.isPrincipal ? item : (existente.isPrincipal ? existente : null);
    const comparado = item.isComparada ? item : (existente.isComparada ? existente : null);

    let tdNomeBeneficiario = document.getElementById("tdNomeBeneficiario");
    let tdCpfCnpjBeneficiario = document.getElementById("tdCpfCnpjBeneficiario");
    let tdValorFormatadoCnpjBeneficiario = document.getElementById("tdValorFormatadoCnpjBeneficiario");
    let tdDataContaFormatadoCnpjBeneficiario = document.getElementById("tdDataContaFormatadoCnpjBeneficiario");

    const cores = {
        iguais: "#d0fdd7",
        diferentes: {
            tdProcedimento: "#ff9ea2",
            default: "#ffe3a1"
        }
    };

    const camposPrincipais = ["tdNatureza", "tdParteAutora", "tdCodigoTipoIndicadorEconomico", "tdValor", "tdProcedimento", "tdDataConta"];

    if (principal && camposPrincipais.includes(item.id)) {
        const cor = iguais
            ? cores.iguais
            : (item.id === "tdProcedimento" ? cores.diferentes.tdProcedimento : cores.diferentes.default);        

        if (item.id != "tdValor" && item.id != "tdDataConta") {
            if (principal.elemento) {
                principal.elemento.style.backgroundColor = cor;
                principal.elemento.style.fontWeight = "bold";
            }
        }


        if (item.id === "tdValor") {            
            aplicarDestaqueSeDiferente(tdValorFormatadoCnpjBeneficiario, principal.valor, cor, cores.iguais);
        }

        if (item.id === "tdDataConta") {
            aplicarDestaqueSeDiferente(tdDataContaFormatadoCnpjBeneficiario, principal.valor, cor, cores.iguais);
        }

    }

    const nomeBeneficiario = tdNomeBeneficiario ? tdNomeBeneficiario.textContent.trim(): "";
    const cpfCnpjBeneficiario = tdCpfCnpjBeneficiario ? tdCpfCnpjBeneficiario.textContent.trim() : "";

    const comparacoes = {
        tdRequerente: nomeBeneficiario,
        tdCpfCnpjRequerente: cpfCnpjBeneficiario
    };

    if (comparado && comparacoes[item.id] !== undefined) {
        const cor = comparacoes[item.id] === item.valor ? cores.iguais : cores.diferentes.default;

        if (comparado.elemento && comparado.elemento.id !== 'tdRequerente' && comparado.elemento.id !== 'tdCpfCnpjRequerente') {
            comparado.elemento.style.backgroundColor = cor;
            comparado.elemento.style.fontWeight = "bold";
        }

        if (tdNomeBeneficiario && comparado.elemento.id === 'tdRequerente') {
            tdNomeBeneficiario.style.backgroundColor = cor;
            tdNomeBeneficiario.style.fontWeight = "bold";
        }

        if (tdCpfCnpjBeneficiario && comparado.elemento.id === 'tdCpfCnpjRequerente') {
            tdCpfCnpjBeneficiario.style.backgroundColor = cor;
            tdCpfCnpjBeneficiario.style.fontWeight = "bold";
        }
    }

}


function aplicarDestaqueSeDiferente(elemento, valorComparacao, corDiferente, corIgual) {
    if (!elemento) return;

    const texto = elemento.textContent.trim();

    const cores = {
        diferente: corDiferente,
        igual: corIgual
    };

    if (texto !== valorComparacao) {
        elemento.style.backgroundColor = cores.diferente;
    } else {
        elemento.style.backgroundColor = cores.igual;
    }

    elemento.style.fontWeight = "bold";
}



