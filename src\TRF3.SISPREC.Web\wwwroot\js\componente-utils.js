function configuratInputParaMes(inputId, iniciarVazio = false) {
    const input = document.getElementById(inputId);

    if (iniciarVazio) {
        input.value = "";
    }

    if (!input) {
        console.warn(`Elemento com ID ${inputId} não encontrado.`);
        return;
    }

    input.addEventListener('input', function () {
        if (this.value === "") {
            return; 
        }

        if (this.value < 1) {
            this.value = 1;
        } else if (this.value > 12) {
            this.value = 12;
        }
    });
}

function configurarFiltroProcedimentoAnoMes(inputProcedimentoId, inputAnoId, inputMesId, primeiraInicializacao = true) {
    // Configuração do input Ano para permitir apenas números.
    configuraInputParaNumerico(inputAnoId);

    const inputProcedimento = document.getElementById(inputProcedimentoId);
    const inputAno = document.getElementById(inputAnoId);
    const inputMes = document.getElementById(inputMesId);

    let mesAtual = new Date().getMonth() + 1; //+1 para pegar o mes atual
    let anoAtual = new Date().getFullYear();

    if (primeiraInicializacao) {
        inputProcedimento.value = "0";

        if (mesAtual == 12) {
            anoAtual++;
            mesAtual = 0;
        }

        inputAno.value = anoAtual;
        inputMes.value = mesAtual + 1;
    }

    inputProcedimento.addEventListener('change', function () {
        const dataAtual = new Date();
        const dataReferencia = new Date(dataAtual.getFullYear(), 3, 2); // 02 de abril

        // PRC
        if (this.value == "1") {
            if (dataAtual <= dataReferencia) {
                inputAno.value = anoAtual + 1;
            }
            else {
                inputAno.value = anoAtual + 2;
            }

            inputMes.value = 1;
        }
        // RPV
        else {
            if (mesAtual == 12) {
                inputAno.value = anoAtual + 1;
                inputMes.value = 1;
            }
            else {
                inputAno.value = anoAtual;
                inputMes.value = mesAtual + 1;
            }
        }
    });
}

function configuraInputParaNumerico(inputId, iniciarVazio = false) {
    const input = document.getElementById(inputId);
    const regex = /[^0-9]/g; // Permitir apenas números

    if (!input) {
        console.warn(`Elemento com ID ${inputId} não encontrado.`);
        return;
    }

    if (iniciarVazio) {
        input.value = "";
    }

    input.addEventListener('input', function () {
        this.value = this.value.replace(regex, ''); // Remove caracteres não numéricos

        if (this.value === "") {
            return; // Permanece vazio se o usuário apagar tudo
        }

        const numero = parseInt(this.value, 10);
        if (isNaN(numero) || numero < 1) {
            this.value = "1"; // Garante que o valor mínimo seja 1
        }
    });
}

function aplicarCheckedEmTable(doc, idTable) {
    $(doc).ready(function () {
        $(idTable+' tbody').on('click', 'tr', function (e) {
            if (!$(e.target).is('.row-checkbox')) {
                const checkbox = $(this).find('.row-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked'));
            }
        });
    });
}

function marcarTodosCheckboxNaTabela(idTable, idButton) {
    $(idButton).click(function () {
        const checkbox = $(idTable).find('tbody .row-checkbox');
        checkbox.prop('checked', !checkbox.prop('checked'));
    })
}

/**
 * Dado uma tabela, seleciona a linha conforme o índice informado, deslocando o scroll para esta linha.
 * A tabela deve ser um data table com a extensão Select habilitada.
 * @param {any} $tabela Data Table.
 * @param {any} indice Índice da linha que se deseja selecionar.
 */
function selecionarLinhaTabela($tabela, indice) {
    let totalLinhas = $tabela.rows().count();

    if (totalLinhas > indice) {
        let row = $tabela.row(':eq(' + indice + ')');

        // Seleciona a linha.
        row.select();

        // Move o scroll para a linha selecionada.
        $tabela.context[0].nScrollBody.scrollTo(0, $(row.node()).position().top);
    }
}

$('.botao-navegacao-tabela').click(function () {
    let tipoNavegacao = $(this).data('tipo-navegacao');
    let idTabela = $(this).data('tabela');

    let $tabela = $('#' + idTabela).DataTable();

    // Obtém índice da linha atualmente selecionada.
    let linhaSelecionada = $tabela.row({ selected: true }).index();
    let indice = linhaSelecionada ?? 0;

    // Calcula índice da nova linha a ser selecionada.
    if (linhaSelecionada != undefined) {
        indice = tipoNavegacao == 'anterior' ? indice - 1 : indice + 1;
    }
    selecionarLinhaTabela($tabela, indice);
});