using NSubstitute;
using Shouldly;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.AnalisePrevencoes;
using TRF3.SISPREC.Analises.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications;

public class AnalisePrevencoesAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IAnalisePrevencoesRepository _analisePrevencoesRepository;
    private readonly AnalisePrevencoesAppService _analisePrevencoesAppService;

    public AnalisePrevencoesAppServiceTests()
    {
        _analisePrevencoesRepository = Substitute.For<IAnalisePrevencoesRepository>();
        _analisePrevencoesAppService = new AnalisePrevencoesAppService(_analisePrevencoesRepository);
    }

    [Fact]
    public async Task GetRequisicoes_Deve_Passar()
    {
        //Arrange
        var getListInput = new AnaliseGetListInput
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
            Ano = 2021,
            Mes = 1,
            DataInicio = DateTime.Now,
            DataTermino = DateTime.Now.AddDays(1)
        };

        _analisePrevencoesRepository
            .ObterRequisicoes(
                Arg.Any<ETipoProcedimentoRequisicao>(),
                Arg.Any<int>(),
                Arg.Any<int>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>())
            .Returns([new() { NumeroProtocoloRequisicao = "1" }]);

        //Act
        var result = await _analisePrevencoesAppService.GetRequisicoes(getListInput);

        //Assert
        result.ShouldNotBeNull();
        await _analisePrevencoesRepository
            .Received(1)
            .ObterRequisicoes(
                Arg.Any<ETipoProcedimentoRequisicao>(),
                Arg.Any<int>(),
                Arg.Any<int>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>());
    }

    [Fact]
    public async Task GetRequisicoes_Com_Numero_Requisicao_Deve_Passar()
    {
        //Arrange
        var getListInput = new AnaliseGetListInput
        {
            NumeroProtocoloRequisicao = "1",
            DataInicio = DateTime.Now,
            DataTermino = DateTime.Now.AddDays(1)
        };

        _analisePrevencoesRepository
            .ObterRequisicoes(
                Arg.Any<ETipoProcedimentoRequisicao?>(),
                Arg.Any<int?>(),
                Arg.Any<int?>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>())
            .Returns([new() { NumeroProtocoloRequisicao = "1" }]);

        //Act
        var result = await _analisePrevencoesAppService.GetRequisicoes(getListInput);

        //Assert
        result.ShouldNotBeNull();
        await _analisePrevencoesRepository
            .Received(1)
            .ObterRequisicoes(
                Arg.Any<ETipoProcedimentoRequisicao?>(),
                Arg.Any<int?>(),
                Arg.Any<int?>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>());
    }

    [Fact]
    public async Task GetPrevencoes_Deve_Passar()
    {
        //Arrange
        var numeroProtocoloRequisicao = "1";

        _analisePrevencoesRepository.ObterPrevencoes(Arg.Any<string>()).Returns(new List<Prevencao>()
        {
            new() {
                CodOcorrencia = 1,
                Descricao = "Descrição",
                NumeroProcessoOriginario = "123123123",
                RequisicaoAnterior = "123123123",
                CodTipoPrevencao = "Tipo 21"
            }
        });

        //Act
        var result = await _analisePrevencoesAppService.GetPrevencoes(numeroProtocoloRequisicao);

        //Assert
        result.ShouldNotBeNull();
        await _analisePrevencoesRepository.Received(1).ObterPrevencoes(Arg.Any<string>());
    }

    [Fact]
    public async Task GetPrevencoes_Sem_Requisicao_Deve_Passar()
    {
        //Arrange
        var numeroProtocoloRequisicao = "";

        _analisePrevencoesRepository.ObterPrevencoes(Arg.Any<string>()).Returns(new List<Prevencao>());

        //Act
        var result = await _analisePrevencoesAppService.GetPrevencoes(numeroProtocoloRequisicao);

        //Assert
        result.ShouldNotBeNull();
        await _analisePrevencoesRepository.Received(0).ObterPrevencoes(Arg.Any<string>());
    }

    [Fact]
    public void Deve_Retornar_Erro_Quando_Ano_E_Requisicao_Estiverem_Nulos()
    {
        // Arrange
        var model = new AnaliseGetListInput
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
            Ano = null,
            Mes = 1,
            NumeroProtocoloRequisicao = null
        };

        // Act
        var result = ValidateModel(model);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.Contains(result, r => r.ErrorMessage.Contains("Preencha os campos"));
    }

    [Fact]
    public void Deve_Passar_Quando_Ano_Estiver_Preenchido()
    {
        var model = new AnaliseGetListInput
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
            Ano = 2024,
            Mes = 1,
            NumeroProtocoloRequisicao = null
        };

        var result = ValidateModel(model);

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void Deve_Passar_Quando_NumeroProtocoloRequisicao_Estiver_Preenchido()
    {
        var model = new AnaliseGetListInput
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
            Ano = 0,
            Mes = 1,
            NumeroProtocoloRequisicao = "12345"
        };

        var result = ValidateModel(model);

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void Deve_Retornar_Erro_Quando_Ano_For_Nulo_E_NumeroProtocoloRequisicao_Vazio()
    {
        var model = new AnaliseGetListInput
        {
            TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
            Ano = null,
            Mes = 1,
            NumeroProtocoloRequisicao = string.Empty
        };

        var result = ValidateModel(model);

        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.Contains(result, r => r.ErrorMessage.Contains("Preencha os campos"));
    }

    private List<ValidationResult> ValidateModel(AnaliseGetListInput model)
    {
        var validationContext = new ValidationContext(model);
        var results = new List<ValidationResult>();

        Validator.TryValidateObject(model, validationContext, results, true);

        results.AddRange(model.Validate(validationContext));

        return results;
    }
}
