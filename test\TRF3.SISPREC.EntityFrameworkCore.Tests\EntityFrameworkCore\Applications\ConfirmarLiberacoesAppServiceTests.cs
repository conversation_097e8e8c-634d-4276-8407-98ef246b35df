using NSubstitute;
using Shouldly;
using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.ConfirmarLiberacoes;
using TRF3.SISPREC.ConfirmarLiberacoes.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class ConfirmarLiberacoesAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private readonly IConfirmarLiberacaoRepository _confirmarLiberacoesRepository;
        private readonly ConfirmarLiberacoesAppService _confirmarLiberacoesAppService;

        public ConfirmarLiberacoesAppServiceTests()
        {
            _confirmarLiberacoesRepository = Substitute.For<IConfirmarLiberacaoRepository>();
            _confirmarLiberacoesAppService = new ConfirmarLiberacoesAppService(_confirmarLiberacoesRepository);
        }


        [Fact]
        public async Task GetRequisicoes_Deve_Passar()
        {
            //Arrange
            var getListInput = new ConfirmarcaoLiberacoesGetListInput
            {
                TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
                Ano = 2021,
                Mes = 1,
                DataInicio = DateTime.Now,
                DataTermino = DateTime.Now.AddDays(1)
            };

            _confirmarLiberacoesRepository.BuscarRequisicoesPendentesParaLiberacao(
                                                          Arg.Any<ETipoProcedimentoRequisicao>(),
                                                          Arg.Any<int>(),
                                                          Arg.Any<int>(),
                                                          Arg.Any<DateTime>(),
                                                          Arg.Any<DateTime>(),
                                                          Arg.Any<string>()).Returns(new List<RequisicoesPendentes>()
            {
            new RequisicoesPendentes
            {
                NumeroProtocoloRequisicaoPendente = "1",
            }
            });

            //Act
            var result = await _confirmarLiberacoesAppService.GetRequisicoes(getListInput);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacoesRepository.Received(1).BuscarRequisicoesPendentesParaLiberacao(
                                                                            Arg.Any<ETipoProcedimentoRequisicao>(),
                                                                            Arg.Any<int>(),
                                                                            Arg.Any<int>(),
                                                                            Arg.Any<DateTime>(),
                                                                            Arg.Any<DateTime>(),
                                                                            Arg.Any<string>());
        }

        [Fact]
        public async Task GetRequisicoes_Nao_Deve_Passar()
        {
            //Arrange
            var getListInput = new ConfirmarcaoLiberacoesGetListInput
            {
                TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
                Ano = 2021,
                Mes = 1,
                DataInicio = DateTime.Now,
                DataTermino = DateTime.Now.AddDays(1)
            };

            _confirmarLiberacoesRepository.BuscarRequisicoesPendentesParaLiberacao(
                                                          Arg.Any<ETipoProcedimentoRequisicao>(),
                                                          Arg.Any<int>(),
                                                          Arg.Any<int>(),
                                                          Arg.Any<DateTime>(),
                                                          Arg.Any<DateTime>(),
                                                          Arg.Any<string>()).Returns(new List<RequisicoesPendentes>());

            //Act
            var result = await _confirmarLiberacoesAppService.GetRequisicoes(getListInput);

            //Assert
            result.Items.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetInformacaoBasica_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacoesRepository.BuscaPorInformacaoBasicaRequisicoes(Arg.Any<string>()).Returns(
                new DadosBasicoParaConfirmarLiberacao
                {
                    AnoPropos = 1,
                    MesPropos = 9,
                    TipoProcedimento = ETipoProcedimentoRequisicao.PRC.ToString(),
                    NumeroProtocoloRequisicao = numeroProtocolo,
                    SituacaoProposta = "ATIVO",
                    SituacaoRequisicao = "PAGO TOTAL - Informado ao Juizo"
                });

            //Act
            var result = await _confirmarLiberacoesAppService.GetInformacaoBasica(numeroProtocolo);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacoesRepository.Received(1).BuscaPorInformacaoBasicaRequisicoes(Arg.Any<string>());
        }

        [Fact]
        public async Task GetInformacaoBasica_Nao_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacoesRepository.BuscaPorInformacaoBasicaRequisicoes(Arg.Any<string>()).Returns((DadosBasicoParaConfirmarLiberacao)null);

            //Act
            var result = await _confirmarLiberacoesAppService.GetInformacaoBasica(numeroProtocolo);

            //Assert
            result.ShouldBeNull();
        }

        [Fact]
        public async Task GetOcorrencias_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacoesRepository.BuscaPorOcorrenciasRequisicoes(Arg.Any<string>())
                .Returns(new List<DadosOcorrenciasParaConfirmarLiberacao>()
                {
                    new DadosOcorrenciasParaConfirmarLiberacao
                    {
                        DataOCorrencia = DateTime.Now,
                        TipoOcorrencia = "Órgão PSS",
                        Descricao = "Teste "
                    }
                });

            //Act
            var result = await _confirmarLiberacoesAppService.GetOcorrencias(numeroProtocolo);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacoesRepository.Received(1).BuscaPorOcorrenciasRequisicoes(Arg.Any<string>());
        }

        [Fact]
        public async Task GetOcorrencias_Nao_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacoesRepository.BuscaPorOcorrenciasRequisicoes(Arg.Any<string>()).Returns((new List<DadosOcorrenciasParaConfirmarLiberacao>()));

            //Act
            var result = await _confirmarLiberacoesAppService.GetOcorrencias(numeroProtocolo);

            //Assert
            result.Items.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetJustificastivaAnalises_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacoesRepository.BuscaPorJustificativasAnalises(
                                                          Arg.Any<string>()).Returns(new List<JustificativasAnalises>()
            {
            new JustificativasAnalises
            {
                 DataAnalise = DateTime.Now,
                 ComplementoJustificativa = "Complemento Justificativa",
                 DescricaoJustificativa = "Descrição Justificativa Análise",
                 UsuarioAnalise = "usuarioTeste",
                 TipoAcao = "CANCELAMENTO",
                 DescricaoTela = "Pendências"
            }
            });

            //Act
            var result = await _confirmarLiberacoesAppService.GetJustificastivaAnalises(numeroProtocolo);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacoesRepository.Received(1).BuscaPorJustificativasAnalises(Arg.Any<string>());
        }

        [Fact]
        public async Task GetJustificastivaAnalises_Nao_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacoesRepository.BuscaPorJustificativasAnalises(Arg.Any<string>()).Returns((new List<JustificativasAnalises>()));

            //Act
            var result = await _confirmarLiberacoesAppService.GetJustificastivaAnalises(numeroProtocolo);

            //Assert
            result.Items.Count.ShouldBe(0);
        }

    }
}
