using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.MotivosExpedientesAdministrativos;
using TRF3.SISPREC.MotivosExpedientesAdministrativos.Dtos;
using TRF3.SISPREC.MotivosExpedientesAdministrativos.Servicos;
using Volo.Abp;
using Volo.Abp.Users;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class MotivoExpedienteAdministrativoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private readonly IMotivoExpedienteAdministrativoAppService _appService;
        private readonly IMotivoExpedienteAdministrativoRepository _repository;
        private readonly IMotivoExpedienteAdministrativoAppService _appServiceMock;
        private readonly IMotivoExpedienteAdministrativoRepository _repositoryMock;
        private MotivoExpedienteAdministrativo motivoObj1;
        private MotivoExpedienteAdministrativo motivoObj2;

        public MotivoExpedienteAdministrativoAppServiceTests()
        {
            _appService = GetRequiredService<IMotivoExpedienteAdministrativoAppService>();
            _repository = GetRequiredService<IMotivoExpedienteAdministrativoRepository>();

            var fakerMotivos = new Bogus.Faker<MotivoExpedienteAdministrativo>()
                .RuleFor(p => p.ExpedienteAdministrativoMotivoId, p => p.Random.Int(1, 5))
                .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash())
                .Generate(2);

            motivoObj1 = fakerMotivos[0];
            motivoObj2 = fakerMotivos[1];

            _repository.InsertAsync(motivoObj1, true);
            _repository.InsertAsync(motivoObj2, true);

            _appServiceMock = Substitute.For<IMotivoExpedienteAdministrativoAppService>();
            _repositoryMock = Substitute.For<IMotivoExpedienteAdministrativoRepository>();
        }

        protected override void AfterAddApplication(IServiceCollection services)
        {
            var currentUser = Substitute.For<ICurrentUser>();
            var webHostEnvironment = Substitute.For<IWebHostEnvironment>();

            var currentUserServiceDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(ICurrentUser));
            var webHostEnvironmentServiceDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IWebHostEnvironment));

            if (currentUserServiceDescriptor != null)
            {
                services.Remove(currentUserServiceDescriptor);
                services.AddSingleton(currentUser);
            }

            if (webHostEnvironmentServiceDescriptor != null)
            {
                services.Remove(webHostEnvironmentServiceDescriptor);
                services.AddSingleton(webHostEnvironment);
            }

            base.AfterAddApplication(services);
        }

        [Fact]
        public async Task Criar_MotivoExpediente_Deve_Passar()
        {
            var input = new Bogus.Faker<CreateUpdateMotivoExpedienteAdministrativoDto>()
                .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(5))
                .Generate();

            var result = await _appService.CreateAsync(input);

            result.ShouldNotBeNull();
            result.ExpedienteAdministrativoMotivoId.ShouldBeGreaterThan(0);
            result.DescricaoMotivo.ShouldBe(input.DescricaoMotivo);
        }

        [Fact]
        public async Task Atualizar_MotivoExpediente_Deve_Passar()
        {
            var input = new Bogus.Faker<CreateUpdateMotivoExpedienteAdministrativoDto>()
                .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(5))
                .Generate();

            var result = await _appService.UpdateAsync(motivoObj1.ExpedienteAdministrativoMotivoId, input);

            result.ShouldNotBeNull();
        }

        [Fact]
        public async Task Atualizar_MotivoExpediente_Deve_Lancar_Excecao()
        {
            var input = new Bogus.Faker<CreateUpdateMotivoExpedienteAdministrativoDto>()
                .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(5))
                .Generate();

            await Task.Run(() =>
            {
                Should.Throw<UserFriendlyException>(_appService.UpdateAsync(2000, input));
            });
        }

        [Fact]
        public async Task Excluir_MotivoExpediente_Deve_Passar()
        {
            var objetoParaExcluir = new Bogus.Faker<MotivoExpedienteAdministrativo>()
                .RuleFor(p => p.ExpedienteAdministrativoMotivoId, p => p.Random.Int(min: 1))
                .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(5))
                .RuleFor(p => p.IsDeleted, false)
                .Generate();

            await _repository.InsertAsync(objetoParaExcluir, autoSave: true);

            await _appService.DeleteAsync(objetoParaExcluir.ExpedienteAdministrativoMotivoId);

            var objetoDeletado = await _repository.FindAsync(a => a.ExpedienteAdministrativoMotivoId == objetoParaExcluir.ExpedienteAdministrativoMotivoId);
            objetoDeletado.ShouldBeNull();
        }

        [Fact]
        public async Task Obter_MotivoExpediente_Por_Id_Deve_Passar()
        {
            var objetoExistente = _repositoryMock.FindAsync(a => a.ExpedienteAdministrativoMotivoId == motivoObj1.ExpedienteAdministrativoMotivoId).Returns(motivoObj1);

            var result = _appServiceMock.GetAsync(motivoObj1.ExpedienteAdministrativoMotivoId).Returns(new MotivoExpedienteAdministrativoDto());

            result.ShouldNotBeNull();
        }

        [Fact]
        public async Task Obter_MotivoExpediente_Com_Ordenacao_Padrao_Deve_Passar()
        {
            var input = new MotivoExpedienteAdministrativoGetListInput();

            var result = await _appService.GetListAsync(input);

            result.ShouldNotBeNull();
        }


        [Fact]
        public async Task Obter_MotivoExpediente_Busca_Por_Id_Deve_Passar_V2()
        {
            var result = await _appService.BuscaPorId(motivoObj1.ExpedienteAdministrativoMotivoId);

            result.ShouldNotBeNull();
        }
        [Fact]
        public async Task GetAll_Deve_Retornar_Todos_Os_Motivos()
        {
            // Act
            var result = await _appService.ListarTodos();

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBeGreaterThanOrEqualTo(1);
            result.ShouldContain(m => m.ExpedienteAdministrativoMotivoId == motivoObj1.ExpedienteAdministrativoMotivoId && m.DescricaoMotivo == motivoObj1.DescricaoMotivo);
        }

        [Fact]
        public async Task GetAll_Deve_Retornar_Lista_Vazia_Quando_Repositorio_Retornar_Null()
        {
            // Arrange
            var repositoryMock = Substitute.For<IMotivoExpedienteAdministrativoRepository>();

            repositoryMock.GetQueryableAsync().Returns((IQueryable<MotivoExpedienteAdministrativo>)null);

            var service = new MotivoExpedienteAdministrativoAppService(repositoryMock, new MotivoExpedienteAdministrativoManager(repositoryMock));
            // Act
            var result = await service.ListarTodos();

            // Assert
            result.ShouldNotBeNull();
            result.ShouldBeEmpty();
        }

    }
}

