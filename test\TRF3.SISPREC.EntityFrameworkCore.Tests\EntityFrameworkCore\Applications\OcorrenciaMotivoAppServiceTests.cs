using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AnaliseTelas;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos.Dtos;
using TRF3.SISPREC.PDFServices;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Users;

namespace TRF3.SISPREC.OcorrenciaMotivos;


public class OcorrenciaMotivoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IOcorrenciaMotivoAppService _appService;
    private readonly IOcorrenciaMotivoAppService _appServiceMock;
    private readonly IOcorrenciaMotivoRepository _repository;
    private readonly IOcorrenciaMotivoRepository _repositoryMock;
    private OcorrenciaMotivo ocorrenciaMotivoObj1;
    private OcorrenciaMotivo ocorrenciaMotivoObj2;
    private AcaoTipo acaoTipoObj1;
    private AcaoTipo acaoTipoObj2;
    private AnaliseTela analiseTelaObj1;

    public OcorrenciaMotivoAppServiceTests()
    {
        _appService = GetRequiredService<IOcorrenciaMotivoAppService>();
        _repository = GetRequiredService<IOcorrenciaMotivoRepository>();

        var fakersAcao = new Bogus.Faker<AcaoTipo>()
        .RuleFor(p => p.AcaoTipoId, p => p.Random.Int(1, 5))
        .RuleFor(p => p.Descricao, p => p.PickRandom<EDescricaoAcaoTipo>())
        .RuleFor(p => p.Codigo, p => p.PickRandom<ECodigoAcaoTipo>()).Generate(2);

        acaoTipoObj1 = fakersAcao[0];
        acaoTipoObj2 = fakersAcao[1];

        var fakersAnaliseTela = new Bogus.Faker<AnaliseTela>()
        .RuleFor(p => p.AnaliseTelaId, p => p.Random.Int(1, 5))
        .RuleFor(p => p.Descricao, p => p.PickRandom<EDescricaoAnaliseTela>())
        .Generate(1);

        analiseTelaObj1 = fakersAnaliseTela[0];

        var fakersMotivo = new Bogus.Faker<OcorrenciaMotivo>()
            .RuleFor(p => p.OcorrenciaMotivoId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.CodigoMotivo, p => p.Random.Int(5))
            .RuleFor(p => p.AcaoTipoId, p => p.Random.Int())
            .RuleFor(p => p.AnaliseTelaId, p => p.Random.Int())
            .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(10))
            .RuleFor(p => p.Ativo, true)
            .RuleFor(p => p.IsDeleted, false)
            .RuleFor(p => p.AcaoTipo, acaoTipoObj1)
            .RuleFor(p => p.AnaliseTela, analiseTelaObj1)
            .Generate(2);

        ocorrenciaMotivoObj1 = fakersMotivo[0];
        ocorrenciaMotivoObj2 = fakersMotivo[1];

        _repository.InsertAsync(ocorrenciaMotivoObj1, true);
        _repository.InsertAsync(ocorrenciaMotivoObj2, true);

        _appServiceMock = Substitute.For<IOcorrenciaMotivoAppService>();
        _repositoryMock = Substitute.For<IOcorrenciaMotivoRepository>();
    }

    protected override void AfterAddApplication(IServiceCollection services)
    {
        var pdfServiceService = Substitute.For<IPdfFileGeneratorService>();
        var currentUser = Substitute.For<ICurrentUser>();
        var webHostEnvironment = Substitute.For<IWebHostEnvironment>();

        var iPDFFileGeneratorServiceDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IPdfFileGeneratorService));
        var currentUserServiceDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(ICurrentUser));
        var webHostEnvironmentServiceDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IWebHostEnvironment));

        if (iPDFFileGeneratorServiceDescriptor != null)
        {
            services.Remove(iPDFFileGeneratorServiceDescriptor);
            services.AddSingleton(pdfServiceService);
        }

        if (currentUserServiceDescriptor != null)
        {
            services.Remove(currentUserServiceDescriptor);
            services.AddSingleton(currentUser);
        }

        if (webHostEnvironmentServiceDescriptor != null)
        {
            services.Remove(webHostEnvironmentServiceDescriptor);
            services.AddSingleton(webHostEnvironment);
        }

        base.AfterAddApplication(services);
    }

    [Fact]
    public async Task Criar_OcorrenciaMotivo_Deve_Passar()
    {
        // Arrange
        var input = new Bogus.Faker<CreateUpdateOcorrenciaMotivoDto>()
            .RuleFor(p => p.CodigoMotivo, p => p.Random.Int(0, int.MaxValue))
            .RuleFor(p => p.AcaoTipoId, acaoTipoObj1.AcaoTipoId)
            .RuleFor(p => p.AnaliseTelaId, analiseTelaObj1.AnaliseTelaId)
            .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(10))
            .RuleFor(p => p.Ativo, p => p.Random.Bool())
            .RuleFor(p => p.IsDeleted, false)
            .Generate();

        // Act
        var result = await _appService.InserirAsync(input);

        // Assert
        result.ShouldNotBeNull();
        result.OcorrenciaMotivoId.ShouldBeGreaterThan(0);
        result.CodigoMotivo.ShouldBe(input.CodigoMotivo);
        result.AcaoTipoId.ShouldBe(input.AcaoTipoId);
        result.AnaliseTelaId.ShouldBe(input.AnaliseTelaId);
        result.DescricaoMotivo.ShouldBe(input.DescricaoMotivo);
        result.Ativo.ShouldBe(input.Ativo);
        result.IsDeleted.ShouldBe(input.IsDeleted);
    }

    [Fact]
    public async Task Atualizar_OcorrenciaMotivo_Deve_Passar()
    {
        // Arrange
        var input = new Bogus.Faker<CreateUpdateOcorrenciaMotivoDto>()
            .RuleFor(p => p.AcaoTipoId, acaoTipoObj1.AcaoTipoId)
            .RuleFor(p => p.AnaliseTelaId, analiseTelaObj1.AnaliseTelaId)
            .RuleFor(p => p.CodigoMotivo, p => p.Random.Int(1, 5))
            .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(10))
            .RuleFor(p => p.Ativo, p => true)
            .RuleFor(p => p.IsDeleted, false)
            .Generate();

        // Act
        var result = _appServiceMock.UpdateAsync(ocorrenciaMotivoObj1.OcorrenciaMotivoId, input).Returns(new OcorrenciaMotivoDto());

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Excluir_OcorrenciaMotivo_Deve_Passar()
    {
        // Arrange
        var objetoParaExcluir = new Bogus.Faker<OcorrenciaMotivo>()
            .RuleFor(p => p.AcaoTipo, new Bogus.Faker<AcaoTipo>())
            .RuleFor(p => p.OcorrenciaMotivoId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.CodigoMotivo, p => p.Random.Int())
            .RuleFor(p => p.AcaoTipoId, p => p.Random.Int(1, 5))
            .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(10))
            .RuleFor(p => p.Ativo, p => p.Random.Bool())
            .RuleFor(p => p.IsDeleted, false)
            .Generate();

        objetoParaExcluir = await _repository.InsertAsync(objetoParaExcluir, autoSave: true);

        // Act
        await _appService.DeleteAsync(objetoParaExcluir.OcorrenciaMotivoId);

        // Assert
        var objetoDeletado = await _repository.FindAsync(a => a.OcorrenciaMotivoId == objetoParaExcluir.OcorrenciaMotivoId);
        objetoDeletado.ShouldBeNull();
    }

    [Fact]
    public async Task Obter_OcorrenciaMotivo_Por_Id_Deve_Passar()
    {
        // Arrange
        var objetoExistente = _repositoryMock.FindAsync(a => a.OcorrenciaMotivoId == ocorrenciaMotivoObj1.OcorrenciaMotivoId).Returns(ocorrenciaMotivoObj1);

        // Act
        var result = _appServiceMock.GetAsync(ocorrenciaMotivoObj1.OcorrenciaMotivoId).Returns(new OcorrenciaMotivoDto());

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Obter_OcorrenciaMotivo_Com_Ordenacao_Padrao_Deve_Passar()
    {
        // Arrange
        var input = new OcorrenciaMotivoGetListInput();  // Sem filtros específicos para testar a ordenação padrão.

        // Act
        var result = _appServiceMock.GetListAsync(input).Returns(new PagedResultDto<OcorrenciaMotivoDto>());

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Inserir_OcorrenciaMotivo_Deve_Lancar_Excecao()
    {
        var input = new Bogus.Faker<CreateUpdateOcorrenciaMotivoDto>()
            .RuleFor(p => p.AcaoTipoId, acaoTipoObj1.AcaoTipoId)
            .RuleFor(p => p.AnaliseTelaId, analiseTelaObj1.AnaliseTelaId)
            .RuleFor(p => p.CodigoMotivo, p => p.Random.Int(0, int.MaxValue))
            .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(10))
            .RuleFor(p => p.Ativo, p => p.Random.Bool())
            .RuleFor(p => p.IsDeleted, false)
            .Generate();

        await _appService.InserirAsync(input);

        Should.Throw<UserFriendlyException>(_appService.InserirAsync(input));
    }

    [Fact]
    public async Task Atualizar_OcorrenciaMotivo_Deve_Lancar_Excecao()
    {
        var input = new Bogus.Faker<CreateUpdateOcorrenciaMotivoDto>()
            .RuleFor(p => p.CodigoMotivo, p => p.Random.Int(0, int.MaxValue))
            .RuleFor(p => p.AcaoTipoId, acaoTipoObj1.AcaoTipoId)
            .RuleFor(p => p.DescricaoMotivo, p => p.Random.Hash(5))
            .RuleFor(p => p.Ativo, p => p.Random.Bool())
            .RuleFor(p => p.IsDeleted, p => false)
            .Generate();

        await Task.Run(() =>
        {
            Should.Throw<UserFriendlyException>(_appService.UpdateAsync(2000, input));
        });
    }
}

