using Shouldly;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using Volo.Abp;
using Volo.Abp.Data;

namespace TRF3.SISPREC.RequisicoesProtocolos.Tests;


public class RequisicaoProtocoloAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IDataFilter<ISoftDelete> _softDeleteFilter;
    private readonly IRequisicaoProtocoloAppService _appService;
    private readonly IRequisicaoProtocoloRepository _requisicaoProtocoloRepository;
    private readonly IRequisicaoProtocoloManager _manager;
    private RequisicaoProtocolo _objetoExistente;
    private RequisicaoProtocolo _objetoExcluir;

    public RequisicaoProtocoloAppServiceTests()
    {
        _softDeleteFilter = GetRequiredService<IDataFilter<ISoftDelete>>();
        _requisicaoProtocoloRepository = GetRequiredService<IRequisicaoProtocoloRepository>();
        _manager = GetRequiredService<IRequisicaoProtocoloManager>();
        _appService = GetRequiredService<IRequisicaoProtocoloAppService>();
        _requisicaoProtocoloRepository = GetRequiredService<IRequisicaoProtocoloRepository>();
        WithUnitOfWorkAsync(async () =>
        {
            _objetoExistente = RequisicaoTesteHelper.CriarRequisicaoProtocolo(ServiceProvider);
            _objetoExcluir = RequisicaoTesteHelper.CriarRequisicaoProtocolo(ServiceProvider);
        });
    }

    [Fact]
    public async Task Criar_RequisicaoProtocolo_Deve_Lancar_Excecao()
    {
        // Arrange
        var input = new Bogus.Faker<CreateUpdateRequisicaoProtocoloDto>("pt_BR")
               .RuleFor(p => p.NumeroProtocoloRequisicao, p => p.Random.Hash(11)) // CHAR(11)
               .RuleFor(p => p.TipoHonorario, p => p.PickRandom<ETipoHonorario>())
               .RuleFor(p => p.RenunciaValorLimite, p => p.Random.Bool())
               .Generate();

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            await _appService.CreateAsync(input);
        });
    }


    [Fact]
    public async Task Atualizar_RequisicaoProtocolo_Deve_Passar()
    {
        // Arrange
        var objetoOriginal = _objetoExistente;
        var input = new Bogus.Faker<CreateUpdateRequisicaoProtocoloDto>("pt_BR")
        .RuleFor(p => p.AssuntoId, objetoOriginal.AssuntoId)
        .RuleFor(p => p.TipoProcedimentoId, objetoOriginal.TipoProcedimentoId) // CHAR(3)
        .RuleFor(p => p.SituacaoRequisicaoId, objetoOriginal.SituacaoRequisicaoId)
        .RuleFor(p => p.NumeroProtocoloRequisicao, objetoOriginal.NumeroProtocoloRequisicao)

        .RuleFor(p => p.TipoHonorario, p => p.PickRandom<ETipoHonorario>())
        .RuleFor(p => p.RenunciaValorLimite, p => p.Random.Bool())
        .RuleFor(p => p.ValorRequisicao, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.DataContaLiquidacao, p => p.Date.Past()) // DATE
        .RuleFor(p => p.TipoRequisicao, p => p.PickRandom<ETipoRequisicao>())
        .RuleFor(p => p.ValorConta, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.DataConta, p => p.Date.Past()) // DATE
        .RuleFor(p => p.DataTransitoJulgadoFase, p => p.Date.Past()) // DATE
        .RuleFor(p => p.DataTransitoJulgadoEmbargos, p => p.Date.Past()) // DATE
        .RuleFor(p => p.NomeMagistrado, p => p.Random.Hash(20)) // VARCHAR(60)
        .RuleFor(p => p.DesignacaoMagistrado, p => p.PickRandom<EDesignacaoMagistrado>())
        .RuleFor(p => p.NaturezaCredito, p => p.PickRandom<ENaturezaCredito>())
        .RuleFor(p => p.DesapropriacaoUnicoImovel, p => p.Random.Bool())
        .RuleFor(p => p.ValorAtualizadoRequisicao, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.ExecucaoFiscal, p => p.Random.Bool())
        .RuleFor(p => p.ValorCompensacao, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.BloqueioDepositoJudicial, p => p.Random.Bool())
        .RuleFor(p => p.LevantamentoOrdemJuizo, p => p.Random.Bool())
        .RuleFor(p => p.DataTransitoDeferimentoCompensacao, p => p.Date.Past()) // DATE
        .RuleFor(p => p.ValorCompensacaoAtualizado, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(18,4)
        .RuleFor(p => p.ValorRequisicaoPrincipal, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorRequisicaoJuros, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorContaPrincipal, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorContaJuros, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorAtualizadoRequisicaoPrincipal, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.ValorAtualizadoRequisicaoJuros, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.Selic, p => p.Random.Bool())
        .RuleFor(p => p.ValorTotalReferencia, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
        .RuleFor(p => p.IndicadorJurosMora, p => p.PickRandom<EIndicadorJurosMora>())
        .RuleFor(p => p.ValorAliquotaJurosMora, p => p.Random.Decimal(0, 999999.9999m)) // DECIMAL(6,4)
        .RuleFor(p => p.ValorJurosMora, p => p.Random.Decimal(0, ************.99m)) // DECIMAL(14,2)
            .Generate();
        input.DataConta = DateTime.Now;

        // Act
        var result = await _appService.UpdateAsync(objetoOriginal.NumeroProtocoloRequisicao, input);

        // Assert
        result.ShouldNotBeNull();
        result.NumeroProtocoloRequisicao.Length.ShouldBe(11);
        result.AssuntoId.ShouldBe(input.AssuntoId);
        result.TipoProcedimentoId.ShouldBe(input.TipoProcedimentoId);
        result.TipoHonorario.ShouldBe(input.TipoHonorario);
        result.RenunciaValorLimite.ShouldBe(input.RenunciaValorLimite);
        result.ValorRequisicao.ShouldBe(input.ValorRequisicao);
        result.DataContaLiquidacao.ShouldBe(input.DataContaLiquidacao);
        result.TipoRequisicao.ShouldBe(input.TipoRequisicao);
        result.ValorConta.ShouldBe(input.ValorConta);
        result.DataConta.ShouldBe(input.DataConta);
        result.DataTransitoJulgadoFase.ShouldBe(input.DataTransitoJulgadoFase);
        result.DataTransitoJulgadoEmbargos.ShouldBe(input.DataTransitoJulgadoEmbargos);
        result.NomeMagistrado.ShouldBe(input.NomeMagistrado);
        result.DesignacaoMagistrado.ShouldBe(input.DesignacaoMagistrado);
        result.NaturezaCredito.ShouldBe(input.NaturezaCredito);
        result.DesapropriacaoUnicoImovel.ShouldBe(input.DesapropriacaoUnicoImovel);
        result.ValorAtualizadoRequisicao.ShouldBe(input.ValorAtualizadoRequisicao);
        result.SituacaoRequisicaoId.ShouldBe(input.SituacaoRequisicaoId);
        result.ExecucaoFiscal.ShouldBe(input.ExecucaoFiscal);
        result.ValorCompensacao.ShouldBe(input.ValorCompensacao);
        result.BloqueioDepositoJudicial.ShouldBe(input.BloqueioDepositoJudicial);
        result.LevantamentoOrdemJuizo.ShouldBe(input.LevantamentoOrdemJuizo);
        result.DataTransitoDeferimentoCompensacao.ShouldBe(input.DataTransitoDeferimentoCompensacao);
        result.ValorCompensacaoAtualizado.ShouldBe(input.ValorCompensacaoAtualizado);
        result.ValorRequisicaoPrincipal.ShouldBe(input.ValorRequisicaoPrincipal);
        result.ValorRequisicaoJuros.ShouldBe(input.ValorRequisicaoJuros);
        result.ValorContaPrincipal.ShouldBe(input.ValorContaPrincipal);
        result.ValorContaJuros.ShouldBe(input.ValorContaJuros);
        result.ValorAtualizadoRequisicaoPrincipal.ShouldBe(input.ValorAtualizadoRequisicaoPrincipal);
        result.ValorAtualizadoRequisicaoJuros.ShouldBe(input.ValorAtualizadoRequisicaoJuros);
        result.Selic.ShouldBe(input.Selic);
        result.ValorTotalReferencia.ShouldBe(input.ValorTotalReferencia);
        result.IndicadorJurosMora.ShouldBe(input.IndicadorJurosMora);
        result.ValorAliquotaJurosMora.ShouldBe(input.ValorAliquotaJurosMora);
        result.ValorJurosMora.ShouldBe(input.ValorJurosMora);
    }

    [Fact]
    public async Task Excluir_RequisicaoProtocolo_Pelo_Repositorio_Deve_Excluir_Logicamente()
    {
        // Arrange
        var objetoParaExcluir = _objetoExcluir;

        // Act
        await _requisicaoProtocoloRepository.DeleteAsync(r => r.NumeroProtocoloRequisicao == objetoParaExcluir.NumeroProtocoloRequisicao);

        // Assert
        //Temporary disable the ISoftDelete filter
        using (_softDeleteFilter.Disable())
        {
            var objetoDeletado = await _requisicaoProtocoloRepository.FindAsync(a => a.NumeroProtocoloRequisicao == objetoParaExcluir.NumeroProtocoloRequisicao);
            objetoDeletado.IsDeleted.ShouldBeTrue();
        }
    }

    [Fact]
    public async Task Excluir_RequisicaoProtocolo_Pelo_AppService_Deve_Lancar_Excecao()
    {
        // Arrange
        var objetoParaExcluir = _objetoExcluir;

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            await _appService.DeleteAsync(objetoParaExcluir.NumeroProtocoloRequisicao);
        });
    }

    [Fact]
    public async Task Obter_RequisicaoProtocolo_Por_Id_Deve_Passar()
    {
        // Arrange
        var objetoExistente = _objetoExistente;

        // Act
        var result = await _appService.GetAsync(objetoExistente.NumeroProtocoloRequisicao);

        // Assert
        result.ShouldNotBeNull();
        result.NumeroProtocoloRequisicao.ShouldBe(objetoExistente.NumeroProtocoloRequisicao);
    }

    [Fact]
    public async Task Obter_RequisicaoProtocolo_Com_Ordenacao_Padrao_Deve_Passar()
    {
        // Arrange
        var input = new RequisicaoProtocoloGetListInput();  // Sem filtros específicos para testar a ordenação padrão.

        // Act
        var result = await _appService.GetListAsync(input);

        // Assert
        result.Items.ShouldNotBeEmpty();
        var orderedItems = result.Items.OrderBy(l => l.NumeroProtocoloRequisicao).ToList();
        orderedItems.SequenceEqual(result.Items).ShouldBeTrue();
    }
}
